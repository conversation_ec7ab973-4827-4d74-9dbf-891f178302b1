/**
 * NCTraverserSimple.cs - 简化版NC遍历工具
 * 用于测试编译和基本功能
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using WindowsAccessBridgeInterop;

namespace NCTraverser
{
    class Program
    {
        private static AccessBridge accessBridge;
        private static string logFileName;

        [DllImport("user32.dll")]
        static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

        [DllImport("user32.dll")]
        static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        [DllImport("user32.dll")]
        static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        static void Main(string[] args)
        {
            logFileName = string.Format("NC_Elements_Simple_{0}.log", DateTime.Now.ToString("yyyyMMdd_HHmmss"));
            
            Console.WriteLine("=== NC简化版遍历工具 ===");
            Console.WriteLine("输出日志文件: " + logFileName);
            
            WriteLog("=== NC简化版遍历工具启动 ===");
            WriteLog("启动时间: " + DateTime.Now.ToString("yyyy/M/d H:mm:ss"));
            WriteLog("操作系统: " + Environment.OSVersion.ToString());
            WriteLog(".NET版本: " + Environment.Version.ToString());
            WriteLog("进程架构: " + (Environment.Is64BitProcess ? "64位" : "32位"));

            try
            {
                // 1. 检查Java进程
                WriteLog("\n=== 1. 检查Java进程 ===");
                CheckJavaProcesses();

                // 2. 检查Java窗口
                WriteLog("\n=== 2. 检查Java窗口 ===");
                CheckJavaWindows();

                // 3. 初始化Access Bridge
                WriteLog("\n=== 3. 初始化Access Bridge ===");
                if (InitializeAccessBridge())
                {
                    WriteLog("✅ Access Bridge初始化成功");
                    
                    // 4. 尝试获取JVM信息
                    WriteLog("\n=== 4. 获取JVM信息 ===");
                    TryGetJVMInfo();
                }
                else
                {
                    WriteLog("❌ Access Bridge初始化失败");
                }

                WriteLog("\n=== 工具运行完成 ===");
                Console.WriteLine("运行完成，详细信息请查看日志文件: " + logFileName);
            }
            catch (Exception ex)
            {
                WriteLog("发生异常: " + ex.ToString());
                Console.WriteLine("发生异常: " + ex.Message);
            }
            finally
            {
                if (accessBridge != null)
                {
                    try
                    {
                        accessBridge.Shutdown();
                        WriteLog("Access Bridge已关闭");
                    }
                    catch (Exception ex)
                    {
                        WriteLog("关闭Access Bridge时发生异常: " + ex.Message);
                    }
                }
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void CheckJavaProcesses()
        {
            var javaProcesses = new List<Process>();
            var allProcesses = Process.GetProcesses();
            
            WriteLog(string.Format("系统中运行的进程总数: {0}", allProcesses.Length));

            foreach (Process process in allProcesses)
            {
                try
                {
                    string processName = process.ProcessName.ToLower();
                    if (processName.Contains("java") || processName.Contains("javaw") || 
                        processName.Contains("nc") || processName.Contains("eclipse"))
                    {
                        javaProcesses.Add(process);
                        WriteLog(string.Format("发现疑似Java进程: {0} (PID: {1})", process.ProcessName, process.Id));
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(string.Format("检查进程时发生异常: {0}", ex.Message));
                }
            }

            WriteLog(string.Format("总共发现 {0} 个疑似Java进程", javaProcesses.Count));
        }

        static void CheckJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            EnumWindows((hWnd, lParam) =>
            {
                if (IsWindowVisible(hWnd))
                {
                    var sb = new StringBuilder(256);
                    GetWindowText(hWnd, sb, sb.Capacity);
                    string windowTitle = sb.ToString();

                    uint processId;
                    GetWindowThreadProcessId(hWnd, out processId);

                    try
                    {
                        Process process = Process.GetProcessById((int)processId);
                        string processName = process.ProcessName.ToLower();

                        // 检查是否为Java窗口
                        bool isJavaWindow = processName.Contains("java") || 
                                          processName.Contains("javaw") ||
                                          windowTitle.ToLower().Contains("nc") ||
                                          windowTitle.ToLower().Contains("java");

                        if (isJavaWindow)
                        {
                            javaWindows.Add(hWnd);
                            WriteLog(string.Format("发现Java窗口: {0} (HWND: {1}, PID: {2})", 
                                windowTitle, hWnd.ToString("X"), processId));
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("检查窗口时发生异常: {0}", ex.Message));
                    }
                }
                return true;
            }, IntPtr.Zero);

            WriteLog(string.Format("总共发现 {0} 个Java窗口", javaWindows.Count));
        }

        static bool InitializeAccessBridge()
        {
            try
            {
                WriteLog("正在初始化Access Bridge...");
                accessBridge = new AccessBridge();
                WriteLog("Access Bridge对象创建成功");
                return true;
            }
            catch (Exception ex)
            {
                WriteLog("Access Bridge初始化失败: " + ex.ToString());
                return false;
            }
        }

        static void TryGetJVMInfo()
        {
            try
            {
                var jvms = accessBridge.GetJavaVirtualMachines();
                WriteLog(string.Format("发现 {0} 个Java虚拟机", jvms.Length));

                foreach (var jvm in jvms)
                {
                    WriteLog(string.Format("JVM ID: {0}", jvm.JvmId));
                    
                    try
                    {
                        var windows = jvm.GetAccessibleWindows();
                        WriteLog(string.Format("JVM {0} 包含 {1} 个可访问窗口", jvm.JvmId, windows.Length));

                        foreach (var window in windows)
                        {
                            WriteLog(string.Format("窗口 HWND: {0}", window.Hwnd.ToString("X")));
                            
                            try
                            {
                                var info = window.AccessibleContextNode.GetInfo();
                                WriteLog(string.Format("  窗口名称: {0}", info.name));
                                WriteLog(string.Format("  窗口角色: {0}", info.role));
                                WriteLog(string.Format("  子元素数量: {0}", info.childrenCount));
                            }
                            catch (Exception ex)
                            {
                                WriteLog(string.Format("  获取窗口信息失败: {0}", ex.Message));
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("获取JVM窗口时发生异常: {0}", ex.Message));
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("获取JVM信息时发生异常: {0}", ex.Message));
            }
        }

        static void WriteLog(string message)
        {
            string logEntry = string.Format("[{0}] {1}", DateTime.Now.ToString("HH:mm:ss.fff"), message);
            Console.WriteLine(message);
            
            try
            {
                File.AppendAllText(logFileName, logEntry + Environment.NewLine, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Console.WriteLine("写入日志失败: " + ex.Message);
            }
        }
    }
} 