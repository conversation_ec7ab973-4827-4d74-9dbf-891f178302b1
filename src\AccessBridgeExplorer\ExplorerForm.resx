﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="mainMenuStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="statusBarStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>132, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="garbageCollectButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAIDSURBVDhPpZLrS5NhGMb3j4SWh0oRQVExD4gonkDpg4hG
        YKxG6WBogkMZKgPNCEVJFBGdGETEvgwyO9DJE5syZw3PIlPEE9pgBCLZ5XvdMB8Ew8gXbl54nuf63dd9
        0OGSnwCahxbPRNPAPMw9Xpg6ZmF46kZZ0xSKzJPIrhpDWsVnpBhGkKx3nAX8Pv7z1zg8OoY/cITdn4fw
        bf/C0kYAN3Ma/w3gWfZL5kzTKBxjWyK2DftwI9tyMYCZKXbNHaD91bLYJrDXsYbrWfUKwJrPE9M2M1Oc
        VzOOpHI7Jr376Hi9ogHqFIANO0/MmmmbmSmm9a8ze+I4MrNWAdjtoJgWcx+PSzg166yZZ8xM8XvXDix9
        c4jIqFYAjoriBV9AhEPv1mH/sonogha0afbZMMZz+yreTGyhpusHwtNNCsA5U1zS4BLxzJIfg299qO32
        Ir7UJtZfftyATqeT+8o2D8JSjQrAJblrncYL7ZJ2+bfaFnC/1S1NjL3diRat7qrO7wLRP3HjWsojBeCo
        mDEo5mNjuweFGvjWg2EBhCbpkW78htSHHwRyNdmgAFzPEee2iFkzayy2OLXzT4gr6UdUnlXrullsxxQ+
        kx0g8BTA3aZlButjSTyjODq/WcQcW/B/Je4OQhLvKQDnzN1mp0nnkvAhR8VuMzNrpm1mpjgkoVwB/v8D
        TgDQASA1MVpwzwAAAABJRU5ErkJggg==
</value>
  </data>
  <metadata name="mainToolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>248, 17</value>
  </metadata>
  <data name="navigateBackwardButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAC4SURBVDhPrZPBDcMgFEMZreLOWlwzQjcoyzAAG7Q9AEfK
        IyBB1CRqiSUriG+b/0kiLkeM8RZCMJlpQ0Otyr4jixbnXNJaJ6VUklIWsmaPmvf+XuUjKFhrB+OW1NBw
        ULWtoDXSj8yNaNAO4zAfLfZC8zDp9XyXZ78P0eKp9hIwnH5khmjxVPsa0IrNvMemuz5gdoS5S5x+jSAn
        Ln9/SA0USKfFPog1e9R2zQ20xnyZ5aI6nv9Mv0OID3SsyI69hw4TAAAAAElFTkSuQmCC
</value>
  </data>
  <data name="navigateForwardButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAC/SURBVDhPrZNBDoMgFEQ5WuPea7n1CF3UdbkMB+AGrQtk
        SXkICVBo0+okEwl/ZvgfVZwOa+1l2zbp6SpKalHWhhfNWms3TZMbx9ENwxDImj1qxphrlJegoJQqjDWp
        oeGgaNtBa6R/MieiQVuMw3y0WIvlXbrnYw3PfB8tnmgPAd3TWyFo8UT7HpCKiHvMQ84P6I2w3JY3c2uE
        Y5d4+DUCnzj//SElUCCdFvMg1uxR65oTaI35PMNFZfz+M/0OIV7WeMhtFzGH3AAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="_refreshButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAD7SURBVDhPtZMhDsJAFERbmhAEkiaoHgFRjUYgEFUIjoJB
        owmCI3CKppozIBBIFAJVZrZ/QqHdpBVM8li2/Fn2706Df2oK5saED7pqDS6gNO6gCMPwgDEFXg3BGch4
        BUswAFISx/HYvjd0AjLfAFvorBmQmbCNVqGVI4asmn20AzK/wAg0BPMeg2oWfBZEUbTCkAMt8AAb8Nvr
        FqiGPAFvyH1wUv+RxXVxMS7KxVWT2587cTvcVmnbbBPbcjUG2/5SZgfkEw9WZsKD7yxeKa9WZl55UxaS
        pJo5MUQME0MlM8PG0HmVWmwLwBjLyHh789EmvkB6mXqlsoeC4A1V70PJoljBogAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="showHelpButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAYASURBVEhLnZZrUNTnFcb93A/mQmLMNJnEJNOMmhrlIgsL
        C0tEZaIouiACUTB0i7IY06kSTTomdGJUrCJEWURuiwgKwUEUCZes3BZcWCQiN0vANtoEdJrWW4u3X9/z
        H6hJtJlMz8yZ/+28z3nf5zzn7E76KUtKSNJZ1lnes6y1lCcmJvZbEi03xbV79U6+Scx4+M83i8Xir0Cs
        ycnJQ7m5uX/r6Oj419atW++PjY0hLvfyTr5JjMTKmvHlP21qZ+s2bdzUU1ZW9vcrIyOcO9dNxckG9uac
        JnlnPe9+XMMn++s5VNrAl+d6uDo6yqmqqiuyRtaOwzza1E4279q16+yF3t47Ha5OCsua+f2eXiK29BOy
        oY8F63sItpwjaG0HRnMTixJr2bitkpZWF0ODg7dlrWCMw/3QJLsE2O32e/WnHfzxQC+mzX8mOOkChoRe
        dGu60Zu71NWFR0wbc1c1o19jR/dWNYGryzhoq+T8+fN3BeOhkwh/csSBnt47dfUNbMkYIOTdC8xLGuAN
        5fHbhkk7MkJB1VXyjo+Qavsrize0MSeyFq/oaryiKvAw2cguqODi0NBtja7v10SKJJw7ne2kWM+y8J0+
        5ln68Yk/T/TWQfou3uLHNvbvMTbscDI9rILXTcdwN5XgGWal9Uwn1dXVVwRTAxeZiRJGv/2Wg8WNRL7f
        R9C6HozK3Ve5MG8b1ACvX7/FxUvfcfPWbe2Z+/e5cvUauqhypi8pZVbYYWaF5hD/O6umMsHUJCxaFrm5
        XF2s3+7CuPZLAhO6MJhdeK46gym5m027u1j42yrmRFSwaL2Db0ZvcO/uPe7fu0vclhqmLbDx2tJCXl+W
        h/uSNJyubgRTsKW45aLpQ0eqWfROOwHmDvx+044+rhVdbBOeMXZeM51i9orP+bXpJC+/WUZd0wD//O4f
        3LxxgxDzUZ7SZ/F80AFenJfJq/PT+DC1CMEUbOG/X46UsrucNxJa8Y9vU8AteK9qxOstO57RNcyOqOJX
        oeW4GRQNYSU4nWf5+i9f09LezwvGdJ4NUAmMVl4IkgTphMamajQJ9iRpfenOhM1Fmra9VzdowF4xtRrw
        K2+W82zQYSb75jN9cQmf1zbS6mijt7efxQklPGPIZFpwFi8pf3m+lekL92FY/rHW8YL93wTxG3Pxj1Oy
        U8AeUULHCZ4LVsc3FDJZl8uMxUU0NrbiaHGoBO2s+6CEXwZmM2NRvvJcZmqex+zQbIzhOx4kmKAoOSUP
        n5hKPCJP8qqi4xljEU/5F+Dml8MvPLNYaD7GVwP9nO3sJiKpmJfmZzMnrBCP5YfwNBUpL8bLdBif8HzC
        zRnfo2i8yNbcUrxWHGVmWBlTAmwKOJcn9Qd50jeLJ3SZxL13guGvBqg/3aHkeFA1VjE6Fa+P+gy/qGPq
        ekxdP8MQaSNlV+mDIk/ItO2MUk+kqCEHN/0BnlDAj+n24+b7qSpiJr4ROZgSbYTEFTA3vFiBlWOIOU5A
        TJXyUxiiqzDKc0Q+7Z39D2Q60WgyOWMtqUz126tA9zF5bgaPeaczxS+D54yZBMYcwmqz84m1We28TAMM
        iKlR/gUB0cqj6giKqiQ2KfuHjSYmbS0jt7bOzszgj3ja90887r0HN+80pvpn4L4sB3uDE2d7J93dfXyY
        5lBjoRpDVAN+K5vwX9FAYGQd/oo2p6uXysrKEcHUwMUmhp2M3PR9Obzo/xFTfHYw1Xe30vmnipICmhxd
        DA4Ocf3aNf6Q2sCcJfXowx3oTA70pkYMy45jzTvFpUuXxh4admIyYmXUysjduSeHVwLeZ5pxBzNC9jNL
        SXDB26UUl7eRd8SJbtkJpZZmfJa34LtUnWJpBWnWKoaHh+88clxPmDqW9oMjI7fO3oxpTQqeoTvVTrPw
        W1GIj5qY7kuOYlxZg8FUi2/ocVaai2hu6+Hy5ctjslYwxuEebZJdjqhG7qgU60x7F9v32FidlE5Y7F7l
        +1ltyWd7WgWdXRe0ggrnGi3/a+c/NuFPiiRKELmJpgVIulNc7uWdfJMYiX2I859jIjPRsjSMAvk//rZM
        mvQfl4i37VKcKEUAAAAASUVORK5CYII=
</value>
  </data>
  <metadata name="_initialTreeRefreshTimer.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>353, 17</value>
  </metadata>
  <metadata name="_propertyImageList.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>754, 17</value>
  </metadata>
  <data name="_propertyImageList.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAADM
        BwAAAk1TRnQBSQFMAgEBAgEAAXABBgFwAQYBEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAARADAAEBAQABCAYAAQQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD/wEAIP8gACD/IAAg/yAA
        IP8gAAP/AQcH7QEHB/8BBwftAQcE/yAAA/8B7QfyAe0H/wHtB/IB7QT/IAAD/wHtA/IBZgPyAe0H/wHt
        B/IB7QT/IAAD/wHtA/MBZgPzAe0H/wHtB/MB7QT/IAAD/wHtAf8FiwH/Ae0H/wHtAfMFiwHzAe0E/yAA
        A/8B7QP/AWYD/wHtB/8B7Qf/Ae0E/yAAA/8B7QP/AWYD/wHtB/8B7Qf/Ae0E/yAAA/8B7Qf/Ae0H/wHt
        B/8B7QT/IAAD/wEHB+0BBwf/AQcH7QEHBP8gACD/IAAg/yAAIP8gAAFCAU0BPgcAAT4DAAEoAwABQAMA
        ARADAAEBAQABAQUAAYAXAAP/gQAL
</value>
  </data>
  <metadata name="messagesToolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>600, 17</value>
  </metadata>
  <metadata name="messagesToolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>600, 17</value>
  </metadata>
  <data name="clearMessagesButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACJSURBVDhPrZIBCoAgDEU9SkfoDiJeo5t1VJv0P+bc0qAH
        Q4n95xLDr6SUdmxdcs4btj0SPmQpMcbz/jLCHqwNnFxYloRhFjINhEyJDrtTWpLlMNGSZ03DxJIshyt6
        7FrLAivMmkqsC9O/40rebnsqwfP0GwQtkQP7h8QJrDChBL0jg9VgpecDIVz/AXOdrzVK3QAAAABJRU5E
        rkJggg==
</value>
  </data>
  <metadata name="eventsToolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>495, 17</value>
  </metadata>
  <metadata name="eventsToolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>495, 17</value>
  </metadata>
  <data name="clearEventsButton.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACJSURBVDhPrZIBCoAgDEU9SkfoDiJeo5t1VJv0P+bc0qAH
        Q4n95xLDr6SUdmxdcs4btj0SPmQpMcbz/jLCHqwNnFxYloRhFjINhEyJDrtTWpLlMNGSZ03DxJIshyt6
        7FrLAivMmkqsC9O/40rebnsqwfP0GwQtkQP7h8QJrDChBL0jg9VgpecDIVz/AXOdrzVK3QAAAABJRU5E
        rkJggg==
</value>
  </data>
  <metadata name="memoryRefreshTimer.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>1044, 17</value>
  </metadata>
  <metadata name="updateChecker.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>910, 17</value>
  </metadata>
</root>