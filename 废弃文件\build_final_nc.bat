@echo off
echo ==============================================
echo 编译NC终极版遍历工具
echo ==============================================

echo 检查编译器...
set MSBUILD_PATH="D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe"
if not exist %MSBUILD_PATH% (
    echo 尝试系统路径中的MSBuild...
    where msbuild >nul 2>&1
    if errorlevel 1 (
        echo 错误: 未找到MSBuild编译器
        echo 请安装Visual Studio或Build Tools
        pause
        exit /b 1
    ) else (
        set MSBUILD_PATH=msbuild
    )
)

echo 清理旧文件...
if exist NCTraverserFinal.exe del NCTraverserFinal.exe

echo 编译NCTraverserFinal.cs...
echo 使用MSBuild: %MSBUILD_PATH%
%MSBUILD_PATH% NCTraverser.csproj /p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath=.\ /p:AssemblyName=NCTraverserFinal

if exist NCTraverserFinal.exe (
    echo.
    echo ✅ 编译成功!
    echo 生成文件: NCTraverserFinal.exe
    dir NCTraverserFinal.exe
    echo.
    echo 运行测试:
    echo .\NCTraverserFinal.exe
) else (
    echo.
    echo ❌ 编译失败!
    echo 检查错误信息并修复代码问题
)

echo.
echo 按任意键退出...
pause >nul 