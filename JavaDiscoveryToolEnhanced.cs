using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace JavaDiscoveryTool
{
    public partial class JavaDiscoveryToolEnhanced : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeViewJavaApps;
        private PropertyGrid propertyGridDetails;
        private TextBox textBoxLog;
        private Button buttonRefresh;
        private Button buttonDiagnose;

        public JavaDiscoveryToolEnhanced()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        private void InitializeComponent()
        {
            this.Text = "Java程序发现工具 - 增强版";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建分割面板
            var splitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Horizontal,
                SplitterDistance = 400
            };

            var topSplitContainer = new SplitContainer
            {
                Dock = DockStyle.Fill,
                Orientation = Orientation.Vertical,
                SplitterDistance = 600
            };

            // 树视图
            treeViewJavaApps = new TreeView
            {
                Dock = DockStyle.Fill,
                ImageList = new ImageList()
            };
            treeViewJavaApps.ImageList.Images.Add(Properties.Resources.folder);
            treeViewJavaApps.ImageList.Images.Add(Properties.Resources.component);
            treeViewJavaApps.AfterSelect += TreeViewJavaApps_AfterSelect;

            // 属性网格
            propertyGridDetails = new PropertyGrid
            {
                Dock = DockStyle.Fill
            };

            // 日志文本框
            textBoxLog = new TextBox
            {
                Dock = DockStyle.Fill,
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 9)
            };

            // 按钮面板
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 40
            };

            buttonRefresh = new Button
            {
                Text = "刷新发现",
                Location = new Point(10, 8),
                Size = new Size(100, 25)
            };
            buttonRefresh.Click += ButtonRefresh_Click;

            buttonDiagnose = new Button
            {
                Text = "增强诊断",
                Location = new Point(120, 8),
                Size = new Size(100, 25)
            };
            buttonDiagnose.Click += ButtonDiagnose_Click;

            buttonPanel.Controls.Add(buttonRefresh);
            buttonPanel.Controls.Add(buttonDiagnose);

            topSplitContainer.Panel1.Controls.Add(treeViewJavaApps);
            topSplitContainer.Panel2.Controls.Add(propertyGridDetails);

            splitContainer.Panel1.Controls.Add(topSplitContainer);
            splitContainer.Panel2.Controls.Add(textBoxLog);

            this.Controls.Add(splitContainer);
            this.Controls.Add(buttonPanel);
        }

        private void InitializeAccessBridge()
        {
            try
            {
                accessBridge = new AccessBridge();
                LogMessage("Access Bridge 初始化成功");
                RefreshJavaApplications();
            }
            catch (Exception ex)
            {
                LogMessage($"Access Bridge 初始化失败: {ex.Message}");
                MessageBox.Show($"无法初始化 Access Bridge: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ButtonRefresh_Click(object sender, EventArgs e)
        {
            RefreshJavaApplications();
        }

        private void ButtonDiagnose_Click(object sender, EventArgs e)
        {
            if (treeViewJavaApps.SelectedNode?.Tag is JavaElementInfo elementInfo)
            {
                DiagnoseElement(elementInfo);
            }
            else
            {
                LogMessage("请先选择一个Java组件进行诊断");
            }
        }

        private void RefreshJavaApplications()
        {
            try
            {
                treeViewJavaApps.Nodes.Clear();
                LogMessage("开始搜索Java应用程序...");

                var jvms = accessBridge.EnumJvms();
                LogMessage($"发现 {jvms.Count} 个Java虚拟机");

                foreach (var jvm in jvms)
                {
                    var jvmNode = new TreeNode($"JVM {jvm.JvmId}")
                    {
                        Tag = jvm,
                        ImageIndex = 0
                    };

                    foreach (var window in jvm.Windows)
                    {
                        CreateWindowNode(jvmNode, window);
                    }

                    treeViewJavaApps.Nodes.Add(jvmNode);
                    jvmNode.Expand();
                }

                LogMessage("Java应用程序搜索完成");
            }
            catch (Exception ex)
            {
                LogMessage($"搜索Java应用程序时出错: {ex.Message}");
            }
        }

        private void CreateWindowNode(TreeNode parentNode, AccessibleWindow window)
        {
            try
            {
                var windowInfo = window.GetInfo();
                var windowNode = new TreeNode($"窗口: {windowInfo.name ?? "未命名"}")
                {
                    Tag = new JavaElementInfo
                    {
                        VmId = window.JvmId,
                        AccessibleContext = window.AccessibleContextHandle,
                        ContextInfo = windowInfo,
                        AccessibleNode = window
                    },
                    ImageIndex = 0
                };

                // 使用增强的子组件获取方法
                AddChildNodesEnhanced(windowNode, window, 0, 3);

                parentNode.Nodes.Add(windowNode);
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode($"错误: {ex.Message}")
                {
                    ForeColor = Color.Red
                };
                parentNode.Nodes.Add(errorNode);
            }
        }

        /// <summary>
        /// 使用增强方法添加子节点
        /// </summary>
        private void AddChildNodesEnhanced(TreeNode parentNode, AccessibleNode accessibleNode, int currentDepth, int maxDepth)
        {
            if (currentDepth >= maxDepth) return;

            try
            {
                IEnumerable<AccessibleNode> children;

                // 如果是AccessibleContextNode，使用增强方法
                if (accessibleNode is AccessibleContextNode contextNode)
                {
                    LogMessage($"使用增强方法获取子组件: {contextNode.GetInfo().name ?? "未命名"}");
                    children = contextNode.GetChildrenEnhanced();
                }
                else
                {
                    children = accessibleNode.GetChildren();
                }

                var childList = children.ToList();
                LogMessage($"找到 {childList.Count} 个子组件");

                foreach (var child in childList.Take(20)) // 限制显示数量
                {
                    try
                    {
                        var childInfo = child.GetInfo();
                        var childNode = new TreeNode($"{childInfo.name ?? "未命名"} [{childInfo.role ?? "未知"}]")
                        {
                            Tag = new JavaElementInfo
                            {
                                VmId = child.JvmId,
                                AccessibleContext = (child as AccessibleContextNode)?.AccessibleContextHandle,
                                ContextInfo = childInfo,
                                AccessibleNode = child
                            },
                            ImageIndex = 1
                        };

                        parentNode.Nodes.Add(childNode);

                        // 递归添加子节点
                        if (childInfo.childrenCount > 0)
                        {
                            AddChildNodesEnhanced(childNode, child, currentDepth + 1, maxDepth);
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorNode = new TreeNode($"子组件错误: {ex.Message}")
                        {
                            ForeColor = Color.Red
                        };
                        parentNode.Nodes.Add(errorNode);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode($"获取子组件失败: {ex.Message}")
                {
                    ForeColor = Color.Red
                };
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void TreeViewJavaApps_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag is JavaElementInfo elementInfo)
            {
                ShowElementDetails(elementInfo);
            }
        }

        private void ShowElementDetails(JavaElementInfo elementInfo)
        {
            try
            {
                var properties = new Dictionary<string, object>
                {
                    ["VM ID"] = elementInfo.VmId,
                    ["名称"] = elementInfo.ContextInfo.name ?? "无",
                    ["角色"] = elementInfo.ContextInfo.role ?? "无",
                    ["描述"] = elementInfo.ContextInfo.description ?? "无",
                    ["状态"] = elementInfo.ContextInfo.states ?? "无",
                    ["子组件数"] = elementInfo.ContextInfo.childrenCount,
                    ["位置"] = $"({elementInfo.ContextInfo.x}, {elementInfo.ContextInfo.y})",
                    ["大小"] = $"{elementInfo.ContextInfo.width} x {elementInfo.ContextInfo.height}",
                    ["可访问文本"] = elementInfo.ContextInfo.accessibleText != 0 ? "是" : "否",
                    ["可访问操作"] = elementInfo.ContextInfo.accessibleAction != 0 ? "是" : "否"
                };

                propertyGridDetails.SelectedObject = properties;
            }
            catch (Exception ex)
            {
                LogMessage($"显示组件详情时出错: {ex.Message}");
            }
        }

        private void DiagnoseElement(JavaElementInfo elementInfo)
        {
            try
            {
                LogMessage("=== 开始增强诊断 ===");
                LogMessage($"诊断组件: {elementInfo.ContextInfo.name ?? "未命名"}");

                if (elementInfo.AccessibleNode is AccessibleContextNode contextNode)
                {
                    // 获取诊断信息
                    var diagnosticInfo = contextNode.GetDiagnosticInfo();
                    LogMessage("诊断信息:");
                    LogMessage(diagnosticInfo);

                    // 测试不同的子组件获取方法
                    LogMessage("\n=== 子组件获取测试 ===");

                    // 标准方法
                    try
                    {
                        var standardChildren = contextNode.GetChildren().ToList();
                        LogMessage($"标准方法找到: {standardChildren.Count} 个子组件");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"标准方法失败: {ex.Message}");
                    }

                    // 增强方法
                    try
                    {
                        var enhancedChildren = contextNode.GetChildrenEnhanced().ToList();
                        LogMessage($"增强方法找到: {enhancedChildren.Count} 个子组件");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"增强方法失败: {ex.Message}");
                    }

                    // NC系统检测
                    if (contextNode.IsNCSystemComponent())
                    {
                        LogMessage("✓ 检测到用友NC系统组件");
                    }
                }

                LogMessage("=== 诊断完成 ===\n");
            }
            catch (Exception ex)
            {
                LogMessage($"诊断过程中出错: {ex.Message}");
            }
        }

        private void LogMessage(string message)
        {
            if (textBoxLog.InvokeRequired)
            {
                textBoxLog.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            textBoxLog.AppendText($"[{DateTime.Now:HH:mm:ss}] {message}\r\n");
            textBoxLog.SelectionStart = textBoxLog.Text.Length;
            textBoxLog.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            accessBridge?.Dispose();
            base.OnFormClosed(e);
        }
    }

    public class JavaElementInfo
    {
        public int VmId { get; set; }
        public JavaObjectHandle? AccessibleContext { get; set; }
        public AccessibleContextInfo ContextInfo { get; set; }
        public AccessibleNode AccessibleNode { get; set; }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new JavaDiscoveryToolEnhanced());
        }
    }
}
