<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net461</TargetFramework>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>JavaElementTools</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="WindowsAccessBridgeInterop">
      <HintPath>bin\Debug\WindowsAccessBridgeInterop.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="**/*.cs" />
  </ItemGroup>
  <!-- 基础版本 -->
  <Target Name="BuildBasic">
    <PropertyGroup>
      <OutputAssembly>JavaElementDumper.exe</OutputAssembly>
    </PropertyGroup>
    <ItemGroup>
      <CompileFiles Include="JavaElementDumper.cs" />
    </ItemGroup>
    <Exec Command="dotnet build --configuration Debug --output . --property:AssemblyName=JavaElementDumper --property:StartupObject=JavaElementDumper"
          WorkingDirectory="$(MSBuildProjectDirectory)" />
  </Target>

  <!-- 增强版本 -->
  <Target Name="BuildEnhanced">
    <PropertyGroup>
      <OutputAssembly>JavaElementDumperEnhanced.exe</OutputAssembly>
    </PropertyGroup>
    <ItemGroup>
      <CompileFiles Include="JavaElementDumperEnhanced.cs" />
    </ItemGroup>
    <Exec Command="dotnet build --configuration Debug --output . --property:AssemblyName=JavaElementDumperEnhanced --property:StartupObject=JavaElementDumperEnhanced"
          WorkingDirectory="$(MSBuildProjectDirectory)" />
  </Target>

  <!-- 快速测试 -->
  <Target Name="BuildTest">
    <PropertyGroup>
      <OutputAssembly>JavaAccessBridgeQuickTest.exe</OutputAssembly>
    </PropertyGroup>
    <ItemGroup>
      <CompileFiles Include="JavaAccessBridgeQuickTest.cs" />
    </ItemGroup>
    <Exec Command="dotnet build --configuration Debug --output . --property:AssemblyName=JavaAccessBridgeQuickTest --property:StartupObject=JavaAccessBridgeQuickTest"
          WorkingDirectory="$(MSBuildProjectDirectory)" />
  </Target>

  <!-- 编译所有 -->
  <Target Name="BuildAll" DependsOnTargets="BuildBasic;BuildEnhanced;BuildTest">
    <Message Text="所有工具编译完成" />
  </Target>

</Project>