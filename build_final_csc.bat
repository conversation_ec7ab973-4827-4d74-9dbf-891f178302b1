@echo off
echo ==============================================
echo 使用CSC编译NC终极版遍历工具
echo ==============================================

echo 检查编译器...
where csc >nul 2>&1
if errorlevel 1 (
    echo 尝试找到.NET Framework CSC编译器...
    set CSC_PATH=%WINDIR%\Microsoft.NET\Framework64\v4.0.30319\csc.exe
    if not exist "%CSC_PATH%" (
        set CSC_PATH=%WINDIR%\Microsoft.NET\Framework\v4.0.30319\csc.exe
    )
    if not exist "%CSC_PATH%" (
        echo 错误: 未找到CSC编译器
        echo 请安装.NET Framework 4.0或更高版本
        pause
        exit /b 1
    )
) else (
    set CSC_PATH=csc
)

echo 清理旧文件...
if exist NCTraverserFinal.exe del NCTraverserFinal.exe

echo 编译NCTraverserFinal.cs...
echo 使用编译器: %CSC_PATH%

"%CSC_PATH%" /target:exe /out:NCTraverserFinal.exe /reference:src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll /reference:System.Drawing.dll NCTraverserFinal.cs

if exist NCTraverserFinal.exe (
    echo.
    echo ✅ 编译成功!
    echo 生成文件: NCTraverserFinal.exe
    dir NCTraverserFinal.exe
    echo.
    echo 使用方法:
    echo .\NCTraverserFinal.exe
    echo.
) else (
    echo.
    echo ❌ 编译失败!
    echo 请检查错误信息和依赖项
)

echo 按任意键退出...
pause >nul 