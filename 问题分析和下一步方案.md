# NC Access Bridge 问题分析和解决方案

## 当前状态总结

### ✅ 已完成的工作

1. **编译环境优化**

   - 成功配置 MSBuild 路径：`D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe`
   - 解决了.NET Framework 版本兼容性问题（从 4.0 升级到 4.7.2）
   - 修复了编译器找不到的问题

2. **多版本工具开发**

   - 创建了 NCTraverserFixed.cs（兼容.NET 4.0 语法）
   - 开发了 NCTraverserFinal.cs（终极诊断版本）
   - 实现了 NCTraverserSimple.cs（简化测试版本）
   - 生成了多个可执行文件（NCTraverser.exe, NCTraverserWorking.exe 等）

3. **诊断功能完善**
   - 添加了系统环境检查
   - 实现了 Java 进程发现
   - 加入了 Java 窗口枚举
   - 提供了详细的日志记录

### ❌ 核心问题

**所有开发的工具都无法发现 Java 应用程序（发现 0 个 JVM）**

## 问题根因分析

### 从 JavaFerret 源码分析的关键发现

通过分析附加的 JavaFerret.cpp 和 JavaMonkey.cpp 源码，发现了关键差异：

1. **初始化方式不同**

   ```cpp
   // JavaFerret使用的初始化方式
   BOOL result = initializeAccessBridge();
   ```

2. **窗口枚举策略**

   ```cpp
   // JavaMonkey的成功方式
   EnumWindows((WNDENUMPROC) EnumWndProc, NULL);
   if (IsJavaWindow(hwnd)) {
       GetAccessibleContextFromHWND(hwnd, &vmID, &ac);
   }
   ```

3. **GetAccessibleChildFromContext 直接调用**
   ```cpp
   // 关键API调用
   addComponentNodes(vmID, GetAccessibleChildFromContext(vmID, context, i),
                     newNode, hwnd, treeNodeItem, treeWnd);
   ```

### 可能的问题原因

1. **API 调用方式差异**

   - C# wrapper 可能与原生 C++ API 有细微差别
   - WindowsAccessBridgeInterop.dll 的封装可能不完整

2. **初始化时序问题**

   - Access Bridge 需要特定的初始化顺序
   - 可能需要更长的等待时间

3. **DLL 依赖问题**
   - 可能缺少关键的 Access Bridge DLL
   - 版本不匹配问题

## 解决方案建议

### 方案 1：使用 JavaFerret 工具进行验证 ⭐

**立即可行的解决方案**

1. **启动用友 NC57 系统**
2. **使用项目中的 JavaFerret 工具**：
   ```bash
   cd accessbridge2_0_2
   JavaFerret-64.exe  # 或 JavaFerret-32.exe
   ```
3. **验证是否能发现 NC 系统的组件**
4. **如果成功，说明 Access Bridge 环境正常**

### 方案 2：基于 JavaFerret 开发 C#版本

**参考 JavaFerret 的成功实现**

1. **直接 P/Invoke 调用 Access Bridge API**

   ```csharp
   [DllImport("WindowsAccessBridge-64.dll")]
   static extern bool initializeAccessBridge();

   [DllImport("WindowsAccessBridge-64.dll")]
   static extern bool GetAccessibleContextFromHWND(IntPtr hwnd, out long vmID, out IntPtr ac);
   ```

2. **使用与 JavaFerret 相同的枚举策略**
   - 先枚举所有窗口
   - 使用 IsJavaWindow 检查
   - 直接调用 GetAccessibleContextFromHWND

### 方案 3：修复现有 WindowsAccessBridgeInterop

**诊断和修复封装库问题**

1. **检查 DLL 依赖**

   ```bash
   dumpbin /dependents src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll
   ```

2. **验证 API 映射**
   - 对比 C++原生调用和 C#封装
   - 确保参数和返回值类型正确

### 方案 4：环境配置优化

**确保 Access Bridge 正确配置**

1. **检查必需的 DLL 文件**：

   - WindowsAccessBridge-32.dll
   - WindowsAccessBridge-64.dll
   - JavaAccessBridge-32.dll
   - JavaAccessBridge-64.dll

2. **验证注册表配置**
3. **检查 Java 可访问性支持是否启用**

## 建议的下一步操作

### 立即执行（优先级：高）

1. **测试 JavaFerret 工具**

   ```bash
   cd accessbridge2_0_2
   .\JavaFerret-64.exe
   ```

2. **启动 NC57 系统**
3. **在 JavaFerret 中验证是否能发现 NC 组件**

### 如果 JavaFerret 成功（优先级：中）

1. **分析 JavaFerret 的成功要素**
2. **开发基于 P/Invoke 的 C#版本**
3. **逐步移植成功的调用模式**

### 如果 JavaFerret 也失败（优先级：高）

1. **检查 Access Bridge 安装**
2. **重新安装 Access Bridge 2.0.2**
3. **检查 NC57 的 Java 版本兼容性**

## 总结

虽然我们开发了多个功能完善的遍历工具，但核心问题是无法发现 Java 应用程序。通过 JavaFerret 源码分析，找到了可能的解决方向。建议先验证 JavaFerret 工具，这将帮助我们确定问题是在我们的实现还是在环境配置上。

基于 JavaFerret 的成功经验，我们可以开发出真正有效的 NC 遍历工具。
