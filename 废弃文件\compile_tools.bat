@echo off
echo ==============================
echo Compiling Java Element Tools
echo ==============================

set CSC_PATH=C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe

echo Checking dependencies...
if not exist "bin\Debug\WindowsAccessBridgeInterop.dll" (
    echo ERROR: WindowsAccessBridgeInterop.dll not found
    pause
    exit /b 1
)

if not exist "%CSC_PATH%" (
    echo ERROR: C# compiler not found at %CSC_PATH%
    echo Please install .NET Framework 4.0 or later
    pause
    exit /b 1
)

echo Cleaning old files...
del /q JavaAccessBridgeQuickTest.exe 2>nul
del /q JavaElementDumper.exe 2>nul
del /q JavaElementDumperEnhanced.exe 2>nul

echo.
echo [1/3] Compiling Quick Test Tool...
"%CSC_PATH%" /target:exe /out:JavaAccessBridgeQuickTest.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll JavaAccessBridgeQuickTest.cs
if %ERRORLEVEL% neq 0 (
    echo Quick Test Tool compilation failed!
    pause
    exit /b 1
)

echo [2/3] Compiling Basic Dumper...
"%CSC_PATH%" /target:exe /out:JavaElementDumper.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll JavaElementDumper.cs
if %ERRORLEVEL% neq 0 (
    echo Basic Dumper compilation failed!
    pause
    exit /b 1
)

echo [3/3] Compiling Enhanced Dumper...
"%CSC_PATH%" /target:exe /out:JavaElementDumperEnhanced.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll JavaElementDumperEnhanced.cs
if %ERRORLEVEL% neq 0 (
    echo Enhanced Dumper compilation failed!
    pause
    exit /b 1
)

echo.
echo ==============================
echo Compilation completed successfully!
echo ==============================

echo.
echo Generated files:
if exist "JavaAccessBridgeQuickTest.exe" echo   - JavaAccessBridgeQuickTest.exe
if exist "JavaElementDumper.exe" echo   - JavaElementDumper.exe  
if exist "JavaElementDumperEnhanced.exe" echo   - JavaElementDumperEnhanced.exe

echo.
echo Usage Instructions:
echo 1. Start your Java application (like Yonyou NC)
echo 2. Run JavaAccessBridgeQuickTest.exe to verify setup
echo 3. Run JavaElementDumperEnhanced.exe for comprehensive traversal
echo.
pause
