@echo off
echo Compiling NC Traverser Tool...

set CSC="C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"

echo Checking dependencies...
if not exist "src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll" (
    echo ERROR: WindowsAccessBridgeInterop.dll not found
    echo Please run build.bat first to compile dependencies
    pause
    exit /b 1
)

echo Compiling NCTraverser.exe...
%CSC% /target:exe /out:NCTraverser.exe /reference:src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll /reference:System.Drawing.dll JavaElementDumperEnhanced.cs

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo SUCCESS! NCTraverser.exe compiled successfully!
echo.
echo Usage:
echo   NCTraverser.exe              - Traverse all Java applications
echo   NCTraverser.exe NC           - Traverse applications containing "NC"
echo   NCTraverser.exe yonyou       - Traverse applications containing "yonyou"
echo.
echo The tool will generate a detailed log file: NC_Elements_Complete_YYYYMMDD_HHMMSS.log
echo.
pause
