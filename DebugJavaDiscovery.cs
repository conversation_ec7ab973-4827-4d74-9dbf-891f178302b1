using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace DebugJavaDiscovery
{
    public partial class DebugForm : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeView;
        private TextBox logTextBox;
        private Button refreshButton;

        public DebugForm()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        private void InitializeComponent()
        {
            this.Text = "调试Java发现工具";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;

            var splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 300;

            // 树视图
            treeView = new TreeView();
            treeView.Dock = DockStyle.Fill;

            // 日志文本框
            logTextBox = new TextBox();
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.ReadOnly = true;
            logTextBox.Font = new Font("Consolas", 9);

            // 按钮面板
            var buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Top;
            buttonPanel.Height = 40;

            refreshButton = new Button();
            refreshButton.Text = "刷新";
            refreshButton.Location = new Point(10, 8);
            refreshButton.Size = new Size(80, 25);
            refreshButton.Click += RefreshButton_Click;

            buttonPanel.Controls.Add(refreshButton);

            splitContainer.Panel1.Controls.Add(treeView);
            splitContainer.Panel2.Controls.Add(logTextBox);

            this.Controls.Add(splitContainer);
            this.Controls.Add(buttonPanel);
        }

        private void InitializeAccessBridge()
        {
            try
            {
                LogMessage("正在初始化 Access Bridge...");
                accessBridge = new AccessBridge();
                LogMessage("Access Bridge 初始化成功");
                RefreshJavaApplications();
            }
            catch (Exception ex)
            {
                LogMessage("Access Bridge 初始化失败: " + ex.Message);
                LogMessage("堆栈跟踪: " + ex.StackTrace);
                MessageBox.Show("无法初始化 Access Bridge: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            RefreshJavaApplications();
        }

        private void RefreshJavaApplications()
        {
            try
            {
                treeView.Nodes.Clear();
                LogMessage("=== 开始搜索Java应用程序 ===");

                if (accessBridge == null)
                {
                    LogMessage("错误: AccessBridge 为 null");
                    return;
                }

                LogMessage("调用 accessBridge.EnumJvms()...");
                var jvms = accessBridge.EnumJvms();
                LogMessage("EnumJvms() 返回成功，发现 " + jvms.Count + " 个Java虚拟机");

                if (jvms.Count == 0)
                {
                    LogMessage("警告: 没有发现任何Java虚拟机");
                    LogMessage("请确保:");
                    LogMessage("1. 有Java应用程序正在运行");
                    LogMessage("2. Java Access Bridge 已正确安装和配置");
                    LogMessage("3. Java应用程序启用了辅助功能支持");
                    return;
                }

                foreach (var jvm in jvms)
                {
                    LogMessage("处理 JVM " + jvm.JvmId + "...");
                    var jvmNode = new TreeNode("JVM " + jvm.JvmId);
                    jvmNode.Tag = jvm;

                    LogMessage("JVM " + jvm.JvmId + " 有 " + jvm.Windows.Count + " 个窗口");

                    foreach (var window in jvm.Windows)
                    {
                        LogMessage("处理窗口: HWND=" + window.Hwnd);
                        CreateWindowNode(jvmNode, window);
                    }

                    treeView.Nodes.Add(jvmNode);
                    jvmNode.Expand();
                }

                LogMessage("=== Java应用程序搜索完成 ===");
            }
            catch (Exception ex)
            {
                LogMessage("搜索Java应用程序时出错: " + ex.Message);
                LogMessage("堆栈跟踪: " + ex.StackTrace);
            }
        }

        private void CreateWindowNode(TreeNode parentNode, AccessibleWindow window)
        {
            try
            {
                LogMessage("获取窗口信息...");
                var windowInfo = window.GetInfo();
                LogMessage("窗口名称: " + (windowInfo.name ?? "未命名"));
                LogMessage("窗口角色: " + (windowInfo.role ?? "未知"));
                LogMessage("子组件数: " + windowInfo.childrenCount);

                var windowNode = new TreeNode("窗口: " + (windowInfo.name ?? "未命名"));
                windowNode.Tag = window;

                // 简单添加子节点，不使用复杂的增强方法
                LogMessage("开始添加子节点...");
                AddSimpleChildNodes(windowNode, window, 0, 2);

                parentNode.Nodes.Add(windowNode);
                LogMessage("窗口节点添加完成");
            }
            catch (Exception ex)
            {
                LogMessage("创建窗口节点时出错: " + ex.Message);
                LogMessage("堆栈跟踪: " + ex.StackTrace);
                var errorNode = new TreeNode("错误: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void AddSimpleChildNodes(TreeNode parentNode, AccessibleNode accessibleNode, int currentDepth, int maxDepth)
        {
            if (currentDepth >= maxDepth)
            {
                LogMessage("达到最大深度 " + maxDepth + "，停止递归");
                return;
            }

            try
            {
                LogMessage("获取子组件，当前深度: " + currentDepth);

                // 使用最简单的标准方法
                var children = accessibleNode.GetChildren().ToList();
                LogMessage("找到 " + children.Count + " 个子组件");

                int count = 0;
                foreach (var child in children.Take(5)) // 限制显示数量
                {
                    try
                    {
                        count++;
                        LogMessage("处理第 " + count + " 个子组件...");

                        if (child is AccessibleContextNode)
                        {
                            var contextChild = (AccessibleContextNode)child;
                            var childInfo = contextChild.GetInfo();
                            var childNode = new TreeNode((childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "]");
                            childNode.Tag = child;

                            parentNode.Nodes.Add(childNode);

                            // 递归添加子节点
                            if (childInfo.childrenCount > 0 && currentDepth < maxDepth - 1)
                            {
                                AddSimpleChildNodes(childNode, child, currentDepth + 1, maxDepth);
                            }
                        }
                        else
                        {
                            var childNode = new TreeNode("未知类型节点");
                            childNode.Tag = child;
                            parentNode.Nodes.Add(childNode);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage("处理子组件 " + count + " 时出错: " + ex.Message);
                        var errorNode = new TreeNode("子组件错误: " + ex.Message);
                        errorNode.ForeColor = Color.Red;
                        parentNode.Nodes.Add(errorNode);
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage("获取子组件失败: " + ex.Message);
                LogMessage("堆栈跟踪: " + ex.StackTrace);
                var errorNode = new TreeNode("获取子组件失败: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void LogMessage(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            logTextBox.AppendText("[" + DateTime.Now.ToString("HH:mm:ss") + "] " + message + "\r\n");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            if (accessBridge != null)
                accessBridge.Dispose();
            base.OnFormClosed(e);
        }
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new DebugForm());
        }
    }
}
