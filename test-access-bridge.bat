@echo off
echo =================================================
echo Access Bridge 配置测试
echo =================================================
echo.

set JAVA8_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot

echo 1. 检查Java 8安装:
if exist "%JAVA8_HOME%\bin\java.exe" (
    echo ✓ Java 8 已安装: %JAVA8_HOME%
) else (
    echo ✗ Java 8 未找到
    goto :end
)
echo.

echo 2. 检查Java版本:
"%JAVA8_HOME%\bin\java.exe" -version
echo.

echo 3. 检查jabswitch工具:
if exist "%JAVA8_HOME%\bin\jabswitch.exe" (
    echo ✓ jabswitch.exe 存在
    "%JAVA8_HOME%\bin\jabswitch.exe" -status
) else (
    echo ✗ jabswitch.exe 不存在
)
echo.

echo 4. 检查Access Bridge文件:
if exist "%JAVA8_HOME%\jre\bin\WindowsAccessBridge-64.dll" (
    echo ✓ WindowsAccessBridge-64.dll 存在
    dir "%JAVA8_HOME%\jre\bin\WindowsAccessBridge-64.dll" | findstr "dll"
) else (
    echo ✗ WindowsAccessBridge-64.dll 不存在
)
echo.

echo 5. 检查系统Access Bridge文件:
if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
    echo ✓ 系统WindowsAccessBridge-64.dll 存在
    dir "C:\Windows\System32\WindowsAccessBridge-64.dll" | findstr "dll"
) else (
    echo ✗ 系统WindowsAccessBridge-64.dll 不存在
)
echo.

echo 6. 启动一个简单的Java测试程序来验证Access Bridge:
echo 正在启动Java程序进行测试...
start "" "%JAVA8_HOME%\bin\java.exe" -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge -cp . -version

:end
echo.
echo =================================================
echo 测试完成！请检查上述结果
echo =================================================
pause 