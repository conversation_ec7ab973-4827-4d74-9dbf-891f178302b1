{"format": 1, "restore": {"D:\\Code\\access-bridge-explorer-master\\JavaAccessBridgeQuickTest.csproj": {}}, "projects": {"D:\\Code\\access-bridge-explorer-master\\JavaAccessBridgeQuickTest.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Code\\access-bridge-explorer-master\\JavaAccessBridgeQuickTest.csproj", "projectName": "JavaAccessBridgeQuickTest", "projectPath": "D:\\Code\\access-bridge-explorer-master\\JavaAccessBridgeQuickTest.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Code\\access-bridge-explorer-master\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["d:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net461"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net461": {"targetAlias": "net461", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net461": {"targetAlias": "net461", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}