using System;
using System.Runtime.InteropServices;
using System.Diagnostics;
using System.Text;

namespace NCTraverser
{
    /// <summary>
    /// NC遍历工具 - 使用正确的API函数名
    /// 基于官方AccessBridgeCalls.c的真实实现
    /// </summary>
    public class NCTraverserCorrectAPI
    {
        // 导入正确的DLL函数名（基于AccessBridgeCalls.c分析）
        private const string WINDOWS_ACCESS_BRIDGE_DLL = "WindowsAccessBridge-64.dll";

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern void Windows_run();

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern bool isJavaWindow(IntPtr hwnd);

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern bool getAccessibleContextFromHWND(IntPtr hwnd, out long vmID, out long accessibleContext);

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern bool getAccessibleContextInfo(long vmID, long accessibleContext, out AccessibleContextInfo info);

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern long getAccessibleChildFromContext(long vmID, long accessibleContext, int index);

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern bool getAccessibleContextAt(long vmID, long acParent, int x, int y, out long ac);

        [DllImport(WINDOWS_ACCESS_BRIDGE_DLL, CallingConvention = CallingConvention.Cdecl)]
        private static extern void releaseJavaObject(long vmID, long javaObject);

        // Windows API
        [DllImport("user32.dll")]
        private static extern IntPtr FindWindow(string lpClassName, string lpWindowName);

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        private static extern bool GetWindowRect(IntPtr hwnd, out RECT lpRect);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        [StructLayout(LayoutKind.Sequential)]
        public struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct AccessibleContextInfo
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1024)]
            public string name;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1024)]
            public string description;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string role;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string states;
            public int indexInParent;
            public int childrenCount;
            public int x;
            public int y;
            public int width;
            public int height;
            public bool accessibleComponent;
            public bool accessibleAction;
            public bool accessibleSelection;
            public bool accessibleText;
            public bool accessibleInterfaces;
        }

        public static void Main(string[] args)
        {
            Console.WriteLine("=== NC遍历工具 - 正确API版本 ===");
            Console.WriteLine("基于官方AccessBridgeCalls.c的真实函数名实现");
            Console.WriteLine();

            try
            {
                // 1. 初始化Access Bridge - 使用正确的函数名
                Console.WriteLine("1. 初始化Access Bridge...");
                Windows_run(); // 这是真正的初始化函数
                Console.WriteLine("   初始化成功！");

                // 2. 寻找所有Java窗口
                Console.WriteLine("\n2. 搜索Java窗口...");
                System.Collections.Generic.List<IntPtr> javaWindows = FindJavaWindows();
                Console.WriteLine("   发现 " + javaWindows.Count + " 个Java窗口");

                // 3. 搜索NC相关窗口
                Console.WriteLine("\n3. 分析Java窗口，寻找NC系统...");
                bool foundNC = false;

                foreach (IntPtr hwnd in javaWindows)
                {
                    try
                    {
                        string windowTitle = GetWindowTitle(hwnd);
                        Console.WriteLine("\n   分析窗口: " + windowTitle + " (句柄: 0x" + hwnd.ToString("X") + ")");

                        // 检查是否为NC系统窗口
                        if (IsNCSystemWindow(windowTitle))
                        {
                            Console.WriteLine("   *** 找到NC系统窗口！***");
                            foundNC = true;
                            AnalyzeNCWindow(hwnd);
                        }
                        else
                        {
                            Console.WriteLine("   普通Java窗口: " + windowTitle);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("   分析窗口时出错: " + ex.Message);
                    }
                }

                if (!foundNC)
                {
                    Console.WriteLine("\n   未找到NC系统窗口，分析所有Java窗口的详细信息...");
                    foreach (IntPtr hwnd in javaWindows)
                    {
                        try
                        {
                            string windowTitle = GetWindowTitle(hwnd);
                            Console.WriteLine("\n--- 详细分析: " + windowTitle + " ---");
                            AnalyzeNCWindow(hwnd);
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine("   详细分析出错: " + ex.Message);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                Console.WriteLine("程序执行出错: " + ex.Message);
                Console.WriteLine("错误类型: " + ex.GetType().Name);
                if (ex.InnerException != null)
                {
                    Console.WriteLine("内部错误: " + ex.InnerException.Message);
                }
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        private static System.Collections.Generic.List<IntPtr> FindJavaWindows()
        {
            System.Collections.Generic.List<IntPtr> javaWindows = new System.Collections.Generic.List<IntPtr>();

            EnumWindows((hWnd, lParam) =>
            {
                try
                {
                    // 使用正确的函数名检查是否为Java窗口
                    if (isJavaWindow(hWnd))
                    {
                        javaWindows.Add(hWnd);
                        string title = GetWindowTitle(hWnd);
                        Console.WriteLine("   发现Java窗口: " + title + " (句柄: 0x" + hWnd.ToString("X") + ")");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine("   检查窗口时出错: " + ex.Message);
                }

                return true; // 继续枚举
            }, IntPtr.Zero);

            return javaWindows;
        }

        private static string GetWindowTitle(IntPtr hwnd)
        {
            StringBuilder sb = new StringBuilder(256);
            GetWindowText(hwnd, sb, sb.Capacity);
            return sb.ToString();
        }

        private static bool IsNCSystemWindow(string title)
        {
            if (string.IsNullOrEmpty(title)) return false;

            string titleLower = title.ToLower();
            return titleLower.Contains("nc") || 
                   titleLower.Contains("用友") || 
                   titleLower.Contains("财务") ||
                   titleLower.Contains("erp") ||
                   titleLower.Contains("管理系统");
        }

        private static void AnalyzeNCWindow(IntPtr hwnd)
        {
            try
            {
                long vmID;
                long accessibleContext;
                
                // 使用正确的函数名获取AccessibleContext
                if (getAccessibleContextFromHWND(hwnd, out vmID, out accessibleContext))
                {
                    Console.WriteLine("   成功获取AccessibleContext: vmID=0x" + vmID.ToString("X") + ", ac=0x" + accessibleContext.ToString("X"));

                    AccessibleContextInfo rootInfo;
                    // 获取根节点信息
                    if (getAccessibleContextInfo(vmID, accessibleContext, out rootInfo))
                    {
                        Console.WriteLine("   根节点信息:");
                        Console.WriteLine("     名称: " + rootInfo.name);
                        Console.WriteLine("     角色: " + rootInfo.role);
                        Console.WriteLine("     子组件数量: " + rootInfo.childrenCount);
                        Console.WriteLine("     状态: " + rootInfo.states);

                        if (rootInfo.childrenCount > 0)
                        {
                            Console.WriteLine("\n   开始遍历 " + rootInfo.childrenCount + " 个子组件:");
                            TraverseChildren(vmID, accessibleContext, 1, 3); // 最多遍历3层
                        }
                        else
                        {
                            Console.WriteLine("   该窗口没有可访问的子组件");
                        }
                    }
                    else
                    {
                        Console.WriteLine("   无法获取根节点详细信息");
                    }

                    // 释放对象
                    releaseJavaObject(vmID, accessibleContext);
                }
                else
                {
                    Console.WriteLine("   无法从窗口句柄获取AccessibleContext");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("   分析窗口时发生异常: " + ex.Message);
            }
        }

        private static void TraverseChildren(long vmID, long parentContext, int currentLevel, int maxLevel)
        {
            if (currentLevel > maxLevel) return;

            try
            {
                AccessibleContextInfo parentInfo;
                // 获取父节点信息
                if (!getAccessibleContextInfo(vmID, parentContext, out parentInfo))
                {
                    return;
                }

                string indent = new string(' ', currentLevel * 2);

                for (int i = 0; i < parentInfo.childrenCount && i < 20; i++) // 限制每层最多20个子组件
                {
                    try
                    {
                        // 使用正确的函数名获取子组件
                        long childContext = getAccessibleChildFromContext(vmID, parentContext, i);
                        
                        if (childContext != 0)
                        {
                            AccessibleContextInfo childInfo;
                            if (getAccessibleContextInfo(vmID, childContext, out childInfo))
                            {
                                Console.WriteLine(indent + "[" + i + "] " + childInfo.name + " (" + childInfo.role + ") - 子组件数: " + childInfo.childrenCount);

                                // 如果是Panel等容器类型，继续遍历
                                if (childInfo.childrenCount > 0 && 
                                    (childInfo.role.Contains("panel") || childInfo.role.Contains("容器") || 
                                     childInfo.role.Contains("面板") || childInfo.role.Contains("区域")))
                                {
                                    TraverseChildren(vmID, childContext, currentLevel + 1, maxLevel);
                                }
                            }

                            // 释放子对象
                            releaseJavaObject(vmID, childContext);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(indent + "[" + i + "] 获取子组件时出错: " + ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("遍历子组件时出错: " + ex.Message);
            }
        }
    }
} 