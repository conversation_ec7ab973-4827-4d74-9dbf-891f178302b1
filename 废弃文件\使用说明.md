# Access Bridge Explorer 增强版使用说明

## 问题解决进展

### ✅ 已解决的问题

1. **Java Access Bridge 启用成功**
2. **Access Bridge 文件版本匹配**
3. **用友 NC 系统检测正常**
4. **部分组件信息获取成功**

### 📍 当前发现的问题

从您的截图可以看到，有一个 panel 没有展开（没有+号），这个区域对应着用友 NC 的主要内容区域，目前显示为空。

## 🚨 重要发现：根本原因已找到！

**问题原因：** `accessibility.properties` 配置文件中的关键设置被注释掉了！

- 文件位置：`C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\lib\accessibility.properties`
- 问题：`assistive_technologies=com.sun.java.accessibility.AccessBridge` 被注释（前面有#）
- 影响：Java Access Bridge 功能未完全激活，只能看到窗口框架，看不到内部控件

## 立即可用的解决方案

### 1. 🔧 修复关键配置（最重要）

```bash
# 右键以管理员身份运行 - 这个是关键修复！
fix-accessibility-properties.bat
```

### 2. 运行综合修复脚本（备选）

```bash
# 右键以管理员身份运行
fix-access-bridge.bat
```

### 2. 重新配置用友 NC 启动参数

使用以下完整的启动参数：

```
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\java.exe" -Dnc.jstart.server=************ -Dnc.jstart.protocol=http -Dnc.jstart.port=80 -Dnc.client.location=nc client home -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge -Djava.awt.headless=false [其他NC启动参数]
```

### 3. 运行增强版 AccessBridgeExplorer

- 位置：`bin\Release\AccessBridgeExplorer.exe`
- 新增功能：空 Panel 自动诊断
- 详细日志：查看 Messages 窗口获取诊断信息

## 使用步骤

1. **准备环境**

   - 确保 Java 8 已安装
   - 运行 `fix-access-bridge.bat`（以管理员权限）

2. **启动用友 NC**

   - 使用新的启动参数
   - 等待完全加载

3. **运行 AccessBridgeExplorer**

   - 启动增强版程序
   - 按 F5 刷新扫描
   - 查看 Messages 窗口获取详细信息

4. **查看诊断结果**
   - 左侧树形结构显示组件层次
   - 右侧属性面板显示详细信息
   - Messages 窗口显示诊断报告

## 下一步优化方向

### 空 Panel 问题的可能解决方案

1. **延迟加载检测**：某些组件可能需要用户交互后才加载
2. **动态内容监控**：实时监控组件状态变化
3. **替代访问方法**：使用屏幕坐标等方式访问
4. **事件触发**：模拟用户操作触发内容加载

### 验证方法

1. 在用友 NC 中点击或切换到主内容区域
2. 在 AccessBridgeExplorer 中按 F5 刷新
3. 观察是否能检测到新的子组件
4. 查看 Messages 窗口的诊断信息

## 文件说明

- `AccessBridgeExplorer.exe` - 增强版主程序
- `fix-access-bridge.bat` - Access Bridge 修复脚本
- `test-access-bridge.bat` - 配置测试脚本
- `start-nc-with-access-bridge.bat` - 启动帮助脚本

## 技术支持

如果问题仍然存在，请提供：

1. Messages 窗口的完整诊断信息
2. 用友 NC 当前的具体界面状态
3. 任何错误消息或异常情况

这个增强版本专门针对用友 NC 系统进行了优化，应该能够提供更详细的诊断信息和更好的组件访问能力。
