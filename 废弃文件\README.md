# Access Bridge Explorer 增强版开发项目

## 项目概述

本项目旨在解决用友 NC57 系统中 Java 可访问性访问问题。经过深入分析和测试，我们发现了关键问题并开发了多种解决方案。

## 核心问题发现

### 1. API 函数缺失问题 ⚠️

经过测试发现，WindowsAccessBridge DLL 中缺失关键函数：

**❌ 缺失的函数：**

- `initializeAccessBridge()` - API 文档要求的标准初始化函数
- `shutdownAccessBridge()` - API 文档要求的标准退出函数
- `IsJavaWindow()` - 检查窗口是否为 Java 窗口的函数
- `GetAccessibleContextFromHWND()` - 从窗口句柄获取上下文的函数

**✅ 存在的函数：**

- `Windows_run()` - 非标准的初始化函数（JavaFerret 使用的方式）

### 2. 官方工具验证结果

- ✅ **JavaFerret-32.exe 和 JavaFerret-64.exe 可以成功发现 JVM**
- ✅ **可以成功检测和访问用友 NC57 窗口**
- ❌ **不带数字后缀的 JavaFerret.exe 无法工作**

## 开发成果

### 成功编译的工具

1. **NCTraverserStandard.exe** (12,288 bytes) - 严格按照 API 标准实现
2. **NCTraverserFixed3Simple.exe** (10,752 bytes) - 直接 P/Invoke 调用版本
3. **NCTraverserWorking.exe** (13,312 bytes) - 工作版本
4. **TestJavaApp.class** - 增强版 Java 测试程序

### 增强的 Java 测试程序

创建了完整的 NC 系统模拟程序，包含：

- 模拟 NC 界面的完整布局
- 正确的 Access Bridge 配置
- 完整的组件层次结构
- 中文界面支持

## 根本原因分析

### 1. API 文档与实际 DLL 不匹配

官方 API 文档描述的函数在实际 DLL 中不存在，需要使用 JavaFerret 的实际调用方式。

### 2. 编译环境配置

- **MSBuild 路径**：`D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe`
- **.NET Framework**：已从 4.0 升级到 4.7.2
- **字符编码**：修复了中文字符编码问题

### 3. WindowsAccessBridgeInterop 库修复

对核心库进行了重大修复：

- 添加了缺失的抽象方法
- 实现了标准和 Legacy 版本的 API 调用
- 优化了初始化和关闭逻辑

## 解决方案

### 方案 1：使用 JavaFerret 的实际调用方式

```csharp
// 使用Windows_run()而不是initializeAccessBridge()
[DllImport("WindowsAccessBridge-64.dll")]
public static extern bool Windows_run();
```

### 方案 2：直接集成官方 JavaFerret 代码

将 JavaFerret.cpp 的成功实现方式直接移植到 C#中。

### 方案 3：使用 Access Bridge Explorer GUI 工具

增强现有的 ExplorerForm，添加"NC 调试"功能。

## 技术细节

### 编译命令

```bash
# 编译C#项目
"D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe" NCTraverser.csproj

# 编译Java程序
javac TestJavaApp.java -encoding UTF-8
java TestJavaApp
```

### 测试步骤

1. 启动用友 NC57 系统
2. 运行 JavaFerret-64.exe 验证 Access Bridge 工作正常
3. 运行我们的 NC 遍历工具进行诊断
4. 使用增强版 Access Bridge Explorer 进行深度分析

## 关键发现

1. **Access Bridge 本身工作正常** - JavaFerret 可以成功检测 NC 窗口
2. **API 文档不准确** - 实际 DLL 中的函数与文档不符
3. **需要使用 JavaFerret 的调用方式** - `Windows_run()`而不是`initializeAccessBridge()`
4. **NC 窗口可以被正确访问** - 问题在于我们的调用方式，不是 NC 系统本身

## 下一步计划

1. **重构 NC 遍历工具** - 使用 JavaFerret 的实际调用方式
2. **实现完整的 NC 访问功能** - 获取所有子组件信息
3. **开发自动化测试** - 确保稳定性和可靠性
4. **文档完善** - 提供完整的使用说明

## 文件结构

```
access-bridge-explorer-master/
├── NCTraverserStandard.exe          # 标准API版本（会遇到函数缺失问题）
├── NCTraverserFixed3Simple.exe      # 修复版本（使用Windows_run）
├── TestJavaApp.java                 # 增强版Java测试程序
├── src/AccessBridgeExplorer/         # GUI工具
├── src/WindowsAccessBridgeInterop/   # 修复后的核心库
└── accessbridge2_0_2/               # 官方Access Bridge文件
    ├── JavaFerret-64.exe            # 可工作的官方工具
    └── WindowsAccessBridge-64.dll   # 核心DLL（缺失部分API函数）
```

## 结论

通过深入分析，我们确认了 Access Bridge 本身可以正常工作并检测 NC 系统，问题在于我们使用了文档中描述但实际 DLL 中不存在的 API 函数。解决方案是采用 JavaFerret 的实际工作方式，使用`Windows_run()`进行初始化，并避免使用不存在的`IsJavaWindow()`等函数。
