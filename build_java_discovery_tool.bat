@echo off
chcp 65001 >nul
echo ===============================================
echo Java程序发现工具编译脚本
echo 基于Java Access Bridge API
echo ===============================================

echo.
echo 🔧 检查编译环境...

REM 检查MSBuild
set "MSBUILD_PATH=D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe"
if not exist "%MSBUILD_PATH%" (
    echo 尝试系统路径中的MSBuild...
    where msbuild >nul 2>&1
    if errorlevel 1 (
        echo ❌ 未找到MSBuild编译器
        echo 💡 请安装Visual Studio或Build Tools
        pause
        exit /b 1
    ) else (
        set "MSBUILD_PATH=msbuild"
    )
)

echo ✅ 找到MSBuild: %MSBUILD_PATH%

echo.
echo 🔍 检查依赖项...

REM 检查WindowsAccessBridgeInterop.dll
if not exist "src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll" (
    echo ❌ 未找到WindowsAccessBridgeInterop.dll
    echo 💡 正在编译依赖项...
    
    "%MSBUILD_PATH%" src\WindowsAccessBridgeInterop\WindowsAccessBridgeInterop.csproj /p:Configuration=Release /verbosity:minimal
    
    if %ERRORLEVEL% neq 0 (
        echo ❌ 依赖项编译失败
        pause
        exit /b 1
    )
    echo ✅ 依赖项编译成功
) else (
    echo ✅ 依赖项已存在
)

echo.
echo 🔧 开始编译Java发现工具...

REM 清理旧文件
if exist "JavaDiscoveryTool.exe" del "JavaDiscoveryTool.exe"

REM 编译项目
"%MSBUILD_PATH%" JavaDiscoveryTool.csproj /p:Configuration=Release /p:Platform=AnyCPU /verbosity:minimal

if %ERRORLEVEL% equ 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 📁 生成的文件:
    if exist "bin\Release\net472\JavaDiscoveryTool.exe" (
        echo   ✓ bin\Release\net472\JavaDiscoveryTool.exe
        copy "bin\Release\net472\JavaDiscoveryTool.exe" "JavaDiscoveryTool.exe" >nul
        echo   ✓ JavaDiscoveryTool.exe (已复制到根目录)
    )
    
    echo.
    echo 🚀 使用方法:
    echo   1. 确保Java Access Bridge已正确安装和配置
    echo   2. 启动Java应用程序(如用友NC)
    echo   3. 运行 JavaDiscoveryTool.exe
    echo   4. 点击"刷新发现"按钮扫描Java程序
    echo.
    echo 💡 功能特点:
    echo   - 自动发现所有Java应用程序窗口
    echo   - 树形结构显示Java组件层次
    echo   - 详细显示每个组件的属性信息
    echo   - 基于官方Java Access Bridge API
    echo.
    
) else (
    echo.
    echo ❌ 编译失败！
    echo 💡 常见解决方案:
    echo   1. 检查.NET Framework版本是否正确
    echo   2. 确保WindowsAccessBridgeInterop.dll存在
    echo   3. 检查项目引用路径是否正确
)

echo.
echo 按任意键退出...
pause >nul
