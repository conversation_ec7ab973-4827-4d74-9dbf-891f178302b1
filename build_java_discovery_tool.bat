@echo off
echo ===============================================
echo Java Discovery Tool Build Script
echo Based on Java Access Bridge API
echo ===============================================

echo.
echo Checking build environment...

REM Check MSBuild
set "MSBUILD_PATH=D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe"
if not exist "%MSBUILD_PATH%" (
    echo Trying MSBuild from system PATH...
    where msbuild >nul 2>&1
    if errorlevel 1 (
        echo ERROR: MSBuild not found
        echo Please install Visual Studio or Build Tools
        pause
        exit /b 1
    ) else (
        set "MSBUILD_PATH=msbuild"
    )
)

echo Found MSBuild: %MSBUILD_PATH%

echo.
echo Checking dependencies...

REM Check WindowsAccessBridgeInterop.dll
if not exist "src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll" (
    echo WindowsAccessBridgeInterop.dll not found
    echo Building dependency...

    "%MSBUILD_PATH%" src\WindowsAccessBridgeInterop\WindowsAccessBridgeInterop.csproj /p:Configuration=Release /verbosity:minimal

    if %ERRORLEVEL% neq 0 (
        echo Dependency build failed
        pause
        exit /b 1
    )
    echo Dependency build successful
) else (
    echo Dependency already exists
)

echo.
echo Building Java Discovery Tool...

REM Clean old files
if exist "JavaDiscoveryTool.exe" del "JavaDiscoveryTool.exe"

REM Build project
"%MSBUILD_PATH%" JavaDiscoveryTool.csproj /p:Configuration=Release /p:Platform=AnyCPU /verbosity:minimal

if %ERRORLEVEL% equ 0 (
    echo.
    echo Build successful!
    echo.
    echo Generated files:
    if exist "bin\Release\net472\JavaDiscoveryTool.exe" (
        echo   - bin\Release\net472\JavaDiscoveryTool.exe
        copy "bin\Release\net472\JavaDiscoveryTool.exe" "JavaDiscoveryTool.exe" >nul
        echo   - JavaDiscoveryTool.exe (copied to root)
    )

    echo.
    echo Usage:
    echo   1. Ensure Java Access Bridge is properly installed
    echo   2. Start Java applications (e.g., NC)
    echo   3. Run JavaDiscoveryTool.exe
    echo   4. Click "Refresh" to scan Java programs
    echo.
    echo Features:
    echo   - Auto-discover Java application windows
    echo   - Tree view of Java component hierarchy
    echo   - Detailed component properties
    echo   - Based on official Java Access Bridge API
    echo.

) else (
    echo.
    echo Build failed!
    echo Common solutions:
    echo   1. Check .NET Framework version
    echo   2. Ensure WindowsAccessBridgeInterop.dll exists
    echo   3. Check project reference paths
)

echo.
echo Press any key to exit...
pause >nul
