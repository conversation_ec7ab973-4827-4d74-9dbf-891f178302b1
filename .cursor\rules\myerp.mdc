---
description: 
globs: 
alwaysApply: false
---
## 编译环境

- 操作系统：Windows 10.0.19043
- 编译工具：MSBuild.exe (C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe)
- .NET Framework：4.0 Client Profile
- 项目类型：Visual Studio Solution (.sln)

主要解决通过access-bridge-explorer访问用友NC57老版本的访问问题。
现在问题主要集中在无法获取到主窗口下面的内容。需要查出原因并解决，目前看到影刀之类的工具是可以具备完整访问可视区域的能力。应该也是依据这类工具的
.NET Framework 4.0不支持字符串插值语法（$"..."）这是C# 6.0的功能。
D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe
D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\amd64\MSBuild.exe

D:\Program Files\Microsoft Visual Studio\2022\Community\dotnet\net8.0\runtime\dotnet.exe
C:\Program Files\dotnet\dotnet.exe
C:\Program Files (x86)\dotnet\dotnet.exe

D:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\Roslyn\csc.exe

文件头都需要有bom的utf-8  这样才不会导致再powershell等输出乱码
