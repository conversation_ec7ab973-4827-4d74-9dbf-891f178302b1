@echo off
echo =================================================
echo Access Bridge 配置修复工具
echo =================================================
echo.

echo 1. 检查当前Java版本...
java -version
echo.

echo 2. 检查Access Bridge状态...
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -status
echo.

echo 3. 备份系统Access Bridge文件...
if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
    copy "C:\Windows\System32\WindowsAccessBridge-64.dll" "C:\Windows\System32\WindowsAccessBridge-64.dll.backup"
    echo 已备份系统Access Bridge文件
)
echo.

echo 4. 复制新版本Access Bridge文件到系统目录...
copy "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\WindowsAccessBridge-64.dll" "C:\Windows\System32\WindowsAccessBridge-64.dll"
copy "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\JavaAccessBridge-64.dll" "C:\Windows\System32\JavaAccessBridge-64.dll"
copy "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\bin\JAWTAccessBridge-64.dll" "C:\Windows\System32\JAWTAccessBridge-64.dll"
echo.

echo 5. 重新启用Access Bridge...
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -disable
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -enable
echo.

echo 6. 检查配置结果...
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -status
echo.

echo 7. 更新环境变量...
setx JAVA_HOME "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot" /M
setx PATH "%PATH%;C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin" /M
echo.

echo =================================================
echo 修复完成！请重启用友NC系统和AccessBridgeExplorer
echo =================================================
pause 