@echo off
echo Building Java Element Tools...

echo Checking dependencies...
if not exist "bin\Debug\WindowsAccessBridgeInterop.dll" (
    echo ERROR: WindowsAccessBridgeInterop.dll not found
    pause
    exit /b 1
)

echo Cleaning old files...
del /q JavaAccessBridgeQuickTest.exe 2>nul
del /q JavaElementDumper.exe 2>nul
del /q JavaElementDumperEnhanced.exe 2>nul

echo Building Quick Test Tool...
msbuild JavaElementTools.csproj /t:BuildTest /p:Configuration=Debug /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Build failed for Quick Test Tool
    pause
    exit /b 1
)

echo Building Basic Dumper...
msbuild JavaElementTools.csproj /t:BuildBasic /p:Configuration=Debug /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Build failed for Basic Dumper
    pause
    exit /b 1
)

echo Building Enhanced Dumper...
msbuild JavaElementTools.csproj /t:BuildEnhanced /p:Configuration=Debug /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo Build failed for Enhanced Dumper
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo.
echo Generated files:
if exist "JavaAccessBridgeQuickTest.exe" echo   - JavaAccessBridgeQuickTest.exe
if exist "JavaElementDumper.exe" echo   - JavaElementDumper.exe
if exist "JavaElementDumperEnhanced.exe" echo   - JavaElementDumperEnhanced.exe

echo.
echo Usage:
echo 1. Start your Java application (like NC)
echo 2. Run JavaAccessBridgeQuickTest.exe to verify setup
echo 3. Run JavaElementDumperEnhanced.exe for full traversal
echo.
pause
