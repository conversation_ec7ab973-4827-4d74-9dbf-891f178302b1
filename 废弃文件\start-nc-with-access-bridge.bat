@echo off
echo =================================================
echo 用友NC系统 + Access Bridge 启动脚本
echo =================================================
echo.

:: 设置Java 8环境变量
set JAVA_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot
set PATH=%JAVA_HOME%\bin;%PATH%

echo 当前Java版本:
java -version
echo.

echo 检查Access Bridge状态:
"%JAVA_HOME%\bin\jabswitch.exe" -status
echo.

echo 启动参数:
echo -Dnc.jstart.server=************
echo -Dnc.jstart.protocol=http
echo -Dnc.jstart.port=80
echo -Dnc.client.location=nc client home
echo -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge
echo -Djava.awt.headless=false
echo.

echo =================================================
echo 请在用友NC的启动参数中使用以下完整命令：
echo =================================================
echo.
echo "%JAVA_HOME%\bin\java.exe" -Dnc.jstart.server=************ -Dnc.jstart.protocol=http -Dnc.jstart.port=80 -Dnc.client.location=nc client home -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge -Djava.awt.headless=false [其他NC启动参数]
echo.
echo =================================================
echo 请按以下步骤操作：
echo 1. 复制上面的Java路径和参数
echo 2. 修改用友NC的启动配置
echo 3. 重启用友NC系统
echo 4. 运行 AccessBridgeExplorer.exe
echo =================================================
pause 