// Copyright 2015 Google Inc. All Rights Reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
//     http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using System;
using System.Linq;

namespace WindowsAccessBridgeInterop {
  public static class AccessibleNodeExtensions {
    public static Path<AccessibleNode> BuildNodePath(this AccessibleNode childNode) {
      var path = new Path<AccessibleNode>();
      while (childNode != null) {
        path.AddRoot(childNode);
        childNode = childNode.GetParent();
        if (path.Count > 1024) {
          throw new InvalidOperationException("Node path is too deep (more than 1024 parents). Is there a cycle in the parent chain?");
        }
      }

      // Set the "manageDescendants" flag where needed
      var managesDescendants = false;
      foreach (var node in path.OfType<AccessibleContextNode>()) {
        node.SetManagedDescendant(managesDescendants);
        if (!managesDescendants && node.GetInfo().states.Contains("manages descendants")) {
          managesDescendants = true;
        }
      }
      return path;
    }
  }
}