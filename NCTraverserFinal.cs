/**
 * NCTraverserFinal.cs - 终极版NC遍历工具
 * 包含最全面的Java程序发现和诊断功能
 * 兼容.NET Framework 4.0（去除C# 6.0语法）
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using WindowsAccessBridgeInterop;

namespace NCTraverser
{
    class Program
    {
        private static AccessBridge accessBridge;
        private static string logFileName;
        private static List<string> discoveredElements = new List<string>();
        private static Dictionary<string, int> elementStats = new Dictionary<string, int>();

        [DllImport("user32.dll")]
        static extern IntPtr GetForegroundWindow();

        [DllImport("user32.dll")]
        static extern bool IsWindow(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

        [DllImport("user32.dll")]
        static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);

        [DllImport("user32.dll")]
        static extern bool IsWindowVisible(IntPtr hWnd);

        delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        [DllImport("kernel32.dll")]
        static extern IntPtr GetModuleHandle(string lpModuleName);

        static void Main(string[] args)
        {
            logFileName = string.Format("NC_Elements_Final_{0}.log", DateTime.Now.ToString("yyyyMMdd_HHmmss"));
            
            Console.WriteLine("=== NC终极版遍历工具 ===");
            Console.WriteLine("包含最全面的Java程序发现和诊断功能");
            Console.WriteLine("输出详细日志文件: " + logFileName);
            
            WriteLog("=== NC终极版遍历工具启动 ===");
            WriteLog("启动时间: " + DateTime.Now.ToString("yyyy/M/d H:mm:ss"));
            WriteLog("操作系统: " + Environment.OSVersion.ToString());
            WriteLog(".NET版本: " + Environment.Version.ToString());
            WriteLog("进程架构: " + (Environment.Is64BitProcess ? "64位" : "32位"));
            WriteLog("============================================================");

            try
            {
                // 1. 系统环境检查
                WriteLog("\n=== 1. 系统环境检查 ===");
                CheckSystemEnvironment();

                // 2. Access Bridge检查
                WriteLog("\n=== 2. Access Bridge检查 ===");
                CheckAccessBridge();

                // 3. Java进程发现
                WriteLog("\n=== 3. Java进程发现 ===");
                var javaProcesses = FindJavaProcesses();
                
                // 4. Java窗口发现
                WriteLog("\n=== 4. Java窗口发现 ===");
                var javaWindows = FindJavaWindows();

                // 5. 初始化Access Bridge
                WriteLog("\n=== 5. 初始化Access Bridge ===");
                if (!InitializeAccessBridge())
                {
                    WriteLog("❌ Access Bridge初始化失败");
                    Console.WriteLine("Access Bridge初始化失败，程序退出");
                    return;
                }

                // 6. 深度遍历所有发现的Java组件
                WriteLog("\n=== 6. 深度遍历Java组件 ===");
                PerformComprehensiveTraversal(javaWindows);

                // 7. 生成统计报告
                WriteLog("\n=== 7. 遍历统计报告 ===");
                GenerateStatisticsReport();

                WriteLog("\n=== 遍历完成 ===");
                WriteLog("结束时间: " + DateTime.Now.ToString("yyyy/M/d H:mm:ss"));
                
                Console.WriteLine("遍历完成，详细信息请查看日志文件: " + logFileName);
            }
            catch (Exception ex)
            {
                WriteLog("发生异常: " + ex.ToString());
                Console.WriteLine("发生异常: " + ex.Message);
            }
            finally
            {
                if (accessBridge != null)
                {
                    try
                    {
                        accessBridge.Shutdown();
                        WriteLog("Access Bridge已关闭");
                    }
                    catch (Exception ex)
                    {
                        WriteLog("关闭Access Bridge时发生异常: " + ex.Message);
                    }
                }
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static void CheckSystemEnvironment()
        {
            WriteLog("操作系统版本: " + Environment.OSVersion.VersionString);
            WriteLog("系统目录: " + Environment.SystemDirectory);
            WriteLog("当前用户: " + Environment.UserName);
            WriteLog("机器名: " + Environment.MachineName);
            WriteLog("工作目录: " + Environment.CurrentDirectory);
            WriteLog("可用内存: " + (GC.GetTotalMemory(false) / 1024 / 1024) + " MB");
            
            // 检查关键DLL
            string[] criticalDlls = {
                "WindowsAccessBridge-32.dll",
                "WindowsAccessBridge-64.dll", 
                "JavaAccessBridge-32.dll",
                "JavaAccessBridge-64.dll"
            };

            foreach (string dll in criticalDlls)
            {
                IntPtr handle = GetModuleHandle(dll);
                WriteLog(string.Format("DLL检查 {0}: {1}", dll, handle != IntPtr.Zero ? "已加载" : "未加载"));
            }
        }

        static void CheckAccessBridge()
        {
            // 检查Access Bridge相关文件
            string systemPath = Environment.GetFolderPath(Environment.SpecialFolder.System);
            string[] bridgeFiles = {
                "WindowsAccessBridge-32.dll",
                "WindowsAccessBridge-64.dll",
                "JAWTAccessBridge-32.dll",
                "JAWTAccessBridge-64.dll"
            };

            foreach (string file in bridgeFiles)
            {
                string fullPath = Path.Combine(systemPath, file);
                bool exists = File.Exists(fullPath);
                WriteLog(string.Format("Access Bridge文件 {0}: {1}", file, exists ? "存在" : "不存在"));
                if (exists)
                {
                    FileInfo fi = new FileInfo(fullPath);
                    WriteLog(string.Format("  - 大小: {0} bytes, 修改时间: {1}", fi.Length, fi.LastWriteTime));
                }
            }

            // 检查注册表项
            try
            {
                using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SOFTWARE\JavaSoft\Java Runtime Environment"))
                {
                    if (key != null)
                    {
                        WriteLog("Java运行时注册表项: 存在");
                        var currentVersion = key.GetValue("CurrentVersion");
                        if (currentVersion != null)
                        {
                            WriteLog("当前Java版本: " + currentVersion.ToString());
                        }
                    }
                    else
                    {
                        WriteLog("Java运行时注册表项: 不存在");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog("检查注册表时发生异常: " + ex.Message);
            }
        }

        static List<Process> FindJavaProcesses()
        {
            var javaProcesses = new List<Process>();
            var allProcesses = Process.GetProcesses();
            
            WriteLog(string.Format("系统中运行的进程总数: {0}", allProcesses.Length));

            foreach (Process process in allProcesses)
            {
                try
                {
                    string processName = process.ProcessName.ToLower();
                    if (processName.Contains("java") || processName.Contains("javaw") || 
                        processName.Contains("nc") || processName.Contains("eclipse"))
                    {
                        javaProcesses.Add(process);
                        WriteLog(string.Format("发现疑似Java进程: {0} (PID: {1})", process.ProcessName, process.Id));
                        
                        try
                        {
                            WriteLog(string.Format("  - 进程路径: {0}", process.MainModule.FileName));
                            WriteLog(string.Format("  - 内存使用: {0} MB", process.WorkingSet64 / 1024 / 1024));
                            WriteLog(string.Format("  - 启动时间: {0}", process.StartTime));
                        }
                        catch (Exception ex)
                        {
                            WriteLog(string.Format("  - 获取进程详细信息失败: {0}", ex.Message));
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(string.Format("检查进程 {0} 时发生异常: {1}", process.Id, ex.Message));
                }
            }

            WriteLog(string.Format("总共发现 {0} 个疑似Java进程", javaProcesses.Count));
            return javaProcesses;
        }

        static List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            EnumWindows((hWnd, lParam) =>
            {
                if (IsWindowVisible(hWnd))
                {
                    var sb = new StringBuilder(256);
                    GetWindowText(hWnd, sb, sb.Capacity);
                    string windowTitle = sb.ToString();

                    uint processId;
                    GetWindowThreadProcessId(hWnd, out processId);

                    try
                    {
                        Process process = Process.GetProcessById((int)processId);
                        string processName = process.ProcessName.ToLower();

                        // 检查是否为Java窗口
                        bool isJavaWindow = processName.Contains("java") || 
                                          processName.Contains("javaw") ||
                                          windowTitle.ToLower().Contains("nc") ||
                                          windowTitle.ToLower().Contains("java") ||
                                          CheckIfJavaWindow(hWnd);

                        if (isJavaWindow)
                        {
                            javaWindows.Add(hWnd);
                            WriteLog(string.Format("发现Java窗口: {0} (HWND: {1}, PID: {2})", 
                                windowTitle, hWnd.ToString("X"), processId));
                        }
                        else if (!string.IsNullOrEmpty(windowTitle))
                        {
                            // 记录所有可见窗口供参考
                            WriteLog(string.Format("普通窗口: {0} (进程: {1})", windowTitle, processName));
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("检查窗口 {0} 时发生异常: {1}", hWnd.ToString("X"), ex.Message));
                    }
                }
                return true;
            }, IntPtr.Zero);

            WriteLog(string.Format("总共发现 {0} 个Java窗口", javaWindows.Count));
            return javaWindows;
        }

        static bool CheckIfJavaWindow(IntPtr hWnd)
        {
            try
            {
                if (accessBridge != null)
                {
                    // 尝试获取Access Bridge信息
                    AccessibleJvm jvm;
                    AccessibleContextNode context;
                    if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out jvm, out context))
                    {
                        WriteLog(string.Format("窗口 {0} 确认为Java窗口 (通过AccessBridge验证)", hWnd.ToString("X")));
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("验证Java窗口时发生异常: {0}", ex.Message));
            }
            return false;
        }

        static bool InitializeAccessBridge()
        {
            try
            {
                WriteLog("正在初始化Access Bridge...");
                accessBridge = new AccessBridge();
                
                WriteLog("Access Bridge对象创建成功");
                
                // 获取版本信息
                try
                {
                    var versionInfo = accessBridge.Functions.GetVersionInfo();
                    WriteLog(string.Format("Access Bridge版本: {0}.{1}.{2}.{3}", 
                        versionInfo.VMVersion, versionInfo.BridgeJavaClassVersion,
                        versionInfo.BridgeJavaDLLVersion, versionInfo.BridgeWinDLLVersion));
                }
                catch (Exception ex)
                {
                    WriteLog("获取版本信息失败: " + ex.Message);
                }

                WriteLog("✅ Access Bridge初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                WriteLog("❌ Access Bridge初始化失败: " + ex.ToString());
                return false;
            }
        }

        static void PerformComprehensiveTraversal(List<IntPtr> javaWindows)
        {
            if (javaWindows.Count == 0)
            {
                WriteLog("❌ 没有发现Java窗口，无法进行遍历");
                return;
            }

            WriteLog(string.Format("开始遍历 {0} 个Java窗口...", javaWindows.Count));

            foreach (IntPtr hWnd in javaWindows)
            {
                WriteLog(string.Format("\n--- 遍历窗口: {0} ---", hWnd.ToString("X")));
                TraverseJavaWindow(hWnd);
            }

            // 尝试通过JVM枚举方式
            WriteLog("\n--- 尝试JVM枚举方式 ---");
            TryJvmEnumeration();
        }

        static void TraverseJavaWindow(IntPtr hWnd)
        {
            try
            {
                AccessibleJvm jvm;
                AccessibleContextNode rootContext;

                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out jvm, out rootContext))
                {
                    WriteLog(string.Format("✅ 成功获取窗口的根可访问上下文"));
                    WriteLog(string.Format("JVM ID: {0}", jvm.JvmId));
                    
                    // 遍历根上下文
                    TraverseAccessibleNode(rootContext, 0, "ROOT");
                }
                else
                {
                    WriteLog("❌ 无法从窗口获取可访问上下文");
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("遍历窗口时发生异常: {0}", ex.Message));
            }
        }

        static void TryJvmEnumeration()
        {
            try
            {
                var jvms = accessBridge.GetJavaVirtualMachines();
                WriteLog(string.Format("发现 {0} 个Java虚拟机", jvms.Length));

                foreach (var jvm in jvms)
                {
                    WriteLog(string.Format("JVM ID: {0}", jvm.JvmId));
                    
                    try
                    {
                        var windows = jvm.GetAccessibleWindows();
                        WriteLog(string.Format("JVM {0} 包含 {1} 个可访问窗口", jvm.JvmId, windows.Length));

                        foreach (var window in windows)
                        {
                            WriteLog(string.Format("窗口 HWND: {0}", window.Hwnd.ToString("X")));
                            TraverseAccessibleNode(window.AccessibleContextNode, 0, "WINDOW");
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("遍历JVM窗口时发生异常: {0}", ex.Message));
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("JVM枚举时发生异常: {0}", ex.Message));
            }
        }

        static void TraverseAccessibleNode(AccessibleContextNode node, int depth, string path)
        {
            if (node == null)
            {
                WriteLog(string.Format("{0}节点为空", GetIndent(depth)));
                return;
            }

            try
            {
                var info = node.GetInfo();
                string nodeInfo = string.Format("{0}[{1}] {2} ({3})", 
                    GetIndent(depth), path, info.name, info.role);
                
                WriteLog(nodeInfo);
                discoveredElements.Add(nodeInfo);

                // 统计元素类型
                string role = info.role ?? "Unknown";
                if (elementStats.ContainsKey(role))
                    elementStats[role]++;
                else
                    elementStats[role] = 1;

                // 记录关键属性
                if (!string.IsNullOrEmpty(info.description))
                {
                    WriteLog(string.Format("{0}  描述: {1}", GetIndent(depth), info.description));
                }

                if (!string.IsNullOrEmpty(info.states))
                {
                    WriteLog(string.Format("{0}  状态: {1}", GetIndent(depth), info.states));
                }

                // 遍历子节点（限制深度防止无限递归）
                if (depth < 10 && info.childrenCount > 0)
                {
                    WriteLog(string.Format("{0}  子节点数量: {1}", GetIndent(depth), info.childrenCount));
                    
                    for (int i = 0; i < Math.Min(info.childrenCount, 50); i++) // 限制子节点数量
                    {
                        try
                        {
                            var child = node.GetChild(i);
                            if (child != null)
                            {
                                TraverseAccessibleNode(child, depth + 1, string.Format("{0}[{1}]", path, i));
                            }
                        }
                        catch (Exception ex)
                        {
                            WriteLog(string.Format("{0}  获取子节点{1}失败: {2}", GetIndent(depth), i, ex.Message));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("{0}遍历节点时发生异常: {1}", GetIndent(depth), ex.Message));
            }
        }

        static string GetIndent(int depth)
        {
            return new string(' ', depth * 2);
        }

        static void GenerateStatisticsReport()
        {
            WriteLog(string.Format("总计发现元素: {0} 个", discoveredElements.Count));
            WriteLog("\n元素类型统计:");
            
            foreach (var kvp in elementStats)
            {
                WriteLog(string.Format("  {0}: {1} 个", kvp.Key, kvp.Value));
            }

            if (discoveredElements.Count > 0)
            {
                WriteLog("\n✅ 遍历成功完成");
                WriteLog("建议: 可以使用发现的元素信息进行进一步的自动化操作");
            }
            else
            {
                WriteLog("\n❌ 未发现任何可访问元素");
                WriteLog("可能原因:");
                WriteLog("1. Java应用程序未启用可访问性支持");
                WriteLog("2. Access Bridge配置不正确");
                WriteLog("3. 应用程序使用了自定义UI框架");
                WriteLog("4. 权限不足或进程架构不匹配");
            }
        }

        static void CheckAccessBridge()
        {
            // 检查Access Bridge相关文件
            string systemPath = Environment.GetFolderPath(Environment.SpecialFolder.System);
            string[] bridgeFiles = {
                "WindowsAccessBridge-32.dll",
                "WindowsAccessBridge-64.dll",
                "JAWTAccessBridge-32.dll",
                "JAWTAccessBridge-64.dll"
            };

            foreach (string file in bridgeFiles)
            {
                string fullPath = Path.Combine(systemPath, file);
                bool exists = File.Exists(fullPath);
                WriteLog(string.Format("Access Bridge文件 {0}: {1}", file, exists ? "存在" : "不存在"));
                if (exists)
                {
                    FileInfo fi = new FileInfo(fullPath);
                    WriteLog(string.Format("  - 大小: {0} bytes, 修改时间: {1}", fi.Length, fi.LastWriteTime));
                }
            }
        }

        static List<Process> FindJavaProcesses()
        {
            var javaProcesses = new List<Process>();
            var allProcesses = Process.GetProcesses();
            
            WriteLog(string.Format("系统中运行的进程总数: {0}", allProcesses.Length));

            foreach (Process process in allProcesses)
            {
                try
                {
                    string processName = process.ProcessName.ToLower();
                    if (processName.Contains("java") || processName.Contains("javaw") || 
                        processName.Contains("nc") || processName.Contains("eclipse"))
                    {
                        javaProcesses.Add(process);
                        WriteLog(string.Format("发现疑似Java进程: {0} (PID: {1})", process.ProcessName, process.Id));
                        
                        try
                        {
                            WriteLog(string.Format("  - 进程路径: {0}", process.MainModule.FileName));
                            WriteLog(string.Format("  - 内存使用: {0} MB", process.WorkingSet64 / 1024 / 1024));
                            WriteLog(string.Format("  - 启动时间: {0}", process.StartTime));
                        }
                        catch (Exception ex)
                        {
                            WriteLog(string.Format("  - 获取进程详细信息失败: {0}", ex.Message));
                        }
                    }
                }
                catch (Exception ex)
                {
                    WriteLog(string.Format("检查进程 {0} 时发生异常: {1}", process.Id, ex.Message));
                }
            }

            WriteLog(string.Format("总共发现 {0} 个疑似Java进程", javaProcesses.Count));
            return javaProcesses;
        }

        static List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            EnumWindows((hWnd, lParam) =>
            {
                if (IsWindowVisible(hWnd))
                {
                    var sb = new StringBuilder(256);
                    GetWindowText(hWnd, sb, sb.Capacity);
                    string windowTitle = sb.ToString();

                    uint processId;
                    GetWindowThreadProcessId(hWnd, out processId);

                    try
                    {
                        Process process = Process.GetProcessById((int)processId);
                        string processName = process.ProcessName.ToLower();

                        // 检查是否为Java窗口
                        bool isJavaWindow = processName.Contains("java") || 
                                          processName.Contains("javaw") ||
                                          windowTitle.ToLower().Contains("nc") ||
                                          windowTitle.ToLower().Contains("java");

                        if (isJavaWindow)
                        {
                            javaWindows.Add(hWnd);
                            WriteLog(string.Format("发现Java窗口: {0} (HWND: {1}, PID: {2})", 
                                windowTitle, hWnd.ToString("X"), processId));
                        }
                        else if (!string.IsNullOrEmpty(windowTitle))
                        {
                            // 记录所有可见窗口供参考
                            WriteLog(string.Format("普通窗口: {0} (进程: {1})", windowTitle, processName));
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("检查窗口 {0} 时发生异常: {1}", hWnd.ToString("X"), ex.Message));
                    }
                }
                return true;
            }, IntPtr.Zero);

            WriteLog(string.Format("总共发现 {0} 个Java窗口", javaWindows.Count));
            return javaWindows;
        }

        static bool InitializeAccessBridge()
        {
            try
            {
                WriteLog("正在初始化Access Bridge...");
                accessBridge = new AccessBridge();
                
                WriteLog("Access Bridge对象创建成功");
                
                WriteLog("✅ Access Bridge初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                WriteLog("❌ Access Bridge初始化失败: " + ex.ToString());
                return false;
            }
        }

        static void PerformComprehensiveTraversal(List<IntPtr> javaWindows)
        {
            if (javaWindows.Count == 0)
            {
                WriteLog("❌ 没有发现Java窗口，无法进行遍历");
                return;
            }

            WriteLog(string.Format("开始遍历 {0} 个Java窗口...", javaWindows.Count));

            foreach (IntPtr hWnd in javaWindows)
            {
                WriteLog(string.Format("\n--- 遍历窗口: {0} ---", hWnd.ToString("X")));
                TraverseJavaWindow(hWnd);
            }

            // 尝试通过JVM枚举方式
            WriteLog("\n--- 尝试JVM枚举方式 ---");
            TryJvmEnumeration();
        }

        static void TraverseJavaWindow(IntPtr hWnd)
        {
            try
            {
                AccessibleJvm jvm;
                AccessibleContextNode rootContext;

                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out jvm, out rootContext))
                {
                    WriteLog(string.Format("✅ 成功获取窗口的根可访问上下文"));
                    WriteLog(string.Format("JVM ID: {0}", jvm.JvmId));
                    
                    // 遍历根上下文
                    TraverseAccessibleNode(rootContext, 0, "ROOT");
                }
                else
                {
                    WriteLog("❌ 无法从窗口获取可访问上下文");
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("遍历窗口时发生异常: {0}", ex.Message));
            }
        }

        static void TryJvmEnumeration()
        {
            try
            {
                var jvms = accessBridge.GetJavaVirtualMachines();
                WriteLog(string.Format("发现 {0} 个Java虚拟机", jvms.Length));

                foreach (var jvm in jvms)
                {
                    WriteLog(string.Format("JVM ID: {0}", jvm.JvmId));
                    
                    try
                    {
                        var windows = jvm.GetAccessibleWindows();
                        WriteLog(string.Format("JVM {0} 包含 {1} 个可访问窗口", jvm.JvmId, windows.Length));

                        foreach (var window in windows)
                        {
                            WriteLog(string.Format("窗口 HWND: {0}", window.Hwnd.ToString("X")));
                            TraverseAccessibleNode(window.AccessibleContextNode, 0, "WINDOW");
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("遍历JVM窗口时发生异常: {0}", ex.Message));
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("JVM枚举时发生异常: {0}", ex.Message));
            }
        }

        static void TraverseAccessibleNode(AccessibleContextNode node, int depth, string path)
        {
            if (node == null)
            {
                WriteLog(string.Format("{0}节点为空", GetIndent(depth)));
                return;
            }

            try
            {
                var info = node.GetInfo();
                string nodeInfo = string.Format("{0}[{1}] {2} ({3})", 
                    GetIndent(depth), path, info.name, info.role);
                
                WriteLog(nodeInfo);
                discoveredElements.Add(nodeInfo);

                // 统计元素类型
                string role = info.role ?? "Unknown";
                if (elementStats.ContainsKey(role))
                    elementStats[role]++;
                else
                    elementStats[role] = 1;

                // 记录关键属性
                if (!string.IsNullOrEmpty(info.description))
                {
                    WriteLog(string.Format("{0}  描述: {1}", GetIndent(depth), info.description));
                }

                if (!string.IsNullOrEmpty(info.states))
                {
                    WriteLog(string.Format("{0}  状态: {1}", GetIndent(depth), info.states));
                }

                // 遍历子节点（限制深度防止无限递归）
                if (depth < 10 && info.childrenCount > 0)
                {
                    WriteLog(string.Format("{0}  子节点数量: {1}", GetIndent(depth), info.childrenCount));
                    
                    for (int i = 0; i < Math.Min(info.childrenCount, 50); i++) // 限制子节点数量
                    {
                        try
                        {
                            var child = node.GetChild(i);
                            if (child != null)
                            {
                                TraverseAccessibleNode(child, depth + 1, string.Format("{0}[{1}]", path, i));
                            }
                        }
                        catch (Exception ex)
                        {
                            WriteLog(string.Format("{0}  获取子节点{1}失败: {2}", GetIndent(depth), i, ex.Message));
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("{0}遍历节点时发生异常: {1}", GetIndent(depth), ex.Message));
            }
        }

        static string GetIndent(int depth)
        {
            return new string(' ', depth * 2);
        }

        static void GenerateStatisticsReport()
        {
            WriteLog(string.Format("总计发现元素: {0} 个", discoveredElements.Count));
            WriteLog("\n元素类型统计:");
            
            foreach (var kvp in elementStats)
            {
                WriteLog(string.Format("  {0}: {1} 个", kvp.Key, kvp.Value));
            }

            if (discoveredElements.Count > 0)
            {
                WriteLog("\n✅ 遍历成功完成");
                WriteLog("建议: 可以使用发现的元素信息进行进一步的自动化操作");
            }
            else
            {
                WriteLog("\n❌ 未发现任何可访问元素");
                WriteLog("可能原因:");
                WriteLog("1. Java应用程序未启用可访问性支持");
                WriteLog("2. Access Bridge配置不正确");
                WriteLog("3. 应用程序使用了自定义UI框架");
                WriteLog("4. 权限不足或进程架构不匹配");
            }
        }

        static void WriteLog(string message)
        {
            string logEntry = string.Format("[{0}] {1}", DateTime.Now.ToString("HH:mm:ss.fff"), message);
            Console.WriteLine(message);
            
            try
            {
                File.AppendAllText(logFileName, logEntry + Environment.NewLine, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Console.WriteLine("写入日志失败: " + ex.Message);
            }
        }
    }
}
