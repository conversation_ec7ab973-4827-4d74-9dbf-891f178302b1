using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace TestAccessibleNodeModel
{
    public partial class TestForm : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeView;
        private TextBox logTextBox;
        private Button refreshButton;
        private Button compareButton;

        public TestForm()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        private void InitializeComponent()
        {
            this.Text = "AccessibleNodeModel 增强测试";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            var splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 500;

            // 树视图
            treeView = new TreeView();
            treeView.Dock = DockStyle.Fill;
            treeView.AfterSelect += TreeView_AfterSelect;

            // 日志文本框
            logTextBox = new TextBox();
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.ReadOnly = true;
            logTextBox.Font = new Font("Consolas", 9);

            // 按钮面板
            var buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Top;
            buttonPanel.Height = 40;

            refreshButton = new Button();
            refreshButton.Text = "刷新";
            refreshButton.Location = new Point(10, 8);
            refreshButton.Size = new Size(80, 25);
            refreshButton.Click += RefreshButton_Click;

            compareButton = new Button();
            compareButton.Text = "对比测试";
            compareButton.Location = new Point(100, 8);
            compareButton.Size = new Size(80, 25);
            compareButton.Click += CompareButton_Click;

            buttonPanel.Controls.Add(refreshButton);
            buttonPanel.Controls.Add(compareButton);

            splitContainer.Panel1.Controls.Add(treeView);
            splitContainer.Panel2.Controls.Add(logTextBox);

            this.Controls.Add(splitContainer);
            this.Controls.Add(buttonPanel);
        }

        private void InitializeAccessBridge()
        {
            try
            {
                accessBridge = new AccessBridge();
                LogMessage("Access Bridge 初始化成功");
                RefreshJavaApplications();
            }
            catch (Exception ex)
            {
                LogMessage("Access Bridge 初始化失败: " + ex.Message);
                MessageBox.Show("无法初始化 Access Bridge: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            RefreshJavaApplications();
        }

        private void CompareButton_Click(object sender, EventArgs e)
        {
            if (treeView.SelectedNode != null && treeView.SelectedNode.Tag is AccessibleContextNode)
            {
                CompareChildrenMethods((AccessibleContextNode)treeView.SelectedNode.Tag);
            }
            else
            {
                LogMessage("请先选择一个Java组件进行对比测试");
            }
        }

        private void RefreshJavaApplications()
        {
            try
            {
                treeView.Nodes.Clear();
                LogMessage("开始搜索Java应用程序...");

                var javaWindows = FindJavaWindows();
                LogMessage("发现 " + javaWindows.Count + " 个Java窗口");

                foreach (var hWnd in javaWindows)
                {
                    CreateJavaWindowNode(hWnd);
                }

                LogMessage("Java应用程序搜索完成");
            }
            catch (Exception ex)
            {
                LogMessage("搜索Java应用程序时出错: " + ex.Message);
            }
        }

        private List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            EnumWindows(delegate(IntPtr hWnd, IntPtr lParam)
            {
                if (IsWindowVisible(hWnd) && accessBridge.Functions.IsJavaWindow(hWnd))
                {
                    javaWindows.Add(hWnd);
                }
                return true;
            }, IntPtr.Zero);
            
            return javaWindows;
        }

        private void CreateJavaWindowNode(IntPtr hWnd)
        {
            try
            {
                var windowTitle = GetWindowTitle(hWnd);
                
                int vmId;
                JavaObjectHandle ac;
                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out vmId, out ac))
                {
                    var rootNode = new TreeNode("Java窗口: " + windowTitle);
                    
                    var accessibleWindow = accessBridge.CreateAccessibleWindow(hWnd);
                    if (accessibleWindow != null)
                    {
                        rootNode.Tag = accessibleWindow;

                        // 使用增强的子组件获取方法（模拟AccessibleNodeModel的行为）
                        AddChildrenUsingEnhancedMethod(rootNode, accessibleWindow, 0, 3);

                        treeView.Nodes.Add(rootNode);
                        rootNode.Expand();
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("错误: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                treeView.Nodes.Add(errorNode);
            }
        }

        /// <summary>
        /// 模拟修改后的AccessibleNodeModel.AddChildren方法
        /// </summary>
        private void AddChildrenUsingEnhancedMethod(TreeNode parentNode, AccessibleNode accessibleNode, int depth, int maxDepth)
        {
            if (depth >= maxDepth) return;

            try
            {
                IEnumerable<AccessibleNode> children;

                // 模拟GetChildrenWithEnhancement方法
                if (accessibleNode is AccessibleContextNode contextNode)
                {
                    try
                    {
                        var enhancedChildren = contextNode.GetChildrenEnhanced().ToList();
                        if (enhancedChildren.Count > 0)
                        {
                            children = enhancedChildren;
                            LogMessage("使用增强方法获取到 " + enhancedChildren.Count + " 个子组件");
                        }
                        else
                        {
                            children = accessibleNode.GetChildren();
                            LogMessage("增强方法未找到子组件，使用标准方法");
                        }
                    }
                    catch (Exception)
                    {
                        children = accessibleNode.GetChildren();
                        LogMessage("增强方法失败，回退到标准方法");
                    }
                }
                else
                {
                    children = accessibleNode.GetChildren();
                }

                var childList = children.ToList();
                foreach (var child in childList.Take(10)) // 限制显示数量
                {
                    try
                    {
                        if (child is AccessibleContextNode childContext)
                        {
                            var childInfo = childContext.GetInfo();
                            var childNode = new TreeNode((childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "]");
                            childNode.Tag = child;

                            parentNode.Nodes.Add(childNode);

                            if (childInfo.childrenCount > 0)
                            {
                                AddChildrenUsingEnhancedMethod(childNode, child, depth + 1, maxDepth);
                            }
                        }
                        else
                        {
                            var childNode = new TreeNode("未知类型节点");
                            childNode.Tag = child;
                            parentNode.Nodes.Add(childNode);
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorNode = new TreeNode("子组件错误: " + ex.Message);
                        errorNode.ForeColor = Color.Red;
                        parentNode.Nodes.Add(errorNode);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("获取子组件失败: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void CompareChildrenMethods(AccessibleContextNode contextNode)
        {
            try
            {
                LogMessage("=== 开始对比测试 ===");
                var info = contextNode.GetInfo();
                LogMessage("测试组件: " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");

                // 测试标准方法
                int standardCount = 0;
                try
                {
                    var standardChildren = contextNode.GetChildren().ToList();
                    standardCount = standardChildren.Count;
                    LogMessage("标准方法 GetChildren(): " + standardCount + " 个子组件");
                }
                catch (Exception ex)
                {
                    LogMessage("标准方法失败: " + ex.Message);
                }

                // 测试增强方法
                int enhancedCount = 0;
                try
                {
                    var enhancedChildren = contextNode.GetChildrenEnhanced().ToList();
                    enhancedCount = enhancedChildren.Count;
                    LogMessage("增强方法 GetChildrenEnhanced(): " + enhancedCount + " 个子组件");
                }
                catch (Exception ex)
                {
                    LogMessage("增强方法失败: " + ex.Message);
                }

                // 对比结果
                if (enhancedCount > standardCount)
                {
                    LogMessage("✅ 增强方法发现了更多子组件！(+" + (enhancedCount - standardCount) + ")");
                }
                else if (enhancedCount == standardCount)
                {
                    LogMessage("ℹ️ 两种方法发现的子组件数量相同");
                }
                else
                {
                    LogMessage("⚠️ 标准方法发现了更多子组件");
                }

                LogMessage("=== 对比测试完成 ===");
            }
            catch (Exception ex)
            {
                LogMessage("对比测试过程中出错: " + ex.Message);
            }
        }

        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag is AccessibleContextNode contextNode)
            {
                var info = contextNode.GetInfo();
                LogMessage("选中: " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "] - 子组件数: " + info.childrenCount);
            }
        }

        private string GetWindowTitle(IntPtr hWnd)
        {
            var sb = new StringBuilder(256);
            GetWindowText(hWnd, sb, sb.Capacity);
            return sb.ToString();
        }

        private void LogMessage(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            logTextBox.AppendText("[" + DateTime.Now.ToString("HH:mm:ss") + "] " + message + "\r\n");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            if (accessBridge != null)
                accessBridge.Dispose();
            base.OnFormClosed(e);
        }

        #region Windows API声明

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        #endregion
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestForm());
        }
    }
}
