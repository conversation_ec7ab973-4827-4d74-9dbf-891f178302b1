// Copyright 2016 Google Inc. All Rights Reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
//     http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using System.Collections;
using System.Collections.Generic;
using System.Linq;

namespace WindowsAccessBridgeInterop {
  /// <summary>
  /// Represents a path from a root node to a leaf node.
  /// </summary>
  public class Path<T> : IEnumerable<T> {
    private readonly List<T> _items = new List<T>();

    public T Root {
      get { return _items.FirstOrDefault(); }
    }

    public T Leaf {
      get { return _items.LastOrDefault(); }
    }

    public int Count {
      get { return _items.Count; }
    }

    public void AddRoot(T item) {
      //TODO: Make this more efficient (N^2 when adding parents).
      _items.Insert(0, item);
    }

    public void AddLeaf(T item) {
      _items.Add(item);
    }

    public void RemoveLeaf() {
      _items.RemoveAt(_items.Count - 1);
    }

    public PathCursor<T> CreateCursor() {
      return new PathCursor<T>(_items, 0, _items.Count);
    }

    public IEnumerator<T> GetEnumerator() {
      return _items.GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator() {
      return GetEnumerator();
    }
  }
}