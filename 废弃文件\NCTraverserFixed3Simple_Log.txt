﻿[14:13:39.618] === NC遍历器修复版本 (简化直接调用) ===
[14:13:39.620] 时间: 2025-07-15 14:13:39
[14:13:39.620] 版本: Fixed3Simple - 直接P/Invoke调用版本
[14:13:39.620] 日志文件: D:\Code\access-bridge-explorer-master\NCTraverserFixed3Simple_Log.txt
[14:13:39.620] 
[14:13:39.621] === 系统环境检查 ===
[14:13:39.621] 操作系统: Microsoft Windows NT 6.2.9200.0
[14:13:39.621] 处理器架构: 24 cores, 64-bit OS
[14:13:39.621] 进程架构: 64-bit process
[14:13:39.621] .NET Framework: 4.0.30319.42000
[14:13:39.624] --- Access Bridge DLL检查 ---
[14:13:39.625] ✅ 找到 accessbridge2_0_2\WindowsAccessBridge-64.dll - 版本: 2, 0, 6, 0
[14:13:39.625] ✅ 找到 accessbridge2_0_2\WindowsAccessBridge.dll - 版本: 2, 0, 6, 0
[14:13:39.625] 
[14:13:39.626] === 初始化Access Bridge（JavaFerret方式）===
[14:13:39.626] 调用 Windows_run() - 这是JavaFerret使用的初始化方法
[14:13:39.638] ✅ Windows_run() 调用成功
[14:13:40.653] 等待Access Bridge初始化完成...
[14:13:40.653] ✅ Access Bridge初始化成功
[14:13:40.654] === 发现Java应用程序（JavaFerret方式）===
[14:13:40.654] 正在枚举所有窗口并检查Java窗口...
[14:13:40.660] ⚠️ 检查窗口 262270 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.660] ⚠️ 检查窗口 65830 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.660] ⚠️ 检查窗口 65834 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.660] ⚠️ 检查窗口 66026 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.660] ⚠️ 检查窗口 131516 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.660] ⚠️ 检查窗口 65880 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.660] ⚠️ 检查窗口 65876 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.661] ⚠️ 检查窗口 65872 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.661] ⚠️ 检查窗口 65868 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.661] ⚠️ 检查窗口 65852 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.661] ⚠️ 检查窗口 65888 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.661] ⚠️ 检查窗口 65854 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.662] ⚠️ 检查窗口 66148 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.662] ⚠️ 检查窗口 66184 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.662] ⚠️ 检查窗口 131706 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.662] ⚠️ 检查窗口 131626 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.662] ⚠️ 检查窗口 65908 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.663] ⚠️ 检查窗口 65906 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.663] ⚠️ 检查窗口 65886 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.663] ⚠️ 检查窗口 65890 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.663] ⚠️ 检查窗口 131360 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.663] ⚠️ 检查窗口 722606 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.663] ⚠️ 检查窗口 65922 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.664] ⚠️ 检查窗口 263958 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.664] ⚠️ 检查窗口 329474 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.664] ⚠️ 检查窗口 66502 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.664] ⚠️ 检查窗口 394884 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.664] ⚠️ 检查窗口 132662 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66572 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66574 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66566 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66568 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66560 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66562 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.665] ⚠️ 检查窗口 66548 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.666] ⚠️ 检查窗口 66550 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.666] ⚠️ 检查窗口 66542 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.666] ⚠️ 检查窗口 66544 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.666] ⚠️ 检查窗口 66536 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.666] ⚠️ 检查窗口 66538 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.667] ⚠️ 检查窗口 66530 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.667] ⚠️ 检查窗口 66532 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.667] ⚠️ 检查窗口 66524 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.667] ⚠️ 检查窗口 66526 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.667] ⚠️ 检查窗口 66490 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.667] ⚠️ 检查窗口 66414 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66416 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66408 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66410 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66402 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66404 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66378 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.668] ⚠️ 检查窗口 66380 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.669] ⚠️ 检查窗口 66396 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.669] ⚠️ 检查窗口 66374 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.669] ⚠️ 检查窗口 131350 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.669] ⚠️ 检查窗口 66372 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.669] ⚠️ 检查窗口 66424 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.670] ⚠️ 检查窗口 66430 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.670] ⚠️ 检查窗口 66434 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.670] ⚠️ 检查窗口 66436 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.670] ⚠️ 检查窗口 66440 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.670] ⚠️ 检查窗口 66442 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.671] ⚠️ 检查窗口 329576 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.671] ⚠️ 检查窗口 723092 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.671] ⚠️ 检查窗口 395410 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.671] ⚠️ 检查窗口 1179730 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.671] ⚠️ 检查窗口 657326 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.671] ⚠️ 检查窗口 460776 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.672] ⚠️ 检查窗口 264288 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.672] ⚠️ 检查窗口 133114 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.672] ⚠️ 检查窗口 133010 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.672] ⚠️ 检查窗口 198336 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.672] ⚠️ 检查窗口 198334 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.673] ⚠️ 检查窗口 459844 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.673] ⚠️ 检查窗口 198262 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.673] ⚠️ 检查窗口 198250 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.673] ⚠️ 检查窗口 198260 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.673] ⚠️ 检查窗口 1049860 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.673] ⚠️ 检查窗口 132906 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.674] ⚠️ 检查窗口 132902 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.674] ⚠️ 检查窗口 8193692 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.674] ⚠️ 检查窗口 198404 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.674] ⚠️ 检查窗口 395234 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.674] ⚠️ 检查窗口 327710 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.675] ⚠️ 检查窗口 395210 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.675] ⚠️ 检查窗口 2229610 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.675] ⚠️ 检查窗口 1246664 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.675] ⚠️ 检查窗口 1312282 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.675] ⚠️ 检查窗口 3671688 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.675] ⚠️ 检查窗口 3606550 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.676] ⚠️ 检查窗口 198798 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.676] ⚠️ 检查窗口 263640 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.676] ⚠️ 检查窗口 198222 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.676] ⚠️ 检查窗口 132688 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.676] ⚠️ 检查窗口 787484 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.676] ⚠️ 检查窗口 394474 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.677] ⚠️ 检查窗口 197998 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.677] ⚠️ 检查窗口 197996 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.677] ⚠️ 检查窗口 918264 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.677] ⚠️ 检查窗口 919492 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.677] ⚠️ 检查窗口 460692 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.677] ⚠️ 检查窗口 14812768 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.678] ⚠️ 检查窗口 1771352 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.678] ⚠️ 检查窗口 263738 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.678] ⚠️ 检查窗口 788422 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.678] ⚠️ 检查窗口 329734 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.678] ⚠️ 检查窗口 1508978 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.678] ⚠️ 检查窗口 460646 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 3081758 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 722116 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 1705154 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 66642 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 722266 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 264204 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.679] ⚠️ 检查窗口 526028 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.680] ⚠️ 检查窗口 1640008 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.680] ⚠️ 检查窗口 788080 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.680] ⚠️ 检查窗口 1902090 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.680] ⚠️ 检查窗口 329380 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.680] ⚠️ 检查窗口 4064800 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.681] ⚠️ 检查窗口 65956 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.681] ⚠️ 检查窗口 395288 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.681] ⚠️ 检查窗口 1049742 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.681] ⚠️ 检查窗口 591830 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.681] ⚠️ 检查窗口 264284 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.681] ⚠️ 检查窗口 591554 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 525928 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 329754 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 525944 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 1246738 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 984514 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 1050008 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 1771136 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 66868 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 2295238 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 133006 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 395516 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.682] ⚠️ 检查窗口 264342 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 264404 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 132864 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 198414 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 198418 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 198436 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 198390 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 132872 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 132862 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.683] ⚠️ 检查窗口 132892 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.684] ⚠️ 检查窗口 132916 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.684] ⚠️ 检查窗口 460288 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.684] ⚠️ 检查窗口 460150 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.684] ⚠️ 检查窗口 722506 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.684] ⚠️ 检查窗口 198050 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.684] ⚠️ 检查窗口 198044 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.685] ⚠️ 检查窗口 394678 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.685] ⚠️ 检查窗口 394988 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.685] ⚠️ 检查窗口 394578 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.685] ⚠️ 检查窗口 263536 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.685] ⚠️ 检查窗口 1114612 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.685] ⚠️ 检查窗口 66476 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.686] ⚠️ 检查窗口 66844 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.686] ⚠️ 检查窗口 918054 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.686] ⚠️ 检查窗口 66832 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.686] ⚠️ 检查窗口 197890 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.686] ⚠️ 检查窗口 263414 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.687] ⚠️ 检查窗口 394462 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.687] ⚠️ 检查窗口 591052 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.687] ⚠️ 检查窗口 591090 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.687] ⚠️ 检查窗口 197764 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.687] ⚠️ 检查窗口 197812 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.687] ⚠️ 检查窗口 393436 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.688] ⚠️ 检查窗口 66688 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.688] ⚠️ 检查窗口 66680 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.688] ⚠️ 检查窗口 66658 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.688] ⚠️ 检查窗口 131578 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.688] ⚠️ 检查窗口 66622 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.689] ⚠️ 检查窗口 66620 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.689] ⚠️ 检查窗口 66618 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.689] ⚠️ 检查窗口 66612 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.689] ⚠️ 检查窗口 131610 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.690] ⚠️ 检查窗口 132112 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.690] ⚠️ 检查窗口 66570 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.690] ⚠️ 检查窗口 66564 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.690] ⚠️ 检查窗口 66558 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.690] ⚠️ 检查窗口 66556 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.691] ⚠️ 检查窗口 66554 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.691] ⚠️ 检查窗口 66552 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.691] ⚠️ 检查窗口 66546 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.691] ⚠️ 检查窗口 66540 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.691] ⚠️ 检查窗口 66534 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.692] ⚠️ 检查窗口 66528 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.692] ⚠️ 检查窗口 66522 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.692] ⚠️ 检查窗口 132016 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.692] ⚠️ 检查窗口 66446 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.692] ⚠️ 检查窗口 66398 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.693] ⚠️ 检查窗口 66504 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.693] ⚠️ 检查窗口 66584 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.693] ⚠️ 检查窗口 66518 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.693] ⚠️ 检查窗口 66516 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.693] ⚠️ 检查窗口 66510 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.693] ⚠️ 检查窗口 66508 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.694] ⚠️ 检查窗口 66494 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.694] ⚠️ 检查窗口 66492 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.694] ⚠️ 检查窗口 66500 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.694] ⚠️ 检查窗口 66498 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.694] ⚠️ 检查窗口 66486 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.695] ⚠️ 检查窗口 66484 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.695] ⚠️ 检查窗口 66472 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.695] ⚠️ 检查窗口 66454 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.695] ⚠️ 检查窗口 131988 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.696] ⚠️ 检查窗口 66450 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.696] ⚠️ 检查窗口 66448 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.696] ⚠️ 检查窗口 66438 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.696] ⚠️ 检查窗口 66432 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.696] ⚠️ 检查窗口 66422 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.697] ⚠️ 检查窗口 66412 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.697] ⚠️ 检查窗口 66406 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.697] ⚠️ 检查窗口 66400 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.697] ⚠️ 检查窗口 66376 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.697] ⚠️ 检查窗口 131894 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.697] ⚠️ 检查窗口 459582 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.698] ⚠️ 检查窗口 1311548 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.698] ⚠️ 检查窗口 655940 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.698] ⚠️ 检查窗口 131886 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.698] ⚠️ 检查窗口 197310 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.698] ⚠️ 检查窗口 328252 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 327752 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 393302 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 327790 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 131420 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 3997790 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 131510 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.699] ⚠️ 检查窗口 262772 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.700] ⚠️ 检查窗口 459372 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.700] ⚠️ 检查窗口 66096 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.700] ⚠️ 检查窗口 131628 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.700] ⚠️ 检查窗口 66016 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.700] ⚠️ 检查窗口 131478 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.701] ⚠️ 检查窗口 131600 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.701] ⚠️ 检查窗口 66018 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.701] ⚠️ 检查窗口 66012 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.701] ⚠️ 检查窗口 65986 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.702] ⚠️ 检查窗口 65984 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.702] ⚠️ 检查窗口 131512 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.702] ⚠️ 检查窗口 65950 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.703] ⚠️ 检查窗口 65940 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.703] ⚠️ 检查窗口 65936 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.703] ⚠️ 检查窗口 65926 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.703] ⚠️ 检查窗口 66218 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.703] ⚠️ 检查窗口 524358 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.703] ⚠️ 检查窗口 131196 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.704] ⚠️ 检查窗口 66188 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.704] ⚠️ 检查窗口 65806 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.704] ⚠️ 检查窗口 65802 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.704] ⚠️ 检查窗口 65796 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.704] ⚠️ 检查窗口 65794 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.705] ⚠️ 检查窗口 131326 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.705] ⚠️ 检查窗口 131302 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.705] ⚠️ 检查窗口 131318 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.705] ⚠️ 检查窗口 65738 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.705] ⚠️ 检查窗口 65734 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.705] ⚠️ 检查窗口 65686 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.706] ⚠️ 检查窗口 131220 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.706] ⚠️ 检查窗口 65598 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.706] ⚠️ 检查窗口 65580 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.706] ⚠️ 检查窗口 65710 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.706] ⚠️ 检查窗口 853642 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.706] ⚠️ 检查窗口 263914 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 131450 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 131746 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 3080256 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 66028 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 66150 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 65910 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.707] ⚠️ 检查窗口 65826 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 4589124 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 66456 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 66586 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 264324 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 4327246 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 2295846 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.708] ⚠️ 检查窗口 7604038 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.709] ⚠️ 检查窗口 591816 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.709] ⚠️ 检查窗口 722746 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.709] ⚠️ 检查窗口 1050062 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.709] ⚠️ 检查窗口 198460 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.709] ⚠️ 检查窗口 132920 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.709] ⚠️ 检查窗口 132846 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.710] ⚠️ 检查窗口 132884 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.710] ⚠️ 检查窗口 262282 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.710] ⚠️ 检查窗口 393240 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.710] ⚠️ 检查窗口 2098754 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.710] ⚠️ 检查窗口 788412 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.710] ⚠️ 检查窗口 1115718 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.711] ⚠️ 检查窗口 1247390 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.711] ⚠️ 检查窗口 263672 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.711] ⚠️ 检查窗口 1377470 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.711] ⚠️ 检查窗口 526172 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 1968080 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 526264 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 853580 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 329388 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 656524 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 1181268 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.712] ⚠️ 检查窗口 66650 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 66614 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 264222 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 1508820 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 329252 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 66602 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 65952 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 460486 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 526246 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 591956 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.713] ⚠️ 检查窗口 1574546 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 525924 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 329926 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 1181266 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 66876 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 787628 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 395514 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 327708 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 198426 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 198440 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.714] ⚠️ 检查窗口 852324 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 591088 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 197898 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 66478 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 66846 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 66834 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 262228 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 590076 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 590366 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.715] ⚠️ 检查窗口 197816 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 197810 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 328914 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 66690 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 66682 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 66660 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 66624 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 131606 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 66578 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.716] ⚠️ 检查窗口 66394 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 197442 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 66474 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 66470 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 66458 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 66460 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 131890 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 131904 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 196738 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 131884 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.717] ⚠️ 检查窗口 262714 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 131742 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 131348 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 131352 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 66176 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 4850074 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 65978 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 65928 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 66220 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 65808 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 65804 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.718] ⚠️ 检查窗口 65800 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 65798 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 65792 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 196750 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 131322 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 65688 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 132628 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 591252 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 65958 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] ⚠️ 检查窗口 65736 时出错: 无法在 DLL“WindowsAccessBridge-64.dll”中找到名为“IsJavaWindow”的入口点。
[14:13:40.719] 发现 0 个Java窗口
[14:13:40.719] ❌ 没有发现运行中的Java应用程序
[14:13:40.720] 请确保:
[14:13:40.720] 1. 已启动Java应用程序（如用友NC）
[14:13:40.720] 2. Java Access Bridge已正确安装
[14:13:40.720] 3. 当前进程架构与Java进程架构匹配
[14:13:40.720] 
=== 程序执行完毕 ===
