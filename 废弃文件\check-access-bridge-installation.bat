@echo off
echo =================================================
echo Java Access Bridge 2.0.2 安装状态检查
echo =================================================
echo.

set JAVA8_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot
set JAVA8_JRE=%JAVA8_HOME%\jre
set AB_SOURCE=%~dp0accessbridge2_0_2

echo 检查时间: %date% %time%
echo.

echo === 1. Java环境检查 ===
if exist "%JAVA8_HOME%" (
    echo ✓ Java 8 JDK: %JAVA8_HOME%
    "%JAVA8_HOME%\bin\java.exe" -version 2>&1 | findstr /C:"1.8.0"
) else (
    echo ✗ Java 8 JDK未找到
)

if exist "%JAVA8_JRE%" (
    echo ✓ Java 8 JRE: %JAVA8_JRE%
) else (
    echo ✗ Java 8 JRE未找到
)
echo.

echo === 2. Windows系统目录检查 ===
echo C:\Windows\System32\:
if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
    for %%f in ("C:\Windows\System32\WindowsAccessBridge-64.dll") do (
        echo ✓ WindowsAccessBridge-64.dll - %%~zf bytes, %%~tf
    )
) else (
    echo ✗ WindowsAccessBridge-64.dll 缺失
)

echo C:\Windows\SysWOW64\:
if exist "C:\Windows\SysWOW64\WindowsAccessBridge-32.dll" (
    for %%f in ("C:\Windows\SysWOW64\WindowsAccessBridge-32.dll") do (
        echo ✓ WindowsAccessBridge-32.dll - %%~zf bytes, %%~tf
    )
) else (
    echo ✗ WindowsAccessBridge-32.dll 缺失
)
echo.

echo === 3. Java运行时目录检查 ===
echo %JAVA8_JRE%\bin\:
if exist "%JAVA8_JRE%\bin\JavaAccessBridge-64.dll" (
    for %%f in ("%JAVA8_JRE%\bin\JavaAccessBridge-64.dll") do (
        echo ✓ JavaAccessBridge-64.dll - %%~zf bytes, %%~tf
    )
) else (
    echo ✗ JavaAccessBridge-64.dll 缺失
)

if exist "%JAVA8_JRE%\bin\JAWTAccessBridge-64.dll" (
    for %%f in ("%JAVA8_JRE%\bin\JAWTAccessBridge-64.dll") do (
        echo ✓ JAWTAccessBridge-64.dll - %%~zf bytes, %%~tf
    )
) else (
    echo ✗ JAWTAccessBridge-64.dll 缺失
)

if exist "%JAVA8_JRE%\bin\WindowsAccessBridge-64.dll" (
    for %%f in ("%JAVA8_JRE%\bin\WindowsAccessBridge-64.dll") do (
        echo ✓ WindowsAccessBridge-64.dll (JRE copy) - %%~zf bytes, %%~tf
    )
) else (
    echo ℹ WindowsAccessBridge-64.dll (JRE copy) 不存在 (正常)
)
echo.

echo === 4. Java扩展库检查 ===
echo %JAVA8_JRE%\lib\ext\:
if exist "%JAVA8_JRE%\lib\ext\jaccess.jar" (
    for %%f in ("%JAVA8_JRE%\lib\ext\jaccess.jar") do (
        echo ✓ jaccess.jar - %%~zf bytes, %%~tf
    )
) else (
    echo ✗ jaccess.jar 缺失
)

if exist "%JAVA8_JRE%\lib\ext\access-bridge-64.jar" (
    for %%f in ("%JAVA8_JRE%\lib\ext\access-bridge-64.jar") do (
        echo ✓ access-bridge-64.jar - %%~zf bytes, %%~tf
    )
) else (
    echo ✗ access-bridge-64.jar 缺失
)
echo.

echo === 5. 配置文件检查 ===
echo %JAVA8_JRE%\lib\:
if exist "%JAVA8_JRE%\lib\accessibility.properties" (
    for %%f in ("%JAVA8_JRE%\lib\accessibility.properties") do (
        echo ✓ accessibility.properties - %%~zf bytes, %%~tf
    )
    echo 内容预览:
    type "%JAVA8_JRE%\lib\accessibility.properties"
) else (
    echo ✗ accessibility.properties 缺失
)
echo.

echo === 6. Access Bridge启用状态 ===
"%JAVA8_HOME%\bin\jabswitch.exe" -status 2>&1
echo.

echo === 7. 对比官方Access Bridge 2.0.2文件 ===
if exist "%AB_SOURCE%" (
    echo ✓ 官方Access Bridge 2.0.2源文件存在
    echo.
    echo 文件大小对比:
    
    if exist "%AB_SOURCE%\WindowsAccessBridge-64.dll" (
        for %%f in ("%AB_SOURCE%\WindowsAccessBridge-64.dll") do (
            set "official_size=%%~zf"
            echo   官方 WindowsAccessBridge-64.dll: %%~zf bytes
        )
    )
    
    if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
        for %%f in ("C:\Windows\System32\WindowsAccessBridge-64.dll") do (
            echo   系统 WindowsAccessBridge-64.dll: %%~zf bytes
        )
    )
    
    if exist "%AB_SOURCE%\JavaAccessBridge-64.dll" (
        for %%f in ("%AB_SOURCE%\JavaAccessBridge-64.dll") do (
            echo   官方 JavaAccessBridge-64.dll: %%~zf bytes
        )
    )
    
    if exist "%JAVA8_JRE%\bin\JavaAccessBridge-64.dll" (
        for %%f in ("%JAVA8_JRE%\bin\JavaAccessBridge-64.dll") do (
            echo   系统 JavaAccessBridge-64.dll: %%~zf bytes
        )
    )
) else (
    echo ✗ 官方Access Bridge 2.0.2源文件不存在: %AB_SOURCE%
)
echo.

echo === 8. 测试程序可用性 ===
if exist "%AB_SOURCE%\JavaFerret-64.exe" (
    echo ✓ JavaFerret-64.exe 可用
) else (
    echo ✗ JavaFerret-64.exe 不可用
)

if exist "%AB_SOURCE%\JavaMonkey-64.exe" (
    echo ✓ JavaMonkey-64.exe 可用
) else (
    echo ✗ JavaMonkey-64.exe 不可用
)
echo.

echo === 9. 综合评估 ===
set /a score=0

if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" set /a score+=1
if exist "C:\Windows\SysWOW64\WindowsAccessBridge-32.dll" set /a score+=1
if exist "%JAVA8_JRE%\bin\JavaAccessBridge-64.dll" set /a score+=1
if exist "%JAVA8_JRE%\bin\JAWTAccessBridge-64.dll" set /a score+=1
if exist "%JAVA8_JRE%\lib\ext\jaccess.jar" set /a score+=1
if exist "%JAVA8_JRE%\lib\ext\access-bridge-64.jar" set /a score+=1
if exist "%JAVA8_JRE%\lib\accessibility.properties" set /a score+=1

echo 安装完整度: %score%/7
if %score% GEQ 6 (
    echo ✅ Access Bridge安装基本完整
) else if %score% GEQ 4 (
    echo ⚠️ Access Bridge安装部分缺失，建议重新安装
) else (
    echo ❌ Access Bridge安装严重缺失，需要完整安装
)

echo.
echo === 建议操作 ===
if %score% LSS 6 (
    echo 1. 以管理员权限运行: install-access-bridge-complete.bat
    echo 2. 重启用友NC系统
    echo 3. 验证AccessBridgeExplorer功能
) else (
    echo 1. Access Bridge配置良好
    echo 2. 可以直接测试用友NC系统访问
    echo 3. 如有问题，检查用友NC启动参数
)

echo.
echo =================================================
pause 