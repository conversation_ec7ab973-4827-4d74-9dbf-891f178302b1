@echo off
echo ===============================================
echo Java Discovery Tool Enhanced - 增强版Java程序发现工具
echo Based on JavaFerret Deep Traversal Algorithm
echo ===============================================

echo.
echo Checking enhanced version...

REM Check if the enhanced tool exists
if not exist "JavaDiscoveryToolEnhanced.exe" (
    echo ERROR: JavaDiscoveryToolEnhanced.exe not found
    echo Please compile the enhanced version first
    pause
    exit /b 1
)

echo Enhanced version found: JavaDiscoveryToolEnhanced.exe
echo.

echo Key Improvements:
echo - Uses GetVisibleChildren API for deeper traversal
echo - Dual traversal strategy (visible + direct children)
echo - Increased depth limit (5 levels)
echo - Visual indicators for traversal methods
echo - Based on JavaFerret algorithm analysis
echo.

echo Starting Enhanced Java Discovery Tool...
echo.
echo Usage Instructions:
echo 1. Start Java applications (NC, Eclipse, NetBeans, etc.)
echo 2. Click "Refresh" to scan for Java programs
echo 3. Look for visual indicators:
echo    - 👁️ Elements found via GetVisibleChildren (deeper)
echo    - 📄 Elements found via GetAccessibleChild (direct)
echo    - 🔍 Debug information and errors
echo 4. Expand nodes to explore deeper component hierarchy
echo 5. Compare with original version for depth differences
echo.

start JavaDiscoveryToolEnhanced.exe

echo Enhanced tool started successfully!
echo.
echo For comparison, you can also run the original version:
echo .\JavaDiscoveryToolSimple.exe
echo.
pause
