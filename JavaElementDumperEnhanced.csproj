<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net461</TargetFramework>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <AssemblyName>JavaElementDumperEnhanced</AssemblyName>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="WindowsAccessBridgeInterop">
      <HintPath>bin\Debug\WindowsAccessBridgeInterop.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Include="JavaElementDumperEnhanced.cs" />
  </ItemGroup>
</Project>
