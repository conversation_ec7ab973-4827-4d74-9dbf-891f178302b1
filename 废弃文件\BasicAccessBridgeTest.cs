using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Runtime.InteropServices;
using WindowsAccessBridgeInterop;

/// <summary>
/// 基础Access Bridge测试工具
/// 直接调用底层API进行诊断
/// </summary>
class BasicAccessBridgeTest {
    
    [DllImport("user32.dll")]
    static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);
    
    [DllImport("user32.dll")]
    static extern int GetWindowText(IntPtr hWnd, StringBuilder strText, int maxCount);
    
    [DllImport("user32.dll")]
    static extern int GetWindowTextLength(IntPtr hWnd);
    
    [DllImport("user32.dll")]
    static extern bool IsWindowVisible(IntPtr hWnd);
    
    [DllImport("user32.dll")]
    static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint lpdwProcessId);
    
    delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);
    
    private static List<WindowInfo> allWindows = new List<WindowInfo>();
    private static AccessBridge accessBridge;
    
    struct WindowInfo {
        public IntPtr Handle;
        public string Title;
        public uint ProcessId;
        public bool IsVisible;
        public bool IsJavaWindow;
    }
    
    static void Main(string[] args) {
        Console.WriteLine("=== 基础Access Bridge测试工具 ===");
        Console.WriteLine("直接调用底层API进行诊断");
        Console.WriteLine();
        
        try {
            // 步骤1: 枚举所有窗口
            Console.WriteLine("步骤1: 枚举所有系统窗口...");
            EnumWindows(new EnumWindowsProc(EnumWindowCallback), IntPtr.Zero);
            Console.WriteLine("发现 " + allWindows.Count + " 个窗口");
            
            // 步骤2: 查找可能的Java窗口
            Console.WriteLine("\n步骤2: 查找可能的Java窗口...");
            var javaWindows = new List<WindowInfo>();
            foreach (var window in allWindows) {
                if (window.IsVisible && !string.IsNullOrEmpty(window.Title)) {
                    // 检查是否可能是Java窗口
                    if (window.Title.ToLower().Contains("java") ||
                        window.Title.ToLower().Contains("nc") ||
                        window.Title.ToLower().Contains("yonyou") ||
                        window.Title.ToLower().Contains("swing") ||
                        window.Title.ToLower().Contains("awt")) {
                        
                        javaWindows.Add(window);
                        Console.WriteLine("  可能的Java窗口: " + window.Title + " (PID: " + window.ProcessId + ")");
                    }
                }
            }
            
            if (javaWindows.Count == 0) {
                Console.WriteLine("  没有发现明显的Java窗口");
                Console.WriteLine("  显示所有可见窗口供参考:");
                foreach (var window in allWindows) {
                    if (window.IsVisible && !string.IsNullOrEmpty(window.Title) && window.Title.Length > 3) {
                        Console.WriteLine("    " + window.Title + " (PID: " + window.ProcessId + ")");
                    }
                }
            }
            
            // 步骤3: 初始化Access Bridge
            Console.WriteLine("\n步骤3: 初始化Access Bridge...");
            accessBridge = new AccessBridge();
            Console.WriteLine("Access Bridge初始化成功");
            
            // 等待一段时间让Access Bridge发现JVM
            Console.WriteLine("等待Access Bridge发现JVM...");
            System.Threading.Thread.Sleep(3000);
            
            // 步骤4: 测试JVM枚举
            Console.WriteLine("\n步骤4: 测试JVM枚举...");
            
            // 方法1: 直接调用EnumJvms
            Console.WriteLine("方法1: 直接调用EnumJvms()");
            try {
                var jvms1 = accessBridge.EnumJvms();
                Console.WriteLine("  发现 " + jvms1.Count + " 个JVM");
                for (int i = 0; i < jvms1.Count; i++) {
                    var jvm = jvms1[i];
                    Console.WriteLine("    JVM " + (i + 1) + ": " + jvm.GetTitle());
                    Console.WriteLine("      JVM ID: " + jvm.JvmId);
                    Console.WriteLine("      窗口数: " + jvm.Windows.Count);
                }
            } catch (Exception ex) {
                Console.WriteLine("  方法1失败: " + ex.Message);
            }
            
            // 方法2: 使用窗口缓存方法
            Console.WriteLine("\n方法2: 使用窗口缓存方法");
            try {
                var jvms2 = accessBridge.EnumJvms(hwnd => {
                    try {
                        return accessBridge.CreateAccessibleWindow(hwnd);
                    } catch {
                        return null;
                    }
                });
                Console.WriteLine("  发现 " + jvms2.Count + " 个JVM");
                for (int i = 0; i < jvms2.Count; i++) {
                    var jvm = jvms2[i];
                    Console.WriteLine("    JVM " + (i + 1) + ": " + jvm.GetTitle());
                    Console.WriteLine("      JVM ID: " + jvm.JvmId);
                    Console.WriteLine("      窗口数: " + jvm.Windows.Count);
                }
            } catch (Exception ex) {
                Console.WriteLine("  方法2失败: " + ex.Message);
            }
            
            // 步骤5: 手动测试每个可能的Java窗口
            Console.WriteLine("\n步骤5: 手动测试每个可能的Java窗口...");
            foreach (var window in javaWindows) {
                Console.WriteLine("测试窗口: " + window.Title);
                try {
                    var accessibleWindow = accessBridge.CreateAccessibleWindow(window.Handle);
                    if (accessibleWindow != null) {
                        Console.WriteLine("  ✅ 成功创建AccessibleWindow");
                        Console.WriteLine("    JVM ID: " + accessibleWindow.JvmId);
                        Console.WriteLine("    窗口标题: " + accessibleWindow.GetTitle());
                        
                        // 尝试获取窗口信息
                        try {
                            var info = accessibleWindow.GetInfo();
                            Console.WriteLine("    角色: " + info.role);
                            Console.WriteLine("    名称: " + info.name);
                            Console.WriteLine("    状态: " + info.states);
                        } catch (Exception ex) {
                            Console.WriteLine("    获取窗口信息失败: " + ex.Message);
                        }
                        
                        // 尝试获取子元素
                        try {
                            var children = accessibleWindow.GetChildren();
                            Console.WriteLine("    子元素数量: " + children.Count());
                        } catch (Exception ex) {
                            Console.WriteLine("    获取子元素失败: " + ex.Message);
                        }
                        
                    } else {
                        Console.WriteLine("  ❌ 无法创建AccessibleWindow");
                    }
                } catch (Exception ex) {
                    Console.WriteLine("  ❌ 测试失败: " + ex.Message);
                }
                Console.WriteLine();
            }
            
            // 步骤6: 诊断建议
            Console.WriteLine("步骤6: 诊断建议");
            Console.WriteLine("===============");
            
            if (javaWindows.Count == 0) {
                Console.WriteLine("❌ 没有发现Java窗口，可能原因:");
                Console.WriteLine("  1. NC应用程序没有运行");
                Console.WriteLine("  2. NC使用了非标准的窗口标题");
                Console.WriteLine("  3. NC运行在不同的用户会话中");
            } else {
                Console.WriteLine("✅ 发现了可能的Java窗口，但Access Bridge无法连接");
                Console.WriteLine("可能原因:");
                Console.WriteLine("  1. NC应用程序没有启用Java Access Bridge支持");
                Console.WriteLine("  2. NC使用的JVM版本不兼容");
                Console.WriteLine("  3. Access Bridge配置不正确");
                Console.WriteLine("  4. 需要在NC启动时添加JVM参数:");
                Console.WriteLine("     -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge");
            }
            
        } catch (Exception ex) {
            Console.WriteLine("测试过程中发生错误: " + ex.Message);
            Console.WriteLine("错误详情: " + ex.ToString());
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
    
    static bool EnumWindowCallback(IntPtr hWnd, IntPtr lParam) {
        try {
            int size = GetWindowTextLength(hWnd);
            if (size++ > 0) {
                StringBuilder sb = new StringBuilder(size);
                GetWindowText(hWnd, sb, size);
                
                uint processId;
                GetWindowThreadProcessId(hWnd, out processId);
                
                var windowInfo = new WindowInfo {
                    Handle = hWnd,
                    Title = sb.ToString(),
                    ProcessId = processId,
                    IsVisible = IsWindowVisible(hWnd)
                };
                
                allWindows.Add(windowInfo);
            }
        } catch {
            // 忽略错误，继续枚举
        }
        
        return true;
    }
}
