@echo off
echo =================================
echo Java Access Bridge 快速测试
echo =================================
echo.
echo 正在检测Java应用程序...
echo.

powershell -Command "& {Add-Type -Path 'bin\Debug\WindowsAccessBridgeInterop.dll'; $bridge = New-Object WindowsAccessBridgeInterop.AccessBridge; $jvms = $bridge.EnumJvms(); Write-Host '发现' $jvms.Count '个Java虚拟机'; if ($jvms.Count -eq 0) { Write-Host '没有发现运行中的Java应用程序' -ForegroundColor Yellow; Write-Host '请启动一个Java应用程序（如用友NC、Eclipse等）然后重新运行此测试' -ForegroundColor Yellow; } else { foreach($jvm in $jvms) { Write-Host ''; Write-Host 'JVM:' $jvm.GetTitle() -ForegroundColor Green; Write-Host '  窗口数:' $jvm.Windows.Count; foreach($window in $jvm.Windows) { Write-Host '    窗口:' $window.GetTitle(); try { $info = $window.GetInfo(); Write-Host '      角色:' $info.role; Write-Host '      子元素数:' $info.childrenCount; if ($info.childrenCount -gt 0) { Write-Host '      ✓ 可以访问子元素' -ForegroundColor Green; } else { Write-Host '      ⚠ 没有子元素或无法访问' -ForegroundColor Yellow; } } catch { Write-Host '      ✗ 获取信息失败:' $_.Exception.Message -ForegroundColor Red; } } } }; $bridge.Dispose(); Write-Host ''; Write-Host '测试完成！' -ForegroundColor Green;}"

echo.
echo =================================
echo 测试结果说明:
echo =================================
echo.
echo 如果发现0个Java虚拟机:
echo   - Access Bridge 工作正常，但没有Java应用在运行
echo   - 请启动Java应用程序然后重试
echo.
echo 如果发现Java应用但无法访问子元素:
echo   - 可能需要特殊的访问策略
echo   - 这就是我们需要增强工具的原因
echo.
echo 下一步建议:
echo   1. 启动用友NC57或其他Java应用
echo   2. 重新运行此测试
echo   3. 观察子元素访问情况
echo.

pause 