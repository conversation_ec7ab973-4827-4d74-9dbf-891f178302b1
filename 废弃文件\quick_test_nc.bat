@echo off
echo ==============================
echo Quick Test for NC 5.7 Access
echo ==============================

echo.
echo This script will help you test NC 5.7 accessibility using existing tools.
echo.
echo Prerequisites:
echo 1. NC 5.7 application should be running
echo 2. Java Access Bridge should be installed
echo.

echo Current available tools:
if exist "accessbridge2_0_2\JavaFerret.exe" (
    echo   ✓ JavaFerret.exe - Available
) else (
    echo   ✗ JavaFerret.exe - Not found
)

if exist "accessbridge2_0_2\JavaMonkey.exe" (
    echo   ✓ JavaMonkey.exe - Available  
) else (
    echo   ✗ JavaMonkey.exe - Not found
)

if exist "bin\Debug\AccessBridgeExplorer.exe" (
    echo   ✓ AccessBridgeExplorer.exe - Available
) else (
    echo   ✗ AccessBridgeExplorer.exe - Not found
)

echo.
echo Choose a tool to test NC 5.7:
echo 1. JavaFerret (Recommended - shows tree structure)
echo 2. JavaMonkey (Event monitoring)
echo 3. AccessBridgeExplorer (GUI explorer)
echo 4. Exit
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    if exist "accessbridge2_0_2\JavaFerret.exe" (
        echo Starting JavaFerret...
        start "" "accessbridge2_0_2\JavaFerret.exe"
        echo.
        echo JavaFerret started. Look for your NC application in the tree.
        echo You should see more elements than with Google's AccessBridgeExplorer.
    ) else (
        echo JavaFerret not found!
    )
) else if "%choice%"=="2" (
    if exist "accessbridge2_0_2\JavaMonkey.exe" (
        echo Starting JavaMonkey...
        start "" "accessbridge2_0_2\JavaMonkey.exe"
        echo.
        echo JavaMonkey started. It will monitor Java accessibility events.
    ) else (
        echo JavaMonkey not found!
    )
) else if "%choice%"=="3" (
    if exist "bin\Debug\AccessBridgeExplorer.exe" (
        echo Starting AccessBridgeExplorer...
        start "" "bin\Debug\AccessBridgeExplorer.exe"
        echo.
        echo AccessBridgeExplorer started.
    ) else (
        echo AccessBridgeExplorer not found!
    )
) else if "%choice%"=="4" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice!
)

echo.
echo Instructions for testing:
echo 1. In the tool window, look for your NC application
echo 2. Expand the tree nodes to explore all accessible elements
echo 3. Compare the number of elements with Google's AccessBridgeExplorer
echo 4. JavaFerret should show more comprehensive element information
echo.
pause
