/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_sun_java_accessibility_AccessBridge */

#ifndef _Included_com_sun_java_accessibility_AccessBridge
#define _Included_com_sun_java_accessibility_AccessBridge
#ifdef __cplusplus
extern "C" {
#endif
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_CHANGE_EVENTS 1i64
#undef com_sun_java_accessibility_AccessBridge_FOCUS_GAINED_EVENTS
#define com_sun_java_accessibility_AccessBridge_FOCUS_GAINED_EVENTS 2i64
#undef com_sun_java_accessibility_AccessBridge_FOCUS_LOST_EVENTS
#define com_sun_java_accessibility_AccessBridge_FOCUS_LOST_EVENTS 4i64
#undef com_sun_java_accessibility_AccessBridge_FOCUS_EVENTS
#define com_sun_java_accessibility_AccessBridge_FOCUS_EVENTS 6i64
#undef com_sun_java_accessibility_AccessBridge_CARET_UPATE_EVENTS
#define com_sun_java_accessibility_AccessBridge_CARET_UPATE_EVENTS 8i64
#undef com_sun_java_accessibility_AccessBridge_CARET_EVENTS
#define com_sun_java_accessibility_AccessBridge_CARET_EVENTS 8i64
#undef com_sun_java_accessibility_AccessBridge_MOUSE_CLICKED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MOUSE_CLICKED_EVENTS 16i64
#undef com_sun_java_accessibility_AccessBridge_MOUSE_ENTERED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MOUSE_ENTERED_EVENTS 32i64
#undef com_sun_java_accessibility_AccessBridge_MOUSE_EXITED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MOUSE_EXITED_EVENTS 64i64
#undef com_sun_java_accessibility_AccessBridge_MOUSE_PRESSED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MOUSE_PRESSED_EVENTS 128i64
#undef com_sun_java_accessibility_AccessBridge_MOUSE_RELEASED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MOUSE_RELEASED_EVENTS 256i64
#undef com_sun_java_accessibility_AccessBridge_MOUSE_EVENTS
#define com_sun_java_accessibility_AccessBridge_MOUSE_EVENTS 496i64
#undef com_sun_java_accessibility_AccessBridge_MENU_CANCELED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MENU_CANCELED_EVENTS 512i64
#undef com_sun_java_accessibility_AccessBridge_MENU_DESELECTED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MENU_DESELECTED_EVENTS 1024i64
#undef com_sun_java_accessibility_AccessBridge_MENU_SELECTED_EVENTS
#define com_sun_java_accessibility_AccessBridge_MENU_SELECTED_EVENTS 2048i64
#undef com_sun_java_accessibility_AccessBridge_MENU_EVENTS
#define com_sun_java_accessibility_AccessBridge_MENU_EVENTS 3584i64
#undef com_sun_java_accessibility_AccessBridge_POPUPMENU_CANCELED_EVENTS
#define com_sun_java_accessibility_AccessBridge_POPUPMENU_CANCELED_EVENTS 4096i64
#undef com_sun_java_accessibility_AccessBridge_POPUPMENU_WILL_BECOME_INVISIBLE_EVENTS
#define com_sun_java_accessibility_AccessBridge_POPUPMENU_WILL_BECOME_INVISIBLE_EVENTS 8192i64
#undef com_sun_java_accessibility_AccessBridge_POPUPMENU_WILL_BECOME_VISIBLE_EVENTS
#define com_sun_java_accessibility_AccessBridge_POPUPMENU_WILL_BECOME_VISIBLE_EVENTS 16384i64
#undef com_sun_java_accessibility_AccessBridge_POPUPMENU_EVENTS
#define com_sun_java_accessibility_AccessBridge_POPUPMENU_EVENTS 28672i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_NAME_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_NAME_CHANGE_EVENTS 1i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_DESCRIPTION_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_DESCRIPTION_CHANGE_EVENTS 2i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_STATE_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_STATE_CHANGE_EVENTS 4i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_VALUE_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_VALUE_CHANGE_EVENTS 8i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_SELECTION_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_SELECTION_CHANGE_EVENTS 16i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_TEXT_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_TEXT_CHANGE_EVENTS 32i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_CARET_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_CARET_CHANGE_EVENTS 64i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_VISIBLEDATA_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_VISIBLEDATA_CHANGE_EVENTS 128i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_CHILD_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_CHILD_CHANGE_EVENTS 256i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_ACTIVEDESCENDENT_CHANGE_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_ACTIVEDESCENDENT_CHANGE_EVENTS 512i64
#undef com_sun_java_accessibility_AccessBridge_PROPERTY_EVENTS
#define com_sun_java_accessibility_AccessBridge_PROPERTY_EVENTS 1023i64
/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    run
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_run
  (JNIEnv *, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    sendDebugString
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_sendDebugString
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    isJAWTInstalled
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_sun_java_accessibility_AccessBridge_isJAWTInstalled
  (JNIEnv *, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    jawtGetNativeWindowHandleFromComponent
 * Signature: (Ljava/awt/Component;)I
 */
JNIEXPORT jint JNICALL Java_com_sun_java_accessibility_AccessBridge_jawtGetNativeWindowHandleFromComponent
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    jawtGetComponentFromNativeWindowHandle
 * Signature: (I)Ljava/awt/Component;
 */
JNIEXPORT jobject JNICALL Java_com_sun_java_accessibility_AccessBridge_jawtGetComponentFromNativeWindowHandle
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyCaretChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;II)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyCaretChange
  (JNIEnv *, jobject, jobject, jobject, jint, jint);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyDescriptionChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyDescriptionChange
  (JNIEnv *, jobject, jobject, jobject, jstring, jstring);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyNameChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyNameChange
  (JNIEnv *, jobject, jobject, jobject, jstring, jstring);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertySelectionChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertySelectionChange
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyStateChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyStateChange
  (JNIEnv *, jobject, jobject, jobject, jstring, jstring);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyTextChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyTextChange
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyValueChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyValueChange
  (JNIEnv *, jobject, jobject, jobject, jstring, jstring);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyVisibleDataChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyVisibleDataChange
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyChildChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;Ljavax/accessibility/AccessibleContext;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyChildChange
  (JNIEnv *, jobject, jobject, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    propertyActiveDescendentChange
 * Signature: (Ljava/beans/PropertyChangeEvent;Ljavax/accessibility/AccessibleContext;Ljavax/accessibility/AccessibleContext;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_propertyActiveDescendentChange
  (JNIEnv *, jobject, jobject, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    javaShutdown
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_javaShutdown
  (JNIEnv *, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    focusGained
 * Signature: (Ljava/awt/event/FocusEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_focusGained
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    focusLost
 * Signature: (Ljava/awt/event/FocusEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_focusLost
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    caretUpdate
 * Signature: (Ljavax/swing/event/CaretEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_caretUpdate
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    mouseClicked
 * Signature: (Ljava/awt/event/MouseEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_mouseClicked
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    mouseEntered
 * Signature: (Ljava/awt/event/MouseEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_mouseEntered
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    mouseExited
 * Signature: (Ljava/awt/event/MouseEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_mouseExited
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    mousePressed
 * Signature: (Ljava/awt/event/MouseEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_mousePressed
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    mouseReleased
 * Signature: (Ljava/awt/event/MouseEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_mouseReleased
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    menuCanceled
 * Signature: (Ljavax/swing/event/MenuEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_menuCanceled
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    menuDeselected
 * Signature: (Ljavax/swing/event/MenuEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_menuDeselected
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    menuSelected
 * Signature: (Ljavax/swing/event/MenuEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_menuSelected
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    popupMenuCanceled
 * Signature: (Ljavax/swing/event/PopupMenuEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_popupMenuCanceled
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    popupMenuWillBecomeInvisible
 * Signature: (Ljavax/swing/event/PopupMenuEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_popupMenuWillBecomeInvisible
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_sun_java_accessibility_AccessBridge
 * Method:    popupMenuWillBecomeVisible
 * Signature: (Ljavax/swing/event/PopupMenuEvent;Ljavax/accessibility/AccessibleContext;)V
 */
JNIEXPORT void JNICALL Java_com_sun_java_accessibility_AccessBridge_popupMenuWillBecomeVisible
  (JNIEnv *, jobject, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_AccessibleJTreeNode */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_AccessibleJTreeNode
#define _Included_com_sun_java_accessibility_AccessBridge_AccessibleJTreeNode
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_EventHandler */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_EventHandler
#define _Included_com_sun_java_accessibility_AccessBridge_EventHandler
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_ObjectReferences */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_ObjectReferences
#define _Included_com_sun_java_accessibility_AccessBridge_ObjectReferences
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_ObjectReferences_Reference */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_ObjectReferences_Reference
#define _Included_com_sun_java_accessibility_AccessBridge_ObjectReferences_Reference
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_DefaultNativeWindowHandler */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_DefaultNativeWindowHandler
#define _Included_com_sun_java_accessibility_AccessBridge_DefaultNativeWindowHandler
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_NativeWindowHandler */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_NativeWindowHandler
#define _Included_com_sun_java_accessibility_AccessBridge_NativeWindowHandler
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
/* Header for class com_sun_java_accessibility_AccessBridge_shutdownHook */

#ifndef _Included_com_sun_java_accessibility_AccessBridge_shutdownHook
#define _Included_com_sun_java_accessibility_AccessBridge_shutdownHook
#ifdef __cplusplus
extern "C" {
#endif
#ifdef __cplusplus
}
#endif
#endif
