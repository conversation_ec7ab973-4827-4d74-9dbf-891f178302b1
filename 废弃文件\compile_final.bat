@echo off
echo Compiling NCTraverserFinal.cs...

C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe /target:exe /out:NCTraverser.exe /reference:src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll /reference:System.Drawing.dll NCTraverserFinal.cs

if %ERRORLEVEL% equ 0 (
    echo.
    echo SUCCESS! NCTraverser.exe compiled successfully!
    echo.
    echo Usage:
    echo   NCTraverser.exe              - Traverse all Java applications
    echo   NCTraverser.exe NC           - Traverse applications containing "NC"
    echo   NCTraverser.exe yonyou       - Traverse applications containing "yonyou"
    echo.
    echo The tool will generate a detailed log file: NC_Elements_Complete_YYYYMMDD_HHMMSS.log
    echo.
    if exist NCTraverser.exe (
        echo File created: NCTraverser.exe
        dir NCTraverser.exe
    )
) else (
    echo.
    echo Compilation failed!
)

pause
