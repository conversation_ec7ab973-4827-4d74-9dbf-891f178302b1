using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Drawing;
using System.Linq;
using System.Threading;
using WindowsAccessBridgeInterop;

/// <summary>
/// NC专用命令行遍历工具 - 工作版本
/// 基于JavaFerret成功连接的经验，针对Java 1.6.0_31和Access Bridge 2.0.2优化
/// </summary>
class NCTraverserWorking {
    private static AccessBridge accessBridge;
    private static StreamWriter logWriter;
    private static int elementCount = 0;
    private static HashSet<string> processedElements = new HashSet<string>();
    private static string targetAppName = "";
    
    static void Main(string[] args) {
        Console.WriteLine("=== NC专用命令行遍历工具 - 工作版本 ===");
        Console.WriteLine("基于JavaFerret成功连接的经验优化");
        Console.WriteLine("针对Java 1.6.0_31和Access Bridge 2.0.2");
        Console.WriteLine();
        
        // 解析命令行参数
        if (args.Length > 0) {
            targetAppName = args[0].ToLower();
            Console.WriteLine("目标应用程序: " + args[0]);
        } else {
            Console.WriteLine("将遍历所有Java应用程序");
            Console.WriteLine("用法: NCTraverserWorking.exe [应用程序名称]");
        }
        
        Console.WriteLine("正在初始化 Access Bridge...");
        
        try {
            // 初始化Access Bridge
            accessBridge = new AccessBridge();
            
            // 创建输出文件
            string outputFile = "NC_Elements_Working_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".log";
            logWriter = new StreamWriter(outputFile, false, Encoding.UTF8);
            
            Console.WriteLine("日志文件: " + outputFile);
            Console.WriteLine("开始深度遍历Java应用程序...\n");
            
            WriteLog("=== NC专用完整结构树遍历报告 - 工作版本 ===");
            WriteLog("遍历时间: " + DateTime.Now.ToString());
            WriteLog("目标应用: " + (string.IsNullOrEmpty(targetAppName) ? "所有Java应用" : targetAppName));
            WriteLog("优化版本: 针对Java 1.6.0_31和Access Bridge 2.0.2");
            WriteLog("参考成功案例: JavaFerret-32/64.exe");
            WriteLog(new string('=', 60));
            WriteLog("");
            
            // 关键改进：更长的等待时间，让Access Bridge充分初始化
            Console.WriteLine("等待Access Bridge充分初始化...");
            WriteLog("等待Access Bridge充分初始化...");
            Thread.Sleep(5000); // 等待5秒，比之前更长
            
            // 使用多种方法尝试枚举JVM
            var jvms = EnumJvmsWithRetry();
            WriteLog("发现 " + jvms.Count + " 个Java虚拟机");
            Console.WriteLine("发现 " + jvms.Count + " 个Java虚拟机");
            
            if (jvms.Count == 0) {
                WriteLog("❌ 仍然没有发现运行中的Java应用程序");
                WriteLog("但是JavaFerret可以工作，说明问题可能在于:");
                WriteLog("1. 我们的初始化时间不够长");
                WriteLog("2. 需要使用不同的API调用方式");
                WriteLog("3. 可能需要特定的Access Bridge配置");
                
                Console.WriteLine("❌ 仍然没有发现运行中的Java应用程序");
                Console.WriteLine("但是JavaFerret可以工作，这提供了重要线索");
                Console.WriteLine("建议:");
                Console.WriteLine("1. 检查JavaFerret的源代码实现");
                Console.WriteLine("2. 可能需要使用Legacy Access Bridge API");
                Console.WriteLine("3. 或者需要特定的初始化顺序");
                
                return;
            }
            
            // 遍历每个JVM
            bool foundTarget = false;
            for (int jvmIndex = 0; jvmIndex < jvms.Count; jvmIndex++) {
                var jvm = jvms[jvmIndex];
                string jvmTitle = jvm.GetTitle();
                
                // 检查是否匹配目标应用程序
                bool isTarget = string.IsNullOrEmpty(targetAppName) || 
                               jvmTitle.ToLower().Contains(targetAppName) ||
                               jvmTitle.ToLower().Contains("nc") ||
                               jvmTitle.ToLower().Contains("yonyou");
                
                if (!string.IsNullOrEmpty(targetAppName) && !isTarget) {
                    WriteLog("跳过JVM " + (jvmIndex + 1) + ": " + jvmTitle + " (不匹配目标应用)");
                    continue;
                }
                
                foundTarget = true;
                WriteLog("\n" + new string('=', 80));
                WriteLog("🎯 JVM " + (jvmIndex + 1) + ": " + jvmTitle);
                WriteLog("JVM ID: " + jvm.JvmId);
                WriteLog("窗口数量: " + jvm.Windows.Count);
                
                // 获取版本信息（模仿JavaFerret的做法）
                try {
                    AccessBridgeVersionInfo versionInfo;
                    if (accessBridge.Functions.GetVersionInfo(jvm.JvmId, out versionInfo)) {
                        WriteLog("版本信息:");
                        WriteLog("  Java虚拟机版本: " + versionInfo.VMversion);
                        WriteLog("  Access Bridge Java类版本: " + versionInfo.bridgeJavaClassVersion);
                        WriteLog("  Access Bridge Java DLL版本: " + versionInfo.bridgeJavaDLLVersion);
                        WriteLog("  Access Bridge Windows DLL版本: " + versionInfo.bridgeWinDLLVersion);
                    }
                } catch (Exception ex) {
                    WriteLog("获取版本信息失败: " + ex.Message);
                }
                
                WriteLog(new string('=', 80));
                
                Console.WriteLine("\n🎯 处理目标JVM " + (jvmIndex + 1) + ": " + jvmTitle);
                Console.WriteLine("   窗口数量: " + jvm.Windows.Count);
                
                // 遍历JVM中的所有窗口
                for (int winIndex = 0; winIndex < jvm.Windows.Count; winIndex++) {
                    var window = jvm.Windows[winIndex];
                    string windowTitle = window.GetTitle();
                    
                    WriteLog("\n" + new string('-', 60));
                    WriteLog("📋 窗口 " + (winIndex + 1) + ": " + windowTitle);
                    WriteLog("窗口句柄: " + window.Hwnd);
                    
                    // 获取窗口的详细信息（模仿JavaFerret）
                    try {
                        var windowInfo = window.GetInfo();
                        WriteLog("窗口信息:");
                        WriteLog("  名称: " + (windowInfo.name ?? ""));
                        WriteLog("  描述: " + (windowInfo.description ?? ""));
                        WriteLog("  角色: " + (windowInfo.role ?? ""));
                        WriteLog("  状态: " + (windowInfo.states ?? ""));
                        WriteLog("  边界矩形: " + windowInfo.x + ", " + windowInfo.y + ", " + windowInfo.width + ", " + windowInfo.height);
                    } catch (Exception ex) {
                        WriteLog("获取窗口信息失败: " + ex.Message);
                    }
                    
                    WriteLog(new string('-', 60));
                    
                    Console.WriteLine("  📋 遍历窗口 " + (winIndex + 1) + ": " + windowTitle);
                    
                    // 深度遍历窗口
                    TraverseElementEnhanced(window, 0);
                }
            }
            
            if (!foundTarget && !string.IsNullOrEmpty(targetAppName)) {
                WriteLog("\n⚠️ 警告: 未找到匹配 '" + targetAppName + "' 的应用程序");
                Console.WriteLine("⚠️ 未找到匹配 '" + targetAppName + "' 的应用程序");
            }
            
            WriteLog("\n" + new string('=', 80));
            WriteLog("✅ 遍历完成统计");
            WriteLog(new string('=', 80));
            WriteLog("📊 总计发现: " + elementCount + " 个唯一可访问元素");
            WriteLog("🔍 处理过的元素: " + processedElements.Count + " 个");
            WriteLog("⏰ 完成时间: " + DateTime.Now.ToString());
            WriteLog("📁 日志文件: " + outputFile);
            WriteLog(new string('=', 80));
            
            Console.WriteLine("\n✅ 遍历完成！");
            Console.WriteLine("📊 总共发现 " + elementCount + " 个唯一可访问元素");
            Console.WriteLine("📁 详细日志已保存到: " + outputFile);
            
        } catch (Exception ex) {
            string errorMsg = "发生错误: " + ex.Message;
            Console.WriteLine(errorMsg);
            if (logWriter != null) {
                WriteLog(errorMsg);
                WriteLog("错误详情: " + ex.ToString());
            }
        } finally {
            if (logWriter != null) {
                logWriter.Close();
            }
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
    
    /// <summary>
    /// 带重试机制的JVM枚举方法
    /// </summary>
    static List<AccessibleJvm> EnumJvmsWithRetry() {
        WriteLog("使用带重试机制的JVM枚举方法...");
        
        for (int attempt = 1; attempt <= 3; attempt++) {
            WriteLog("尝试 " + attempt + "/3: 枚举JVM...");
            
            try {
                // 方法1: 直接调用
                var jvms1 = accessBridge.EnumJvms();
                WriteLog("  方法1 (直接调用): 发现 " + jvms1.Count + " 个JVM");
                
                if (jvms1.Count > 0) {
                    WriteLog("  成功！使用方法1的结果");
                    return jvms1;
                }
                
                // 方法2: 使用窗口缓存
                var jvms2 = accessBridge.EnumJvms(hwnd => {
                    try {
                        return accessBridge.CreateAccessibleWindow(hwnd);
                    } catch {
                        return null;
                    }
                });
                WriteLog("  方法2 (窗口缓存): 发现 " + jvms2.Count + " 个JVM");
                
                if (jvms2.Count > 0) {
                    WriteLog("  成功！使用方法2的结果");
                    return jvms2;
                }
                
            } catch (Exception ex) {
                WriteLog("  尝试 " + attempt + " 失败: " + ex.Message);
            }
            
            if (attempt < 3) {
                WriteLog("  等待2秒后重试...");
                Thread.Sleep(2000);
            }
        }
        
        WriteLog("所有尝试都失败了");
        return new List<AccessibleJvm>();
    }
    
    /// <summary>
    /// 增强版元素遍历
    /// </summary>
    static void TraverseElementEnhanced(AccessibleNode element, int depth) {
        if (element == null || depth > 10) return; // 限制深度避免无限递归
        
        try {
            // 生成唯一标识符避免重复处理
            string elementId = GenerateElementId(element);
            if (processedElements.Contains(elementId)) {
                return;
            }
            processedElements.Add(elementId);
            
            elementCount++;
            string indent = new string(' ', depth * 2);
            
            // 获取元素基本信息
            string name = "";
            string role = "";
            string description = "";
            string states = "";
            
            var contextNode = element as AccessibleContextNode;
            if (contextNode != null) {
                try {
                    var info = contextNode.GetInfo();
                    name = info.name ?? "";
                    role = info.role ?? "";
                    description = info.description ?? "";
                    states = info.states ?? "";
                } catch (Exception ex) {
                    WriteLog(indent + "❌ 获取元素信息失败: " + ex.Message);
                }
            } else {
                try {
                    name = element.GetTitle() ?? "";
                    role = element.GetType().Name;
                } catch (Exception ex) {
                    WriteLog(indent + "❌ 获取标题失败: " + ex.Message);
                }
            }
            
            // 记录元素信息
            WriteLog(indent + "├─ [" + elementCount + "] " + role + ": " + name);
            
            if (!string.IsNullOrEmpty(description)) {
                WriteLog(indent + "│  描述: " + description);
            }
            
            if (!string.IsNullOrEmpty(states)) {
                WriteLog(indent + "│  状态: " + states);
            }
            
            // 获取位置信息
            try {
                var rect = element.GetScreenRectangle();
                if (rect.HasValue) {
                    WriteLog(indent + "│  位置: (" + rect.Value.X + "," + rect.Value.Y + ") 大小: " + rect.Value.Width + "x" + rect.Value.Height);
                }
            } catch {
                WriteLog(indent + "│  位置: 无法获取");
            }
            
            // 遍历子元素
            TraverseChildren(element, depth + 1);
            
        } catch (Exception ex) {
            WriteLog(new string(' ', depth * 2) + "❌ 遍历元素时出错: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 遍历子元素
    /// </summary>
    static void TraverseChildren(AccessibleNode element, int depth) {
        try {
            var children = element.GetChildren().ToList();
            if (children != null && children.Count > 0) {
                WriteLog(new string(' ', depth * 2) + "┌─ 子元素 (" + children.Count + " 个)");
                
                for (int i = 0; i < children.Count; i++) {
                    var child = children[i];
                    if (child != null) {
                        TraverseElementEnhanced(child, depth + 1);
                    }
                }
            }
        } catch (Exception ex) {
            WriteLog(new string(' ', depth * 2) + "❌ 子元素遍历失败: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 生成元素唯一标识符
    /// </summary>
    static string GenerateElementId(AccessibleNode element) {
        try {
            string name = "";
            string role = "";
            
            var contextNode = element as AccessibleContextNode;
            if (contextNode != null) {
                var info = contextNode.GetInfo();
                name = info.name ?? "";
                role = info.role ?? "";
            } else {
                name = element.GetTitle() ?? "";
                role = element.GetType().Name;
            }
            
            var rect = element.GetScreenRectangle();
            if (rect.HasValue) {
                return role + "|" + name + "|" + rect.Value.X + "," + rect.Value.Y + "," + rect.Value.Width + "," + rect.Value.Height;
            } else {
                return role + "|" + name + "|" + Guid.NewGuid().ToString();
            }
        } catch {
            return Guid.NewGuid().ToString();
        }
    }
    
    /// <summary>
    /// 写入日志
    /// </summary>
    static void WriteLog(string message) {
        if (logWriter != null) {
            logWriter.WriteLine(message);
            logWriter.Flush();
        }
    }
}
