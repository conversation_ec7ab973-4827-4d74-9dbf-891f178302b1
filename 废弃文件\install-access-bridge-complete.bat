@echo off
echo =================================================
echo Java Access Bridge 2.0.2 完整安装脚本
echo =================================================
echo.

set JAVA8_HOME=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot
set JAVA8_JRE=%JAVA8_HOME%\jre
set AB_SOURCE=%~dp0accessbridge2_0_2

echo 1. 检查Java 8安装...
if not exist "%JAVA8_HOME%" (
    echo ✗ Java 8未找到: %JAVA8_HOME%
    goto :error
)
echo ✓ Java 8已安装: %JAVA8_HOME%
echo.

echo 2. 检查Access Bridge源文件...
if not exist "%AB_SOURCE%" (
    echo ✗ Access Bridge源目录未找到: %AB_SOURCE%
    goto :error
)
echo ✓ Access Bridge源文件存在
echo.

echo 3. 备份现有文件...
if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
    copy "C:\Windows\System32\WindowsAccessBridge-64.dll" "C:\Windows\System32\WindowsAccessBridge-64.dll.backup" >nul
    echo ✓ 已备份 WindowsAccessBridge-64.dll
)
echo.

echo 4. 安装64位系统文件到Windows目录...
copy "%AB_SOURCE%\WindowsAccessBridge-64.dll" "C:\Windows\System32\" >nul
if errorlevel 1 (
    echo ✗ 复制WindowsAccessBridge-64.dll失败
    goto :error
)
echo ✓ WindowsAccessBridge-64.dll → C:\Windows\System32\

copy "%AB_SOURCE%\WindowsAccessBridge-32.dll" "C:\Windows\SysWOW64\" >nul
if errorlevel 1 (
    echo ✗ 复制WindowsAccessBridge-32.dll失败
    goto :error
)
echo ✓ WindowsAccessBridge-32.dll → C:\Windows\SysWOW64\
echo.

echo 5. 安装Java运行时文件...
copy "%AB_SOURCE%\JavaAccessBridge-64.dll" "%JAVA8_JRE%\bin\" >nul
if errorlevel 1 (
    echo ✗ 复制JavaAccessBridge-64.dll失败
    goto :error
)
echo ✓ JavaAccessBridge-64.dll → %JAVA8_JRE%\bin\

copy "%AB_SOURCE%\JAWTAccessBridge-64.dll" "%JAVA8_JRE%\bin\" >nul
if errorlevel 1 (
    echo ✗ 复制JAWTAccessBridge-64.dll失败
    goto :error
)
echo ✓ JAWTAccessBridge-64.dll → %JAVA8_JRE%\bin\
echo.

echo 6. 安装Java扩展文件...
copy "%AB_SOURCE%\access-bridge-64.jar" "%JAVA8_JRE%\lib\ext\" >nul
if errorlevel 1 (
    echo ✗ 复制access-bridge-64.jar失败
    goto :error
)
echo ✓ access-bridge-64.jar → %JAVA8_JRE%\lib\ext\

copy "%AB_SOURCE%\jaccess.jar" "%JAVA8_JRE%\lib\ext\" >nul
if errorlevel 1 (
    echo ✗ 复制jaccess.jar失败
    goto :error
)
echo ✓ jaccess.jar → %JAVA8_JRE%\lib\ext\
echo.

echo 7. 安装配置文件...
copy "%AB_SOURCE%\accessibility.properties" "%JAVA8_JRE%\lib\" >nul
if errorlevel 1 (
    echo ✗ 复制accessibility.properties失败
    goto :error
)
echo ✓ accessibility.properties → %JAVA8_JRE%\lib\
echo.

echo 8. 启用Access Bridge...
"%JAVA8_HOME%\bin\jabswitch.exe" -enable >nul
if errorlevel 1 (
    echo ✗ 启用Access Bridge失败
    goto :error
)
echo ✓ Access Bridge已启用
echo.

echo 9. 验证安装...
echo 检查关键文件是否存在:
if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
    echo ✓ WindowsAccessBridge-64.dll
) else (
    echo ✗ WindowsAccessBridge-64.dll 缺失
)

if exist "C:\Windows\SysWOW64\WindowsAccessBridge-32.dll" (
    echo ✓ WindowsAccessBridge-32.dll
) else (
    echo ✗ WindowsAccessBridge-32.dll 缺失
)

if exist "%JAVA8_JRE%\bin\JavaAccessBridge-64.dll" (
    echo ✓ JavaAccessBridge-64.dll
) else (
    echo ✗ JavaAccessBridge-64.dll 缺失
)

if exist "%JAVA8_JRE%\lib\ext\jaccess.jar" (
    echo ✓ jaccess.jar
) else (
    echo ✗ jaccess.jar 缺失
)

if exist "%JAVA8_JRE%\lib\accessibility.properties" (
    echo ✓ accessibility.properties
) else (
    echo ✗ accessibility.properties 缺失
)
echo.

echo 10. 测试Access Bridge...
echo 启动测试程序 JavaFerret-64...
start "" "%AB_SOURCE%\JavaFerret-64.exe"
echo.

echo =================================================
echo ✅ Java Access Bridge 2.0.2 安装完成！
echo.
echo 下一步：
echo 1. 重启用友NC系统
echo 2. 使用新的启动参数：
echo    -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge
echo 3. 运行 AccessBridgeExplorer.exe
echo 4. 使用 JavaFerret-64.exe 测试Java应用程序访问
echo =================================================
goto :end

:error
echo.
echo ❌ 安装过程中出现错误！
echo 请检查：
echo 1. 是否以管理员权限运行此脚本
echo 2. Java 8是否正确安装
echo 3. Access Bridge源文件是否完整
echo.

:end
pause 