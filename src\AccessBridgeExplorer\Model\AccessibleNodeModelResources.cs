// Copyright 2016 Google Inc. All Rights Reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
//     http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using System;
using System.Drawing;
using System.Windows.Forms;

namespace AccessBridgeExplorer.Model {
  public class AccessibleNodeModelResources {
    private readonly TreeView _treeView;
    private readonly Lazy<Font> _ephemeralFont;

    public AccessibleNodeModelResources(TreeView treeView) {
      _treeView = treeView;
      _ephemeralFont = new Lazy<Font>(() => new Font(_treeView.Font, FontStyle.Italic));
    }

    public Font ManagedDescendantFont {
      get { return _ephemeralFont.Value; }
    }
  }
}