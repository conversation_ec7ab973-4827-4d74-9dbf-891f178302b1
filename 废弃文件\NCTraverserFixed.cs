using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;
using WindowsAccessBridgeInterop;

/// <summary>
/// NC专用Java程序发现和遍历工具
/// 兼容.NET Framework 4.0版本
/// </summary>
class NCTraverserFixed {
    private static AccessBridge accessBridge;
    private static StreamWriter logWriter;
    private static int elementCount = 0;
    private static HashSet<string> processedElements = new HashSet<string>();
    private static string targetAppName = "";
    
    // Windows API 声明
    [DllImport("user32.dll")]
    private static extern bool EnumWindows(EnumWindowsDelegate lpEnumFunc, IntPtr lParam);
    
    [DllImport("user32.dll", CharSet = CharSet.Auto)]
    private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);
    
    [DllImport("user32.dll")]
    private static extern int GetWindowTextLength(IntPtr hWnd);
    
    [DllImport("user32.dll")]
    private static extern uint GetWindowThreadProcessId(IntPtr hWnd, out uint processId);
    
    [DllImport("user32.dll")]
    private static extern bool IsWindowVisible(IntPtr hWnd);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    private static extern IntPtr LoadLibrary(string lpFileName);
    
    [DllImport("kernel32.dll", SetLastError = true)]
    private static extern bool FreeLibrary(IntPtr hModule);
    
    private delegate bool EnumWindowsDelegate(IntPtr hWnd, IntPtr lParam);
    
    static void Main(string[] args) {
        Console.WriteLine("=== NC专用Java程序发现和遍历工具 ===");
        Console.WriteLine("兼容.NET Framework 4.0版本");
        Console.WriteLine();
        
        // 解析命令行参数
        if (args.Length > 0) {
            targetAppName = args[0].ToLower();
            Console.WriteLine("目标应用程序: " + args[0]);
        } else {
            Console.WriteLine("将搜索所有Java应用程序");
            Console.WriteLine("用法: NCTraverserFixed.exe [应用程序名称]");
        }
        Console.WriteLine();
        
        try {
            // 创建日志文件
            string outputFile = "NC_Java_Discovery_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".log";
            logWriter = new StreamWriter(outputFile, false, Encoding.UTF8);
            
            WriteLog("=== NC专用Java程序发现和遍历报告 ===");
            WriteLog("开始时间: " + DateTime.Now.ToString());
            WriteLog("目标应用: " + (string.IsNullOrEmpty(targetAppName) ? "所有Java应用" : targetAppName));
            WriteLog(new string('=', 80));
            WriteLog("");
            
            Console.WriteLine("日志文件: " + outputFile);
            Console.WriteLine();
            
            // 步骤1: 系统环境检查
            Console.WriteLine("步骤1: 系统环境检查");
            PerformSystemCheck();
            
            // 步骤2: Java进程发现
            Console.WriteLine("\n步骤2: Java进程发现");
            var javaProcesses = DiscoverJavaProcesses();
            
            // 步骤3: Access Bridge初始化
            Console.WriteLine("\n步骤3: Access Bridge初始化");
            if (!InitializeAccessBridge()) {
                Console.WriteLine("Access Bridge初始化失败");
                return;
            }
            
            // 步骤4: Java窗口发现
            Console.WriteLine("\n步骤4: Java窗口发现");
            var javaWindows = DiscoverJavaWindows();
            
            // 步骤5: JVM发现和遍历
            Console.WriteLine("\n步骤5: JVM发现和遍历");
            PerformJvmDiscoveryAndTraversal();
            
            WriteLog("\n遍历完成");
            WriteLog("总计发现元素: " + elementCount.ToString());
            WriteLog("完成时间: " + DateTime.Now.ToString());
            
            Console.WriteLine("\n遍历完成！发现 " + elementCount + " 个元素");
            Console.WriteLine("详细日志: " + outputFile);
            
        } catch (Exception ex) {
            string errorMsg = "发生错误: " + ex.Message;
            Console.WriteLine(errorMsg);
            WriteLog(errorMsg);
            WriteLog("错误详情: " + ex.ToString());
        } finally {
            if (logWriter != null) {
                logWriter.Close();
            }
            if (accessBridge != null) {
                accessBridge.Dispose();
            }
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
    
    private static void PerformSystemCheck() {
        WriteLog("系统环境检查");
        WriteLog(new string('-', 40));
        
        var osVersion = Environment.OSVersion;
        WriteLog("操作系统: " + osVersion.VersionString);
        Console.WriteLine("  操作系统: " + osVersion.VersionString);
        
        var dotNetVersion = Environment.Version;
        WriteLog(".NET版本: " + dotNetVersion.ToString());
        Console.WriteLine("  .NET版本: " + dotNetVersion.ToString());
        
        var architecture = Environment.Is64BitProcess ? "64位" : "32位";
        WriteLog("进程架构: " + architecture);
        Console.WriteLine("  进程架构: " + architecture);
        
        CheckAccessBridgeDlls();
        WriteLog("");
    }
    
    private static void CheckAccessBridgeDlls() {
        WriteLog("Access Bridge DLL检查:");
        
        string[] dllNames = Environment.Is64BitProcess ? 
            new[] { "WindowsAccessBridge-64.dll", "WindowsAccessBridge.dll" } :
            new[] { "WindowsAccessBridge-32.dll", "WindowsAccessBridge.dll" };
        
        bool found = false;
        foreach (string dllName in dllNames) {
            try {
                var handle = LoadLibrary(dllName);
                if (handle != IntPtr.Zero) {
                    WriteLog("找到: " + dllName);
                    Console.WriteLine("    找到: " + dllName);
                    FreeLibrary(handle);
                    found = true;
                    break;
                }
            } catch (Exception ex) {
                WriteLog("检查 " + dllName + " 失败: " + ex.Message);
            }
        }
        
        if (!found) {
            WriteLog("未找到Access Bridge DLL文件");
            Console.WriteLine("    未找到Access Bridge DLL文件");
            WriteLog("请确保已安装Java Access Bridge");
        }
    }
    
    private static List<Process> DiscoverJavaProcesses() {
        var javaProcesses = new List<Process>();
        
        WriteLog("Java进程发现");
        WriteLog(new string('-', 40));
        
        try {
            var allProcesses = Process.GetProcesses();
            
            foreach (var process in allProcesses) {
                try {
                    if (IsJavaProcess(process)) {
                        javaProcesses.Add(process);
                        
                        string processInfo = "PID: " + process.Id + ", 名称: " + process.ProcessName;
                        try {
                            processInfo += ", 标题: " + process.MainWindowTitle;
                        } catch { }
                        
                        WriteLog("发现Java进程: " + processInfo);
                        Console.WriteLine("  Java进程: " + process.ProcessName + " (PID: " + process.Id + ")");
                        
                        if (!string.IsNullOrEmpty(targetAppName)) {
                            bool isMatch = process.ProcessName.ToLower().Contains(targetAppName) ||
                                         process.MainWindowTitle.ToLower().Contains(targetAppName) ||
                                         process.MainWindowTitle.ToLower().Contains("nc") ||
                                         process.MainWindowTitle.ToLower().Contains("yonyou");
                            
                            if (isMatch) {
                                WriteLog("匹配目标应用: " + processInfo);
                                Console.WriteLine("    匹配目标应用!");
                            }
                        }
                    }
                } catch (Exception ex) {
                    continue;
                }
            }
        } catch (Exception ex) {
            WriteLog("进程枚举失败: " + ex.Message);
            Console.WriteLine("  进程枚举失败: " + ex.Message);
        }
        
        WriteLog("总计发现 " + javaProcesses.Count + " 个Java进程");
        Console.WriteLine("  总计发现 " + javaProcesses.Count + " 个Java进程");
        WriteLog("");
        
        return javaProcesses;
    }
    
    private static bool IsJavaProcess(Process process) {
        try {
            string processName = process.ProcessName.ToLower();
            
            string[] javaProcessNames = {
                "java", "javaw", "javaws", "eclipse", "netbeans", "idea64", "idea",
                "nc", "ufida", "yonyou", "jre", "jvm"
            };
            
            foreach (string javaName in javaProcessNames) {
                if (processName.Contains(javaName)) {
                    return true;
                }
            }
            
            string windowTitle = process.MainWindowTitle.ToLower();
            if (!string.IsNullOrEmpty(windowTitle)) {
                if (windowTitle.Contains("java") || windowTitle.Contains("nc") || 
                    windowTitle.Contains("yonyou") || windowTitle.Contains("用友")) {
                    return true;
                }
            }
            
            return false;
        } catch {
            return false;
        }
    }
    
    private static bool InitializeAccessBridge() {
        WriteLog("Access Bridge初始化");
        WriteLog(new string('-', 40));
        
        try {
            Console.WriteLine("  正在初始化Access Bridge...");
            accessBridge = new AccessBridge();
            accessBridge.Initialize();
            
            WriteLog("Access Bridge初始化成功");
            Console.WriteLine("  Access Bridge初始化成功");
            
            try {
                var version = accessBridge.LibraryVersion;
                WriteLog("Access Bridge版本: " + version.FileVersion);
                Console.WriteLine("    版本: " + version.FileVersion);
                
                var isLegacy = accessBridge.IsLegacy;
                WriteLog("Legacy模式: " + isLegacy.ToString());
                Console.WriteLine("    Legacy模式: " + isLegacy.ToString());
            } catch (Exception ex) {
                WriteLog("无法获取版本信息: " + ex.Message);
            }
            
            WriteLog("");
            return true;
            
        } catch (Exception ex) {
            WriteLog("Access Bridge初始化失败: " + ex.Message);
            Console.WriteLine("  初始化失败: " + ex.Message);
            
            WriteLog("可能的解决方案:");
            WriteLog("1. 确保已安装Java Access Bridge");
            WriteLog("2. 检查Java应用程序是否启用了可访问性");
            WriteLog("3. 尝试重新启动Java应用程序");
            WriteLog("4. 确保Java版本支持Access Bridge (JRE 7+)");
            
            Console.WriteLine("  请检查Java Access Bridge安装状态");
            WriteLog("");
            return false;
        }
    }
    
    private static List<IntPtr> DiscoverJavaWindows() {
        var javaWindows = new List<IntPtr>();
        
        WriteLog("Java窗口发现");
        WriteLog(new string('-', 40));
        
        try {
            EnumWindows((hWnd, lParam) => {
                if (IsWindowVisible(hWnd) && IsJavaWindow(hWnd)) {
                    javaWindows.Add(hWnd);
                    
                    string windowTitle = GetWindowTitle(hWnd);
                    uint processId;
                    GetWindowThreadProcessId(hWnd, out processId);
                    
                    WriteLog("Java窗口: " + windowTitle + " (HWND: 0x" + hWnd.ToString("X") + ", PID: " + processId + ")");
                    Console.WriteLine("  Java窗口: " + windowTitle);
                    
                    if (!string.IsNullOrEmpty(targetAppName)) {
                        bool isMatch = windowTitle.ToLower().Contains(targetAppName) ||
                                     windowTitle.ToLower().Contains("nc") ||
                                     windowTitle.ToLower().Contains("yonyou");
                        
                        if (isMatch) {
                            WriteLog("匹配目标窗口: " + windowTitle);
                            Console.WriteLine("    匹配目标窗口!");
                        }
                    }
                }
                return true;
            }, IntPtr.Zero);
        } catch (Exception ex) {
            WriteLog("窗口枚举失败: " + ex.Message);
            Console.WriteLine("  窗口枚举失败: " + ex.Message);
        }
        
        WriteLog("总计发现 " + javaWindows.Count + " 个Java窗口");
        Console.WriteLine("  总计发现 " + javaWindows.Count + " 个Java窗口");
        WriteLog("");
        
        return javaWindows;
    }
    
    private static bool IsJavaWindow(IntPtr hWnd) {
        try {
            if (accessBridge != null && accessBridge.Functions != null) {
                return accessBridge.Functions.IsJavaWindow(hWnd);
            }
            return false;
        } catch {
            return false;
        }
    }
    
    private static string GetWindowTitle(IntPtr hWnd) {
        try {
            int length = GetWindowTextLength(hWnd);
            if (length > 0) {
                StringBuilder sb = new StringBuilder(length + 1);
                GetWindowText(hWnd, sb, sb.Capacity);
                return sb.ToString();
            }
            return "<无标题>";
        } catch {
            return "<获取失败>";
        }
    }
    
    private static void PerformJvmDiscoveryAndTraversal() {
        WriteLog("JVM发现和遍历");
        WriteLog(new string('-', 40));
        
        if (accessBridge == null) {
            WriteLog("Access Bridge未初始化");
            Console.WriteLine("  Access Bridge未初始化");
            return;
        }
        
        try {
            List<AccessibleJvm> jvms = null;
            for (int attempt = 1; attempt <= 3; attempt++) {
                Console.WriteLine("  尝试 " + attempt + "/3: 枚举JVM...");
                WriteLog("枚举JVM尝试 " + attempt + ":");
                
                jvms = accessBridge.EnumJvms();
                WriteLog("发现 " + jvms.Count + " 个JVM");
                
                if (jvms.Count > 0) {
                    break;
                }
                
                if (attempt < 3) {
                    Console.WriteLine("    等待2秒后重试...");
                    Thread.Sleep(2000);
                }
            }
            
            if (jvms == null || jvms.Count == 0) {
                WriteLog("未发现任何JVM");
                Console.WriteLine("  未发现任何JVM");
                
                WriteLog("可能的原因:");
                WriteLog("1. Java应用程序未启用可访问性支持");
                WriteLog("2. Access Bridge版本不匹配");
                WriteLog("3. Java应用程序使用了自定义的安全管理器");
                WriteLog("4. 需要以管理员身份运行");
                
                Console.WriteLine("  建议:");
                Console.WriteLine("    1. 确保Java应用程序已启动");
                Console.WriteLine("    2. 尝试重新启动Java应用程序");
                Console.WriteLine("    3. 检查Java应用程序的可访问性设置");
                return;
            }
            
            Console.WriteLine("  发现 " + jvms.Count + " 个JVM");
            
            bool foundTarget = false;
            for (int jvmIndex = 0; jvmIndex < jvms.Count; jvmIndex++) {
                var jvm = jvms[jvmIndex];
                string jvmTitle = jvm.GetTitle();
                
                WriteLog("\n" + new string('=', 60));
                WriteLog("JVM " + (jvmIndex + 1) + ": " + jvmTitle);
                WriteLog("JVM ID: " + jvm.JvmId);
                WriteLog("窗口数量: " + jvm.Windows.Count);
                
                Console.WriteLine("\n  JVM " + (jvmIndex + 1) + ": " + jvmTitle);
                Console.WriteLine("     窗口数量: " + jvm.Windows.Count);
                
                bool isTarget = string.IsNullOrEmpty(targetAppName) ||
                               jvmTitle.ToLower().Contains(targetAppName) ||
                               jvmTitle.ToLower().Contains("nc") ||
                               jvmTitle.ToLower().Contains("yonyou");
                
                if (!string.IsNullOrEmpty(targetAppName) && !isTarget) {
                    WriteLog("跳过JVM (不匹配目标应用)");
                    continue;
                }
                
                foundTarget = true;
                
                for (int winIndex = 0; winIndex < jvm.Windows.Count; winIndex++) {
                    var window = jvm.Windows[winIndex];
                    string windowTitle = window.GetTitle();
                    
                    WriteLog("\n" + new string('-', 50));
                    WriteLog("窗口 " + (winIndex + 1) + ": " + windowTitle);
                    WriteLog("窗口句柄: 0x" + window.Hwnd.ToString("X"));
                    
                    Console.WriteLine("     遍历窗口 " + (winIndex + 1) + ": " + windowTitle);
                    
                    TraverseElementEnhanced(window, 0);
                }
            }
            
            if (!foundTarget && !string.IsNullOrEmpty(targetAppName)) {
                WriteLog("\n警告: 未找到匹配 '" + targetAppName + "' 的JVM");
                Console.WriteLine("  未找到匹配 '" + targetAppName + "' 的JVM");
            }
            
        } catch (Exception ex) {
            WriteLog("JVM发现失败: " + ex.Message);
            Console.WriteLine("  JVM发现失败: " + ex.Message);
            WriteLog("错误详情: " + ex.ToString());
        }
    }
    
    private static void TraverseElementEnhanced(AccessibleNode node, int depth) {
        if (node == null || depth > 50) return;
        
        try {
            string indent = new string(' ', depth * 2);
            string nodeInfo = GetNodeInfo(node);
            string uniqueId = GetUniqueElementId(node);
            
            if (processedElements.Contains(uniqueId)) {
                return;
            }
            processedElements.Add(uniqueId);
            elementCount++;
            
            WriteLog(indent + "[" + elementCount.ToString("000") + "] " + nodeInfo);
            
            if (elementCount % 10 == 0) {
                Console.Write("\r    已发现 " + elementCount + " 个元素...");
            }
            
            var children = node.GetChildren();
            
            foreach (var child in children) {
                TraverseElementEnhanced(child, depth + 1);
            }
            
        } catch (Exception ex) {
            WriteLog("遍历元素时出错: " + ex.Message);
        }
    }
    
    private static string GetNodeInfo(AccessibleNode node) {
        try {
            string title = node.GetTitle() ?? "<无标题>";
            string type = node.GetType().Name;
            
            if (node is AccessibleContextNode) {
                try {
                    var contextNode = node as AccessibleContextNode;
                    var info = contextNode.GetInfo();
                    return type + ": " + title + " | 角色: " + info.role + " | 状态: " + info.states + " | 子元素: " + info.childrenCount;
                } catch {
                    return type + ": " + title;
                }
            }
            
            return type + ": " + title;
        } catch {
            return node.GetType().Name + ": <获取信息失败>";
        }
    }
    
    private static string GetUniqueElementId(AccessibleNode node) {
        try {
            if (node is AccessibleContextNode) {
                var contextNode = node as AccessibleContextNode;
                var info = contextNode.GetInfo();
                return node.JvmId + "_" + info.x + "_" + info.y + "_" + info.width + "_" + info.height + "_" + info.role + "_" + info.name;
            }
            return node.JvmId + "_" + node.GetTitle() + "_" + node.GetHashCode();
        } catch {
            return node.GetHashCode() + "_" + DateTime.Now.Ticks;
        }
    }
    
    private static void WriteLog(string message) {
        string logMessage = "[" + DateTime.Now.ToString("HH:mm:ss") + "] " + message;
        if (logWriter != null) {
            logWriter.WriteLine(logMessage);
            logWriter.Flush();
        }
    }
}
