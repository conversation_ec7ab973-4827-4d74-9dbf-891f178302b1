﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{902E9F38-EF1A-4ED5-86BC-25881DBDABED}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>AccessBridgeExplorer</RootNamespace>
    <AssemblyName>AccessBridgeExplorer</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile>Client</TargetFrameworkProfile>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
    <LangVersion>4</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>..\..\bin\Debug\x86\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <UseVSHostingProcess>false</UseVSHostingProcess>
    <LangVersion>4</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>..\..\bin\Release\x86\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AboutForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="AboutForm.Designer.cs">
      <DependentUpon>AboutForm.cs</DependentUpon>
    </Compile>
    <Compile Include="AccessibleRectInfoSelectedEventArgs.cs" />
    <Compile Include="ExceptionForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExceptionForm.Designer.cs">
      <DependentUpon>ExceptionForm.cs</DependentUpon>
    </Compile>
    <Compile Include="PropertyListTreeViewModel.cs" />
    <Compile Include="Utils\IWindowsHotKeyHandler.cs" />
    <Compile Include="Utils\Settings\BoolUserSetting.cs" />
    <Compile Include="Utils\Settings\EnumFlagsUserSettings.cs" />
    <Compile Include="Utils\Settings\EnumUserSetting.cs" />
    <Compile Include="Utils\Settings\IntUserSetting.cs" />
    <Compile Include="Utils\Settings\IUserSettings.cs" />
    <Compile Include="Utils\Settings\ChangedEventArgs.cs" />
    <Compile Include="Utils\Settings\SettingValueChangedEventArgs.cs" />
    <Compile Include="Utils\Settings\StringUserSetting.cs" />
    <Compile Include="Utils\Settings\SyncEventArgs.cs" />
    <Compile Include="Utils\Settings\UserSetting.cs" />
    <Compile Include="Utils\Settings\UserSettingBase.cs" />
    <Compile Include="Utils\Settings\UserSettingImpl.cs" />
    <Compile Include="Utils\Settings\UserSettings.cs" />
    <Compile Include="Utils\Settings\UserSettingsExtensions.cs" />
    <Compile Include="Utils\Settings\WeakDelegateList.cs" />
    <Compile Include="Utils\Settings\WeakEventHandlers.cs" />
    <Compile Include="Utils\TreeListViewNodeArgs.cs" />
    <Compile Include="Utils\TreeListView.cs" />
    <Compile Include="Utils\TreeListViewModel.cs" />
    <Compile Include="Utils\TreeNodeViewModel.cs" />
    <Compile Include="Utils\SingleDelayedTask.cs" />
    <Compile Include="ExplorerFormNavigation.cs" />
    <Compile Include="ExplorerFormController.cs" />
    <Compile Include="IExplorerFormNavigation.cs" />
    <Compile Include="IExplorerFormView.cs" />
    <Compile Include="Utils\ExceptionUtils.cs" />
    <Compile Include="Utils\IMessageQueue.cs" />
    <Compile Include="Utils\ListHelpers.cs" />
    <Compile Include="Utils\ListViewItemCollectionExtensions.cs" />
    <Compile Include="Model\AccessibleNodeModelResources.cs" />
    <Compile Include="NotificationPanelEntry.cs" />
    <Compile Include="NotificationPanelIcon.cs" />
    <Compile Include="PropertyGroupErrorEventArgs.cs" />
    <Compile Include="PropertyListView.cs" />
    <Compile Include="Utils\EnumerableExtensions.cs" />
    <Compile Include="EventForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="EventForm.Designer.cs">
      <DependentUpon>EventForm.cs</DependentUpon>
    </Compile>
    <Compile Include="RoundButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="TooltipWindow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TooltipWindow.Designer.cs">
      <DependentUpon>TooltipWindow.cs</DependentUpon>
    </Compile>
    <Compile Include="OverlayWindow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OverlayWindow.Designer.cs">
      <DependentUpon>OverlayWindow.cs</DependentUpon>
    </Compile>
    <Compile Include="ExplorerForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExplorerForm.Designer.cs">
      <DependentUpon>ExplorerForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Model\NodeModel.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="NotificationPanel.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NotificationPanel.Designer.cs">
      <DependentUpon>NotificationPanel.cs</DependentUpon>
    </Compile>
    <Compile Include="Utils\TreeNodeCollectionExtensions.cs" />
    <Compile Include="UpdateChecker.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="UpdateChecker.Designer.cs">
      <DependentUpon>UpdateChecker.cs</DependentUpon>
    </Compile>
    <Compile Include="UpdateInfoArgs.cs" />
    <Compile Include="Model\AccessibleNodeModel.cs" />
    <Compile Include="Utils\ScreenUtils.cs" />
    <Compile Include="Utils\User32Utils.cs" />
    <Compile Include="Utils\Settings\UserSettingsProvider.cs" />
    <Compile Include="Utils\WindowsHotKeyHandler.cs" />
    <Compile Include="Utils\WindowsHotKeyHandlerFactory.cs" />
    <EmbeddedResource Include="AboutForm.resx">
      <DependentUpon>AboutForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExceptionForm.resx">
      <DependentUpon>ExceptionForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="EventForm.resx">
      <DependentUpon>EventForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="NotificationPanel.resx">
      <DependentUpon>NotificationPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TooltipWindow.resx">
      <DependentUpon>TooltipWindow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OverlayWindow.resx">
      <DependentUpon>OverlayWindow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExplorerForm.resx">
      <DependentUpon>ExplorerForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="UpdateChecker.resx">
      <DependentUpon>UpdateChecker.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0,Profile=Client">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 Client Profile %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.4.5">
      <Visible>False</Visible>
      <ProductName>Windows Installer 4.5</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Crosshair.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Plus.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\Minus.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\SmallCloseButton.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\InfoIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\ErrorIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\WarningIcon.png" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\WindowsAccessBridgeInterop\WindowsAccessBridgeInterop.csproj">
      <Project>{4b5de0e1-2f0a-426d-bb12-becb0f6f8b51}</Project>
      <Name>WindowsAccessBridgeInterop</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>