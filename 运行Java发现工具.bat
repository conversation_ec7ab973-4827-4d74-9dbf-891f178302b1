@echo off
echo ===============================================
echo Java Discovery Tool - Java程序发现工具
echo Based on Java Access Bridge API
echo ===============================================

echo.
echo Checking dependencies...

REM Check if the tool exists
if not exist "JavaDiscoveryToolSimple.exe" (
    echo ERROR: JavaDiscoveryToolSimple.exe not found
    echo Please compile the tool first using build_java_discovery_tool.bat
    pause
    exit /b 1
)

REM Check WindowsAccessBridgeInterop.dll
if not exist "src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll" (
    echo ERROR: WindowsAccessBridgeInterop.dll not found
    echo Please ensure the dependency is compiled
    pause
    exit /b 1
)

echo Dependencies OK
echo.

echo Starting Java Discovery Tool...
echo.
echo Instructions:
echo 1. Make sure Java Access Bridge is properly installed
echo 2. Start Java applications (e.g., NC, Eclipse, NetBeans)
echo 3. Click "Refresh" button to scan for Java programs
echo 4. Expand tree nodes to explore Java component hierarchy
echo 5. Click on nodes to view detailed properties
echo.

start JavaDiscoveryToolSimple.exe

echo Tool started successfully!
echo.
pause
