using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace SimpleJavaMethodTester
{
    public partial class TestForm : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeView;
        private TextBox logTextBox;
        private AccessibleContextNode selectedNode;

        public TestForm()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        private void InitializeComponent()
        {
            this.Text = "Java发现方法测试工具 - 简化版";
            this.Size = new Size(1200, 800);
            this.StartPosition = FormStartPosition.CenterScreen;

            var splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 400;

            // 树视图
            treeView = new TreeView();
            treeView.Dock = DockStyle.Fill;
            treeView.AfterSelect += TreeView_AfterSelect;

            // 日志文本框
            logTextBox = new TextBox();
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.ReadOnly = true;
            logTextBox.Font = new Font("Consolas", 9);

            // 按钮面板
            var buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Top;
            buttonPanel.Height = 200;
            buttonPanel.AutoScroll = true;

            CreateButtons(buttonPanel);

            splitContainer.Panel1.Controls.Add(treeView);
            splitContainer.Panel2.Controls.Add(logTextBox);

            this.Controls.Add(splitContainer);
            this.Controls.Add(buttonPanel);
        }

        private void CreateButtons(Panel panel)
        {
            int x = 10, y = 10;
            int buttonWidth = 150, buttonHeight = 30;
            int spacing = 160;

            var buttons = new Button[]
            {
                CreateButton("刷新Java窗口", x, y, buttonWidth, buttonHeight, RefreshJavaWindows),
                CreateButton("1.GetChildren", x + spacing, y, buttonWidth, buttonHeight, TestGetChildren),
                CreateButton("2.GetChildrenEnhanced", x + spacing * 2, y, buttonWidth, buttonHeight, TestGetChildrenEnhanced),
                CreateButton("3.GetVisibleChildren", x, y + 40, buttonWidth, buttonHeight, TestGetVisibleChildren),
                CreateButton("4.GetAccessibleSelection", x + spacing, y + 40, buttonWidth, buttonHeight, TestGetAccessibleSelection),
                CreateButton("5.坐标搜索", x + spacing * 2, y + 40, buttonWidth, buttonHeight, TestCoordinateSearch),
                CreateButton("6.GetAccessibleTable", x, y + 80, buttonWidth, buttonHeight, TestGetAccessibleTable),
                CreateButton("7.GetAccessibleText", x + spacing, y + 80, buttonWidth, buttonHeight, TestGetAccessibleText),
                CreateButton("8.深度递归搜索", x + spacing * 2, y + 80, buttonWidth, buttonHeight, TestDeepSearch),
                CreateButton("9.组合测试", x, y + 120, buttonWidth, buttonHeight, TestCombined),
                CreateButton("清空日志", x + spacing, y + 120, buttonWidth, buttonHeight, ClearLog)
            };

            foreach (var button in buttons)
            {
                panel.Controls.Add(button);
            }
        }

        private Button CreateButton(string text, int x, int y, int width, int height, EventHandler clickHandler)
        {
            var button = new Button();
            button.Text = text;
            button.Location = new Point(x, y);
            button.Size = new Size(width, height);
            button.Click += clickHandler;
            return button;
        }

        private void InitializeAccessBridge()
        {
            try
            {
                accessBridge = new AccessBridge();
                LogMessage("Access Bridge 初始化成功");
                RefreshJavaWindows(null, null);
            }
            catch (Exception ex)
            {
                LogMessage("Access Bridge 初始化失败: " + ex.Message);
                MessageBox.Show("无法初始化 Access Bridge: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshJavaWindows(object sender, EventArgs e)
        {
            try
            {
                treeView.Nodes.Clear();
                LogMessage("=== 开始搜索Java窗口 ===");

                var javaWindows = FindJavaWindows();
                LogMessage("发现 " + javaWindows.Count + " 个Java窗口");

                foreach (var hWnd in javaWindows)
                {
                    CreateJavaWindowNode(hWnd);
                }

                LogMessage("Java窗口搜索完成");
            }
            catch (Exception ex)
            {
                LogMessage("搜索Java窗口时出错: " + ex.Message);
            }
        }

        private List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            EnumWindows(delegate(IntPtr hWnd, IntPtr lParam)
            {
                if (IsWindowVisible(hWnd) && accessBridge.Functions.IsJavaWindow(hWnd))
                {
                    javaWindows.Add(hWnd);
                }
                return true;
            }, IntPtr.Zero);
            
            return javaWindows;
        }

        private void CreateJavaWindowNode(IntPtr hWnd)
        {
            try
            {
                var windowTitle = GetWindowTitle(hWnd);
                
                int vmId;
                JavaObjectHandle ac;
                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out vmId, out ac))
                {
                    var rootNode = new TreeNode("Java窗口: " + windowTitle);
                    
                    var accessibleWindow = accessBridge.CreateAccessibleWindow(hWnd);
                    if (accessibleWindow != null)
                    {
                        rootNode.Tag = accessibleWindow;
                        AddBasicChildren(rootNode, accessibleWindow, 0, 2);
                        treeView.Nodes.Add(rootNode);
                        rootNode.Expand();
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("错误: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                treeView.Nodes.Add(errorNode);
            }
        }

        private void AddBasicChildren(TreeNode parentNode, AccessibleNode accessibleNode, int depth, int maxDepth)
        {
            if (depth >= maxDepth) return;

            try
            {
                var children = accessibleNode.GetChildren().ToList();
                foreach (var child in children.Take(5))
                {
                    if (child is AccessibleContextNode)
                    {
                        var childContext = (AccessibleContextNode)child;
                        var childInfo = childContext.GetInfo();
                        var childNode = new TreeNode((childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "]");
                        childNode.Tag = child;
                        parentNode.Nodes.Add(childNode);

                        if (childInfo.childrenCount > 0)
                        {
                            AddBasicChildren(childNode, child, depth + 1, maxDepth);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("获取子组件失败: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag is AccessibleContextNode)
            {
                selectedNode = (AccessibleContextNode)e.Node.Tag;
                var info = selectedNode.GetInfo();
                LogMessage("选中组件: " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                LogMessage("  子组件数: " + info.childrenCount + ", 位置: (" + info.x + ", " + info.y + "), 大小: " + info.width + "x" + info.height);
            }
            else
            {
                selectedNode = null;
                LogMessage("请选择一个Java组件进行测试");
            }
        }

        private void TestGetChildren(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试 GetChildren ===");
            try
            {
                var children = selectedNode.GetChildren().ToList();
                LogMessage("发现 " + children.Count + " 个子组件:");
                
                foreach (var child in children.Take(10))
                {
                    if (child is AccessibleContextNode)
                    {
                        var childContext = (AccessibleContextNode)child;
                        var info = childContext.GetInfo();
                        LogMessage("  - " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                    }
                    else
                    {
                        LogMessage("  - 未知类型: " + child.GetType().Name);
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage("GetChildren 失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestGetChildrenEnhanced(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试 GetChildrenEnhanced ===");
            try
            {
                var children = selectedNode.GetChildrenEnhanced().ToList();
                LogMessage("发现 " + children.Count + " 个子组件:");
                
                foreach (var child in children.Take(10))
                {
                    if (child is AccessibleContextNode)
                    {
                        var childContext = (AccessibleContextNode)child;
                        var info = childContext.GetInfo();
                        LogMessage("  - " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                    }
                    else
                    {
                        LogMessage("  - 未知类型: " + child.GetType().Name);
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage("GetChildrenEnhanced 失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestGetVisibleChildren(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试 GetVisibleChildren ===");
            try
            {
                var visibleCount = accessBridge.Functions.GetVisibleChildrenCount(selectedNode.JvmId, selectedNode.AccessibleContextHandle);
                LogMessage("可见子组件数量: " + visibleCount);

                if (visibleCount > 0)
                {
                    VisibleChildrenInfo childrenInfo;
                    if (accessBridge.Functions.GetVisibleChildren(selectedNode.JvmId, selectedNode.AccessibleContextHandle, 0, out childrenInfo))
                    {
                        LogMessage("实际返回: " + childrenInfo.returnedChildrenCount + " 个可见子组件");

                        for (int i = 0; i < Math.Min(childrenInfo.returnedChildrenCount, 10); i++)
                        {
                            var childHandle = childrenInfo.children[i];
                            var childNode = new AccessibleContextNode(accessBridge, childHandle);
                            var childInfo = childNode.GetInfo();
                            LogMessage("  - " + (childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "] (可见)");
                        }
                    }
                }
                else
                {
                    LogMessage("没有可见子组件");
                }
            }
            catch (Exception ex)
            {
                LogMessage("GetVisibleChildren 失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestGetAccessibleSelection(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试 GetAccessibleSelection ===");
            try
            {
                var info = selectedNode.GetInfo();
                if (info.accessibleSelection != 0)
                {
                    var selCount = accessBridge.Functions.GetAccessibleSelectionCountFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle);
                    LogMessage("选择项数量: " + selCount);

                    for (int i = 0; i < Math.Min(selCount, 5); i++)
                    {
                        var selectedHandle = accessBridge.Functions.GetAccessibleSelectionFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle, i);
                        if (!selectedHandle.IsNull)
                        {
                            var selectedNodeItem = new AccessibleContextNode(accessBridge, selectedHandle);
                            var selectedInfo = selectedNodeItem.GetInfo();
                            LogMessage("  - " + (selectedInfo.name ?? "未命名") + " [" + (selectedInfo.role ?? "未知") + "] (选中)");
                        }
                    }
                }
                else
                {
                    LogMessage("此组件不支持选择功能");
                }
            }
            catch (Exception ex)
            {
                LogMessage("GetAccessibleSelection 失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestCoordinateSearch(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试坐标搜索 ===");
            try
            {
                var rect = selectedNode.GetScreenRectangle();
                if (rect.HasValue)
                {
                    var searchRect = rect.Value;
                    var foundNodes = new HashSet<string>();

                    var stepX = Math.Max(1, searchRect.Width / 5);
                    var stepY = Math.Max(1, searchRect.Height / 5);

                    LogMessage("搜索区域: " + searchRect + ", 步长: " + stepX + "x" + stepY);

                    for (int x = searchRect.X; x < searchRect.X + searchRect.Width; x += stepX)
                    {
                        for (int y = searchRect.Y; y < searchRect.Y + searchRect.Height; y += stepY)
                        {
                            try
                            {
                                JavaObjectHandle childHandle;
                                if (accessBridge.Functions.GetAccessibleContextAt(selectedNode.JvmId, selectedNode.AccessibleContextHandle, x, y, out childHandle))
                                {
                                    if (!childHandle.IsNull)
                                    {
                                        var childNode = new AccessibleContextNode(accessBridge, childHandle);
                                        var childInfo = childNode.GetInfo();
                                        var nodeDesc = (childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "] @(" + x + "," + y + ")";
                                        foundNodes.Add(nodeDesc);
                                    }
                                }
                            }
                            catch
                            {
                                // 忽略单个点的搜索错误
                            }
                        }
                    }

                    LogMessage("通过坐标搜索发现 " + foundNodes.Count + " 个唯一组件:");
                    foreach (var node in foundNodes.Take(10))
                    {
                        LogMessage("  - " + node);
                    }

                    if (foundNodes.Count > 10)
                    {
                        LogMessage("  ... 还有 " + (foundNodes.Count - 10) + " 个结果");
                    }
                }
                else
                {
                    LogMessage("无法获取组件屏幕位置");
                }
            }
            catch (Exception ex)
            {
                LogMessage("坐标搜索失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestGetAccessibleTable(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试 GetAccessibleTable ===");
            try
            {
                var info = selectedNode.GetInfo();
                if ((info.accessibleInterfaces & AccessibleInterfaces.cAccessibleTableInterface) != 0)
                {
                    AccessibleTableInfo tableInfo;
                    if (accessBridge.Functions.GetAccessibleTableInfo(selectedNode.JvmId, selectedNode.AccessibleContextHandle, out tableInfo))
                    {
                        LogMessage("表格信息: " + tableInfo.rowCount + "行 x " + tableInfo.columnCount + "列");
                        LogMessage("标题: " + (tableInfo.caption.IsNull ? "无" : "有标题"));
                        LogMessage("摘要: " + (tableInfo.summary.IsNull ? "无" : "有摘要"));

                        // 获取前几个单元格信息
                        for (int row = 0; row < Math.Min(tableInfo.rowCount, 2); row++)
                        {
                            for (int col = 0; col < Math.Min(tableInfo.columnCount, 3); col++)
                            {
                                try
                                {
                                    AccessibleTableCellInfo cellInfo;
                                    if (accessBridge.Functions.GetAccessibleTableCellInfo(selectedNode.JvmId, selectedNode.AccessibleContextHandle, row, col, out cellInfo))
                                    {
                                        LogMessage("单元格[" + row + "," + col + "]: " + (cellInfo.accessibleContext.IsNull ? "空" : "有内容"));
                                    }
                                }
                                catch
                                {
                                    // 忽略单个单元格错误
                                }
                            }
                        }
                    }
                }
                else
                {
                    LogMessage("此组件不是表格");
                }
            }
            catch (Exception ex)
            {
                LogMessage("GetAccessibleTable 失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestGetAccessibleText(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试 GetAccessibleText ===");
            try
            {
                var info = selectedNode.GetInfo();
                if (info.accessibleText != 0)
                {
                    AccessibleTextInfo textInfo;
                    if (accessBridge.Functions.GetAccessibleTextInfo(selectedNode.JvmId, selectedNode.AccessibleContextHandle, out textInfo, 0, 0))
                    {
                        LogMessage("文本长度: " + textInfo.charCount);
                        LogMessage("插入符位置: " + textInfo.caretIndex);

                        // 获取文本内容
                        if (textInfo.charCount > 0)
                        {
                            var textBuffer = new char[Math.Min(textInfo.charCount + 1, 100)];
                            if (accessBridge.Functions.GetAccessibleTextRange(selectedNode.JvmId, selectedNode.AccessibleContextHandle, 0, Math.Min(textInfo.charCount, 99), textBuffer, (short)textBuffer.Length))
                            {
                                var text = new string(textBuffer).Trim('\0');
                                LogMessage("文本内容: \"" + text.Substring(0, Math.Min(text.Length, 50)) + (text.Length > 50 ? "..." : "") + "\"");
                            }
                        }
                    }
                }
                else
                {
                    LogMessage("此组件不包含文本");
                }
            }
            catch (Exception ex)
            {
                LogMessage("GetAccessibleText 失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void TestDeepSearch(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试深度递归搜索 ===");
            try
            {
                var foundNodes = new List<string>();
                DeepSearchRecursive(selectedNode, foundNodes, 0, 3); // 最大深度3层

                LogMessage("深度递归搜索结果 (最大3层):");
                foreach (var node in foundNodes.Take(15))
                {
                    LogMessage("  " + node);
                }

                if (foundNodes.Count > 15)
                {
                    LogMessage("  ... 还有 " + (foundNodes.Count - 15) + " 个结果");
                }
            }
            catch (Exception ex)
            {
                LogMessage("深度递归搜索失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private void DeepSearchRecursive(AccessibleNode node, List<string> results, int depth, int maxDepth)
        {
            if (depth >= maxDepth) return;

            try
            {
                if (node is AccessibleContextNode)
                {
                    var contextNode = (AccessibleContextNode)node;
                    var info = contextNode.GetInfo();
                    var indent = new string(' ', depth * 2);
                    results.Add(indent + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "] (深度" + depth + ")");

                    // 尝试多种方法获取子组件
                    var allChildren = new HashSet<AccessibleNode>();

                    // 标准方法
                    try
                    {
                        var standardChildren = contextNode.GetChildren().ToList();
                        foreach (var child in standardChildren)
                        {
                            allChildren.Add(child);
                        }
                    }
                    catch { }

                    // 增强方法
                    try
                    {
                        var enhancedChildren = contextNode.GetChildrenEnhanced().ToList();
                        foreach (var child in enhancedChildren)
                        {
                            allChildren.Add(child);
                        }
                    }
                    catch { }

                    foreach (var child in allChildren.Take(3)) // 限制每层的子组件数量
                    {
                        DeepSearchRecursive(child, results, depth + 1, maxDepth);
                    }
                }
            }
            catch
            {
                // 忽略单个节点的错误
            }
        }

        private void TestCombined(object sender, EventArgs e)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 组合测试所有方法 ===");
            var results = new Dictionary<string, int>();

            try
            {
                // 测试GetChildren
                try
                {
                    var children = selectedNode.GetChildren().ToList();
                    results["GetChildren"] = children.Count;
                }
                catch
                {
                    results["GetChildren"] = 0;
                }

                // 测试GetChildrenEnhanced
                try
                {
                    var children = selectedNode.GetChildrenEnhanced().ToList();
                    results["GetChildrenEnhanced"] = children.Count;
                }
                catch
                {
                    results["GetChildrenEnhanced"] = 0;
                }

                // 测试GetVisibleChildren
                try
                {
                    var visibleCount = accessBridge.Functions.GetVisibleChildrenCount(selectedNode.JvmId, selectedNode.AccessibleContextHandle);
                    results["GetVisibleChildren"] = visibleCount;
                }
                catch
                {
                    results["GetVisibleChildren"] = 0;
                }

                // 测试GetAccessibleSelection
                try
                {
                    var info = selectedNode.GetInfo();
                    if (info.accessibleSelection != 0)
                    {
                        var selCount = accessBridge.Functions.GetAccessibleSelectionCountFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle);
                        results["GetAccessibleSelection"] = selCount;
                    }
                    else
                    {
                        results["GetAccessibleSelection"] = 0;
                    }
                }
                catch
                {
                    results["GetAccessibleSelection"] = 0;
                }

                // 汇总结果
                LogMessage("各方法发现的组件数量:");
                foreach (var result in results.OrderByDescending(x => x.Value))
                {
                    LogMessage("  " + result.Key + ": " + result.Value + " 个");
                }

                var totalResults = results.Values.Sum();
                LogMessage("总计: " + totalResults + " 个结果");

                // 推荐最佳方法
                var bestMethod = results.OrderByDescending(x => x.Value).First();
                LogMessage("推荐方法: " + bestMethod.Key + " (发现 " + bestMethod.Value + " 个组件)");

            }
            catch (Exception ex)
            {
                LogMessage("组合测试失败: " + ex.Message);
            }
            LogMessage("=== 组合测试完成 ===");
        }

        private void ClearLog(object sender, EventArgs e)
        {
            logTextBox.Clear();
            LogMessage("日志已清空");
        }

        private string GetWindowTitle(IntPtr hWnd)
        {
            var sb = new StringBuilder(256);
            GetWindowText(hWnd, sb, sb.Capacity);
            return sb.ToString();
        }

        private void LogMessage(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            logTextBox.AppendText("[" + DateTime.Now.ToString("HH:mm:ss") + "] " + message + "\r\n");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            if (accessBridge != null)
                accessBridge.Dispose();
            base.OnFormClosed(e);
        }

        #region Windows API声明

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        #endregion
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestForm());
        }
    }
}
