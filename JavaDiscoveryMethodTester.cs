using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace JavaDiscoveryMethodTester
{
    public partial class TestForm : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeView;
        private TextBox logTextBox;
        private Panel buttonPanel;
        private AccessibleContextNode selectedNode;

        public TestForm()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        private void InitializeComponent()
        {
            this.Text = "Java发现方法全面测试工具";
            this.Size = new Size(1400, 900);
            this.StartPosition = FormStartPosition.CenterScreen;

            var mainSplitContainer = new SplitContainer();
            mainSplitContainer.Dock = DockStyle.Fill;
            mainSplitContainer.Orientation = Orientation.Horizontal;
            mainSplitContainer.SplitterDistance = 600;

            var topSplitContainer = new SplitContainer();
            topSplitContainer.Dock = DockStyle.Fill;
            topSplitContainer.Orientation = Orientation.Vertical;
            topSplitContainer.SplitterDistance = 800;

            // 树视图
            treeView = new TreeView();
            treeView.Dock = DockStyle.Fill;
            treeView.AfterSelect += TreeView_AfterSelect;

            // 按钮面板
            buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Fill;
            buttonPanel.AutoScroll = true;
            CreateTestButtons();

            // 日志文本框
            logTextBox = new TextBox();
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.ReadOnly = true;
            logTextBox.Font = new Font("Consolas", 9);

            topSplitContainer.Panel1.Controls.Add(treeView);
            topSplitContainer.Panel2.Controls.Add(buttonPanel);

            mainSplitContainer.Panel1.Controls.Add(topSplitContainer);
            mainSplitContainer.Panel2.Controls.Add(logTextBox);

            this.Controls.Add(mainSplitContainer);
        }

        private void CreateTestButtons()
        {
            var buttons = new List<(string text, Action action)>
            {
                ("刷新Java窗口", RefreshJavaWindows),
                ("1. GetChildren (标准)", () => TestMethod("GetChildren", TestGetChildren)),
                ("2. GetChildrenEnhanced (增强)", () => TestMethod("GetChildrenEnhanced", TestGetChildrenEnhanced)),
                ("3. GetVisibleChildren", () => TestMethod("GetVisibleChildren", TestGetVisibleChildren)),
                ("4. GetAccessibleSelection", () => TestMethod("GetAccessibleSelection", TestGetAccessibleSelection)),
                ("5. GetAccessibleContextAt (坐标搜索)", () => TestMethod("GetAccessibleContextAt", TestGetAccessibleContextAt)),
                ("6. GetAccessibleTable", () => TestMethod("GetAccessibleTable", TestGetAccessibleTable)),
                ("7. GetAccessibleText", () => TestMethod("GetAccessibleText", TestGetAccessibleText)),
                ("8. GetAccessibleActions", () => TestMethod("GetAccessibleActions", TestGetAccessibleActions)),
                ("9. GetAccessibleValue", () => TestMethod("GetAccessibleValue", TestGetAccessibleValue)),
                ("10. GetAccessibleHypertext", () => TestMethod("GetAccessibleHypertext", TestGetAccessibleHypertext)),
                ("11. GetAccessibleRelationSet", () => TestMethod("GetAccessibleRelationSet", TestGetAccessibleRelationSet)),
                ("12. 深度递归搜索", () => TestMethod("DeepRecursiveSearch", TestDeepRecursiveSearch)),
                ("13. 网格坐标搜索", () => TestMethod("GridCoordinateSearch", TestGridCoordinateSearch)),
                ("14. 全方法组合测试", () => TestMethod("CombinedTest", TestCombinedMethods)),
                ("清空日志", ClearLog)
            };

            int y = 10;
            foreach (var (text, action) in buttons)
            {
                var button = new Button();
                button.Text = text;
                button.Location = new Point(10, y);
                button.Size = new Size(200, 30);
                button.Click += (s, e) => action();
                buttonPanel.Controls.Add(button);
                y += 35;
            }
        }

        private void InitializeAccessBridge()
        {
            try
            {
                accessBridge = new AccessBridge();
                LogMessage("Access Bridge 初始化成功");
                RefreshJavaWindows();
            }
            catch (Exception ex)
            {
                LogMessage("Access Bridge 初始化失败: " + ex.Message);
                MessageBox.Show("无法初始化 Access Bridge: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshJavaWindows()
        {
            try
            {
                treeView.Nodes.Clear();
                LogMessage("=== 开始搜索Java窗口 ===");

                var javaWindows = FindJavaWindows();
                LogMessage("发现 " + javaWindows.Count + " 个Java窗口");

                foreach (var hWnd in javaWindows)
                {
                    CreateJavaWindowNode(hWnd);
                }

                LogMessage("Java窗口搜索完成");
            }
            catch (Exception ex)
            {
                LogMessage("搜索Java窗口时出错: " + ex.Message);
            }
        }

        private List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            EnumWindows(delegate(IntPtr hWnd, IntPtr lParam)
            {
                if (IsWindowVisible(hWnd) && accessBridge.Functions.IsJavaWindow(hWnd))
                {
                    javaWindows.Add(hWnd);
                }
                return true;
            }, IntPtr.Zero);
            
            return javaWindows;
        }

        private void CreateJavaWindowNode(IntPtr hWnd)
        {
            try
            {
                var windowTitle = GetWindowTitle(hWnd);
                
                int vmId;
                JavaObjectHandle ac;
                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out vmId, out ac))
                {
                    var rootNode = new TreeNode("Java窗口: " + windowTitle);
                    
                    var accessibleWindow = accessBridge.CreateAccessibleWindow(hWnd);
                    if (accessibleWindow != null)
                    {
                        rootNode.Tag = accessibleWindow;
                        AddBasicChildren(rootNode, accessibleWindow, 0, 2);
                        treeView.Nodes.Add(rootNode);
                        rootNode.Expand();
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("错误: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                treeView.Nodes.Add(errorNode);
            }
        }

        private void AddBasicChildren(TreeNode parentNode, AccessibleNode accessibleNode, int depth, int maxDepth)
        {
            if (depth >= maxDepth) return;

            try
            {
                var children = accessibleNode.GetChildren().ToList();
                foreach (var child in children.Take(5))
                {
                    if (child is AccessibleContextNode childContext)
                    {
                        var childInfo = childContext.GetInfo();
                        var childNode = new TreeNode((childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "]");
                        childNode.Tag = child;
                        parentNode.Nodes.Add(childNode);

                        if (childInfo.childrenCount > 0)
                        {
                            AddBasicChildren(childNode, child, depth + 1, maxDepth);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("获取子组件失败: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag is AccessibleContextNode contextNode)
            {
                selectedNode = contextNode;
                var info = contextNode.GetInfo();
                LogMessage("选中组件: " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                LogMessage("  子组件数: " + info.childrenCount + ", 位置: (" + info.x + ", " + info.y + "), 大小: " + info.width + "x" + info.height);
            }
            else
            {
                selectedNode = null;
                LogMessage("请选择一个Java组件进行测试");
            }
        }

        private void TestMethod(string methodName, Func<List<string>> testFunc)
        {
            if (selectedNode == null)
            {
                LogMessage("请先选择一个Java组件");
                return;
            }

            LogMessage("=== 测试方法: " + methodName + " ===");
            try
            {
                var results = testFunc();
                LogMessage("发现 " + results.Count + " 个结果:");
                foreach (var result in results)
                {
                    LogMessage("  - " + result);
                }
            }
            catch (Exception ex)
            {
                LogMessage("测试失败: " + ex.Message);
            }
            LogMessage("=== 测试完成 ===");
        }

        private List<string> TestGetChildren()
        {
            var results = new List<string>();
            var children = selectedNode.GetChildren().ToList();
            
            foreach (var child in children)
            {
                if (child is AccessibleContextNode childContext)
                {
                    var info = childContext.GetInfo();
                    results.Add((info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                }
                else
                {
                    results.Add("未知类型: " + child.GetType().Name);
                }
            }
            
            return results;
        }

        private List<string> TestGetChildrenEnhanced()
        {
            var results = new List<string>();
            var children = selectedNode.GetChildrenEnhanced().ToList();
            
            foreach (var child in children)
            {
                if (child is AccessibleContextNode childContext)
                {
                    var info = childContext.GetInfo();
                    results.Add((info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                }
                else
                {
                    results.Add("未知类型: " + child.GetType().Name);
                }
            }
            
            return results;
        }

        private List<string> TestGetAccessibleSelection()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                if (info.accessibleSelection != 0)
                {
                    var selCount = accessBridge.Functions.GetAccessibleSelectionCountFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle);
                    LogMessage("选择项数量: " + selCount);

                    for (int i = 0; i < Math.Min(selCount, 10); i++)
                    {
                        var selectedHandle = accessBridge.Functions.GetAccessibleSelectionFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle, i);
                        if (!selectedHandle.IsNull)
                        {
                            var selectedNode = new AccessibleContextNode(accessBridge, selectedHandle);
                            var selectedInfo = selectedNode.GetInfo();
                            results.Add((selectedInfo.name ?? "未命名") + " [" + (selectedInfo.role ?? "未知") + "] (选中)");
                        }
                    }
                }
                else
                {
                    results.Add("此组件不支持选择功能");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleSelection 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleContextAt()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                var rect = selectedNode.GetScreenRectangle();

                if (rect.HasValue)
                {
                    // 在组件区域内搜索
                    var searchRect = rect.Value;
                    var stepX = Math.Max(1, searchRect.Width / 5);
                    var stepY = Math.Max(1, searchRect.Height / 5);

                    var foundNodes = new HashSet<string>();

                    for (int x = searchRect.X; x < searchRect.X + searchRect.Width; x += stepX)
                    {
                        for (int y = searchRect.Y; y < searchRect.Y + searchRect.Height; y += stepY)
                        {
                            try
                            {
                                JavaObjectHandle childHandle;
                                if (accessBridge.Functions.GetAccessibleContextAt(selectedNode.JvmId, selectedNode.AccessibleContextHandle, x, y, out childHandle))
                                {
                                    if (!childHandle.IsNull)
                                    {
                                        var childNode = new AccessibleContextNode(accessBridge, childHandle);
                                        var childInfo = childNode.GetInfo();
                                        var nodeDesc = (childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "] @(" + x + "," + y + ")";
                                        foundNodes.Add(nodeDesc);
                                    }
                                }
                            }
                            catch
                            {
                                // 忽略单个点的搜索错误
                            }
                        }
                    }

                    results.AddRange(foundNodes);
                }
                else
                {
                    results.Add("无法获取组件屏幕位置");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleContextAt 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleTable()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                if ((info.accessibleInterfaces & AccessibleInterfaces.cAccessibleTableInterface) != 0)
                {
                    AccessibleTableInfo tableInfo;
                    if (accessBridge.Functions.GetAccessibleTableInfo(selectedNode.JvmId, selectedNode.AccessibleContextHandle, out tableInfo))
                    {
                        results.Add("表格信息: " + tableInfo.rowCount + "行 x " + tableInfo.columnCount + "列");
                        results.Add("标题: " + (tableInfo.caption ?? "无"));
                        results.Add("摘要: " + (tableInfo.summary ?? "无"));

                        // 获取表格单元格信息
                        for (int row = 0; row < Math.Min(tableInfo.rowCount, 3); row++)
                        {
                            for (int col = 0; col < Math.Min(tableInfo.columnCount, 3); col++)
                            {
                                try
                                {
                                    AccessibleTableCellInfo cellInfo;
                                    if (accessBridge.Functions.GetAccessibleTableCellInfo(selectedNode.JvmId, selectedNode.AccessibleContextHandle, row, col, out cellInfo))
                                    {
                                        results.Add("单元格[" + row + "," + col + "]: " + (cellInfo.accessibleContext.IsNull ? "空" : "有内容"));
                                    }
                                }
                                catch
                                {
                                    // 忽略单个单元格错误
                                }
                            }
                        }
                    }
                }
                else
                {
                    results.Add("此组件不是表格");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleTable 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleText()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                if (info.accessibleText != 0)
                {
                    AccessibleTextInfo textInfo;
                    if (accessBridge.Functions.GetAccessibleTextInfo(selectedNode.JvmId, selectedNode.AccessibleContextHandle, out textInfo, 0, 0))
                    {
                        results.Add("文本长度: " + textInfo.charCount);
                        results.Add("插入符位置: " + textInfo.caretIndex);
                        results.Add("选择开始: " + textInfo.selectionStartIndex);
                        results.Add("选择结束: " + textInfo.selectionEndIndex);

                        // 获取文本内容
                        if (textInfo.charCount > 0)
                        {
                            var textBuffer = new char[Math.Min(textInfo.charCount + 1, 100)];
                            if (accessBridge.Functions.GetAccessibleTextRange(selectedNode.JvmId, selectedNode.AccessibleContextHandle, 0, Math.Min(textInfo.charCount, 99), textBuffer, (short)textBuffer.Length))
                            {
                                var text = new string(textBuffer).Trim('\0');
                                results.Add("文本内容: \"" + text.Substring(0, Math.Min(text.Length, 50)) + (text.Length > 50 ? "..." : "") + "\"");
                            }
                        }
                    }
                }
                else
                {
                    results.Add("此组件不包含文本");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleText 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleActions()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                if (info.accessibleAction != 0)
                {
                    AccessibleActions actions;
                    if (accessBridge.Functions.GetAccessibleActions(selectedNode.JvmId, selectedNode.AccessibleContextHandle, out actions))
                    {
                        results.Add("可用操作数量: " + actions.actionsCount);

                        for (int i = 0; i < Math.Min(actions.actionsCount, 10); i++)
                        {
                            var actionName = actions.actionInfo[i].name ?? "未命名操作";
                            results.Add("操作 " + i + ": " + actionName);
                        }
                    }
                }
                else
                {
                    results.Add("此组件不支持操作");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleActions 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleValue()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                if ((info.accessibleInterfaces & AccessibleInterfaces.cAccessibleValueInterface) != 0)
                {
                    var valueBuffer = new StringBuilder(256);

                    if (accessBridge.Functions.GetCurrentAccessibleValueFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle, valueBuffer, (short)valueBuffer.Capacity))
                    {
                        results.Add("当前值: " + valueBuffer.ToString());
                    }

                    if (accessBridge.Functions.GetMaximumAccessibleValueFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle, valueBuffer, (short)valueBuffer.Capacity))
                    {
                        results.Add("最大值: " + valueBuffer.ToString());
                    }

                    if (accessBridge.Functions.GetMinimumAccessibleValueFromContext(selectedNode.JvmId, selectedNode.AccessibleContextHandle, valueBuffer, (short)valueBuffer.Capacity))
                    {
                        results.Add("最小值: " + valueBuffer.ToString());
                    }
                }
                else
                {
                    results.Add("此组件不支持值接口");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleValue 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleHypertext()
        {
            var results = new List<string>();

            try
            {
                var info = selectedNode.GetInfo();
                if ((info.accessibleInterfaces & AccessibleInterfaces.cAccessibleHypertextInterface) != 0)
                {
                    AccessibleHypertextInfo hypertextInfo;
                    if (accessBridge.Functions.GetAccessibleHypertextExt(selectedNode.JvmId, selectedNode.AccessibleContextHandle, 0, out hypertextInfo))
                    {
                        results.Add("超文本长度: " + hypertextInfo.accessibleText.charCount);
                        results.Add("链接数量: " + hypertextInfo.linkCount);

                        for (int i = 0; i < Math.Min(hypertextInfo.linkCount, 5); i++)
                        {
                            try
                            {
                                AccessibleHyperlinkInfo linkInfo;
                                if (accessBridge.Functions.GetAccessibleHyperlink(selectedNode.JvmId, selectedNode.AccessibleContextHandle, i, out linkInfo))
                                {
                                    results.Add("链接 " + i + ": " + (linkInfo.text ?? "无文本"));
                                }
                            }
                            catch
                            {
                                // 忽略单个链接错误
                            }
                        }
                    }
                }
                else
                {
                    results.Add("此组件不支持超文本");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleHypertext 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestGetAccessibleRelationSet()
        {
            var results = new List<string>();

            try
            {
                AccessibleRelationSetInfo relationSetInfo;
                if (accessBridge.Functions.GetAccessibleRelationSet(selectedNode.JvmId, selectedNode.AccessibleContextHandle, out relationSetInfo))
                {
                    results.Add("关系数量: " + relationSetInfo.relationCount);

                    for (int i = 0; i < Math.Min(relationSetInfo.relationCount, 5); i++)
                    {
                        var relation = relationSetInfo.relations[i];
                        results.Add("关系 " + i + ": " + (relation.key ?? "未知关系") + " (目标数: " + relation.targetCount + ")");
                    }
                }
                else
                {
                    results.Add("无法获取关系信息");
                }
            }
            catch (Exception ex)
            {
                results.Add("GetAccessibleRelationSet 失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestDeepRecursiveSearch()
        {
            var results = new List<string>();

            try
            {
                var foundNodes = new List<string>();
                DeepSearchRecursive(selectedNode, foundNodes, 0, 4); // 最大深度4层

                results.Add("深度递归搜索结果 (最大4层):");
                results.AddRange(foundNodes.Take(20)); // 限制显示数量

                if (foundNodes.Count > 20)
                {
                    results.Add("... 还有 " + (foundNodes.Count - 20) + " 个结果");
                }
            }
            catch (Exception ex)
            {
                results.Add("深度递归搜索失败: " + ex.Message);
            }

            return results;
        }

        private void DeepSearchRecursive(AccessibleNode node, List<string> results, int depth, int maxDepth)
        {
            if (depth >= maxDepth) return;

            try
            {
                if (node is AccessibleContextNode contextNode)
                {
                    var info = contextNode.GetInfo();
                    var indent = new string(' ', depth * 2);
                    results.Add(indent + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "] (深度" + depth + ")");

                    // 尝试多种方法获取子组件
                    var allChildren = new HashSet<AccessibleNode>();

                    // 标准方法
                    try
                    {
                        allChildren.UnionWith(contextNode.GetChildren());
                    }
                    catch { }

                    // 增强方法
                    try
                    {
                        allChildren.UnionWith(contextNode.GetChildrenEnhanced());
                    }
                    catch { }

                    foreach (var child in allChildren.Take(5)) // 限制每层的子组件数量
                    {
                        DeepSearchRecursive(child, results, depth + 1, maxDepth);
                    }
                }
            }
            catch
            {
                // 忽略单个节点的错误
            }
        }

        private List<string> TestGridCoordinateSearch()
        {
            var results = new List<string>();

            try
            {
                var rect = selectedNode.GetScreenRectangle();
                if (rect.HasValue)
                {
                    var searchRect = rect.Value;
                    var foundNodes = new HashSet<string>();

                    // 更密集的网格搜索
                    var stepX = Math.Max(1, searchRect.Width / 10);
                    var stepY = Math.Max(1, searchRect.Height / 10);

                    LogMessage("网格搜索区域: " + searchRect + ", 步长: " + stepX + "x" + stepY);

                    for (int x = searchRect.X; x < searchRect.X + searchRect.Width; x += stepX)
                    {
                        for (int y = searchRect.Y; y < searchRect.Y + searchRect.Height; y += stepY)
                        {
                            try
                            {
                                JavaObjectHandle childHandle;
                                if (accessBridge.Functions.GetAccessibleContextAt(selectedNode.JvmId, selectedNode.AccessibleContextHandle, x, y, out childHandle))
                                {
                                    if (!childHandle.IsNull)
                                    {
                                        var childNode = new AccessibleContextNode(accessBridge, childHandle);
                                        var childInfo = childNode.GetInfo();
                                        var nodeDesc = (childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "] @(" + x + "," + y + ")";
                                        foundNodes.Add(nodeDesc);
                                    }
                                }
                            }
                            catch
                            {
                                // 忽略单个点的搜索错误
                            }
                        }
                    }

                    results.AddRange(foundNodes.Take(30));
                    if (foundNodes.Count > 30)
                    {
                        results.Add("... 还有 " + (foundNodes.Count - 30) + " 个结果");
                    }
                }
                else
                {
                    results.Add("无法获取组件屏幕位置");
                }
            }
            catch (Exception ex)
            {
                results.Add("网格坐标搜索失败: " + ex.Message);
            }

            return results;
        }

        private List<string> TestCombinedMethods()
        {
            var results = new List<string>();
            var allResults = new Dictionary<string, int>();

            try
            {
                LogMessage("开始组合测试所有方法...");

                // 测试所有方法并统计结果
                var methods = new Dictionary<string, Func<List<string>>>
                {
                    {"GetChildren", TestGetChildren},
                    {"GetChildrenEnhanced", TestGetChildrenEnhanced},
                    {"GetVisibleChildren", TestGetVisibleChildren},
                    {"GetAccessibleSelection", TestGetAccessibleSelection},
                    {"GetAccessibleContextAt", TestGetAccessibleContextAt},
                    {"GetAccessibleTable", TestGetAccessibleTable},
                    {"GetAccessibleText", TestGetAccessibleText},
                    {"GetAccessibleActions", TestGetAccessibleActions},
                    {"GetAccessibleValue", TestGetAccessibleValue},
                    {"GetAccessibleHypertext", TestGetAccessibleHypertext},
                    {"GetAccessibleRelationSet", TestGetAccessibleRelationSet},
                    {"DeepRecursiveSearch", TestDeepRecursiveSearch},
                    {"GridCoordinateSearch", TestGridCoordinateSearch}
                };

                foreach (var method in methods)
                {
                    try
                    {
                        var methodResults = method.Value();
                        allResults[method.Key] = methodResults.Count;
                        LogMessage(method.Key + ": " + methodResults.Count + " 个结果");
                    }
                    catch (Exception ex)
                    {
                        allResults[method.Key] = 0;
                        LogMessage(method.Key + ": 失败 - " + ex.Message);
                    }
                }

                // 汇总结果
                results.Add("=== 组合测试汇总 ===");
                var sortedResults = allResults.OrderByDescending(x => x.Value);
                foreach (var result in sortedResults)
                {
                    results.Add(result.Key + ": " + result.Value + " 个结果");
                }

                var totalResults = allResults.Values.Sum();
                results.Add("总计: " + totalResults + " 个结果");

                // 推荐最佳方法
                var bestMethod = sortedResults.First();
                results.Add("推荐方法: " + bestMethod.Key + " (发现最多内容)");

            }
            catch (Exception ex)
            {
                results.Add("组合测试失败: " + ex.Message);
            }

            return results;
        }

        private void ClearLog()
        {
            logTextBox.Clear();
            LogMessage("日志已清空");
        }

        private string GetWindowTitle(IntPtr hWnd)
        {
            var sb = new StringBuilder(256);
            GetWindowText(hWnd, sb, sb.Capacity);
            return sb.ToString();
        }

        private void LogMessage(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            logTextBox.AppendText("[" + DateTime.Now.ToString("HH:mm:ss") + "] " + message + "\r\n");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            if (accessBridge != null)
                accessBridge.Dispose();
            base.OnFormClosed(e);
        }

        #region Windows API声明

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        #endregion
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestForm());
        }
    }
}
