@echo off
echo ==============================================
echo      NC专用Java程序发现和遍历工具编译脚本
echo ==============================================
echo.

:: 设置编码为UTF-8
chcp 65001 >nul

:: 检查MSBuild是否存在
where msbuild >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到MSBuild，正在尝试使用Visual Studio构建工具...
    
    :: 尝试找到Visual Studio构建工具
    set "MSBUILD_PATH="
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe" (
        set "MSBUILD_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
    )
    
    if "%MSBUILD_PATH%"=="" (
        echo ❌ 未找到MSBuild工具
        echo 💡 请安装Visual Studio或Visual Studio Build Tools
        pause
        exit /b 1
    )
    
    echo ✅ 找到MSBuild: %MSBUILD_PATH%
    set "MSBUILD_CMD=%MSBUILD_PATH%"
) else (
    set "MSBUILD_CMD=msbuild"
)

echo.
echo 🔍 步骤1: 检查项目文件...
if not exist "NCTraverser.csproj" (
    echo ❌ 未找到NCTraverser.csproj文件
    pause
    exit /b 1
)
echo ✅ 项目文件存在

echo.
echo 🔍 步骤2: 检查源代码文件...
if not exist "NCTraverser.cs" (
    echo ❌ 未找到NCTraverser.cs源代码文件
    pause
    exit /b 1
)
echo ✅ 源代码文件存在

echo.
echo 🔍 步骤3: 检查依赖项目...
if not exist "src\WindowsAccessBridgeInterop\WindowsAccessBridgeInterop.csproj" (
    echo ❌ 未找到WindowsAccessBridgeInterop依赖项目
    echo 💡 请确保完整的项目结构
    pause
    exit /b 1
)
echo ✅ 依赖项目存在

echo.
echo 🔧 步骤4: 开始编译...
echo 编译配置: Debug
echo 目标平台: AnyCPU

"%MSBUILD_CMD%" NCTraverser.csproj /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ 编译成功！
    echo.
    
    if exist "NCTraverser.exe" (
        echo 📁 生成的文件:
        echo    NCTraverser.exe - 主程序
        echo.
        
        echo 📊 文件信息:
        for %%f in (NCTraverser.exe) do echo    大小: %%~zf 字节
        
        echo.
        echo 🚀 使用方法:
        echo    NCTraverser.exe              - 遍历所有Java应用程序
        echo    NCTraverser.exe NC           - 只遍历NC相关应用程序
        echo    NCTraverser.exe yonyou       - 只遍历用友相关应用程序
        echo.
        
        echo 💡 提示:
        echo 1. 请确保目标Java应用程序正在运行
        echo 2. 确保Java Access Bridge已正确安装
        echo 3. 某些应用可能需要管理员权限
        echo.
        
        set /p test_run="是否立即测试运行？(Y/N): "
        if /i "%test_run%"=="Y" (
            echo.
            echo 🧪 开始测试运行...
            NCTraverser.exe
        )
        
    ) else (
        echo ❌ 编译成功但未找到输出文件
        echo 💡 检查输出路径设置
    )
    
) else (
    echo.
    echo ❌ 编译失败！
    echo 💡 常见解决方案:
    echo 1. 检查.NET Framework版本是否正确
    echo 2. 确保所有依赖项目都能正常编译
    echo 3. 检查项目引用路径是否正确
    echo.
    
    echo 🔧 尝试手动编译依赖项目...
    "%MSBUILD_CMD%" src\WindowsAccessBridgeInterop\WindowsAccessBridgeInterop.csproj /p:Configuration=Debug /verbosity:minimal
    
    if %ERRORLEVEL% EQU 0 (
        echo ✅ 依赖项目编译成功，重新编译主项目...
        "%MSBUILD_CMD%" NCTraverser.csproj /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal
        
        if %ERRORLEVEL% EQU 0 (
            echo ✅ 编译最终成功！
        ) else (
            echo ❌ 主项目编译仍然失败
        )
    ) else (
        echo ❌ 依赖项目编译失败
    )
)

echo.
echo 按任意键退出...
pause >nul
