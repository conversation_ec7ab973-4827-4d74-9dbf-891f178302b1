using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace NCTraverser
{
    /// <summary>
    /// NC遍历器修复版本 - 直接P/Invoke调用
    /// 基于对JavaFerret成功实现的分析，使用正确的DLL函数调用
    /// </summary>
    class NCTraverserFixed3Simple
    {
        #region DLL导入

        // 正确的DLL函数调用（基于JavaFerret的成功实现）
        [DllImport("WindowsAccessBridge-64.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern void Windows_run();

        [DllImport("WindowsAccessBridge-64.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern bool IsJavaWindow(IntPtr hwnd);

        [DllImport("WindowsAccessBridge-64.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern bool GetAccessibleContextFromHWND(IntPtr hwnd, out int vmID, out IntPtr accessibleContext);

        // 枚举窗口相关
        public delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        [DllImport("user32.dll")]
        static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

        [DllImport("user32.dll")]
        static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        [DllImport("user32.dll")]
        static extern int GetWindowTextLength(IntPtr hWnd);

        [DllImport("user32.dll")]
        static extern bool IsWindow(IntPtr hWnd);

        #endregion

        private static StreamWriter logWriter;
        private static List<IntPtr> javaWindows = new List<IntPtr>();

        static void Main(string[] args)
        {
            string logFile = Path.Combine(Environment.CurrentDirectory, "NCTraverserFixed3Simple_Log.txt");
            
            try
            {
                // 初始化日志
                logWriter = new StreamWriter(logFile, false, Encoding.UTF8);
                logWriter.AutoFlush = true;

                WriteLog("=== NC遍历器修复版本 (简化直接调用) ===");
                WriteLog($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                WriteLog($"版本: Fixed3Simple - 直接P/Invoke调用版本");
                WriteLog($"日志文件: {logFile}");
                WriteLog("");

                // 系统环境检查
                PerformSystemCheck();

                // 初始化Access Bridge（使用JavaFerret的方式）
                if (!InitializeAccessBridge())
                {
                    WriteLog("❌ Access Bridge初始化失败");
                    return;
                }

                WriteLog("✅ Access Bridge初始化成功");

                // 发现Java应用程序
                DiscoverJavaApplications();

                WriteLog("\n=== 程序执行完毕 ===");
            }
            catch (Exception ex)
            {
                WriteLog($"❌ 程序异常: {ex.Message}");
                WriteLog($"调用栈: {ex.StackTrace}");
            }
            finally
            {
                if (logWriter != null)
                {
                    logWriter.Close();
                    logWriter.Dispose();
                }

                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }

        static void WriteLog(string message)
        {
            string logMessage = $"[{DateTime.Now:HH:mm:ss.fff}] {message}";
            Console.WriteLine(logMessage);

            try
            {
                logWriter?.WriteLine(logMessage);
            }
            catch (Exception)
            {
                // 忽略日志写入错误
            }
        }

        static void PerformSystemCheck()
        {
            WriteLog("=== 系统环境检查 ===");

            // 操作系统信息
            WriteLog($"操作系统: {Environment.OSVersion}");
            WriteLog($"处理器架构: {Environment.ProcessorCount} cores, {(Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit")} OS");
            WriteLog($"进程架构: {(Environment.Is64BitProcess ? "64-bit" : "32-bit")} process");

            // .NET Framework版本
            WriteLog($".NET Framework: {Environment.Version}");

            // Access Bridge DLL检查
            CheckAccessBridgeDLLs();

            WriteLog("");
        }

        static void CheckAccessBridgeDLLs()
        {
            WriteLog("--- Access Bridge DLL检查 ---");

            string[] requiredDlls = Environment.Is64BitProcess 
                ? new[] { "WindowsAccessBridge-64.dll", "WindowsAccessBridge.dll" }
                : new[] { "WindowsAccessBridge-32.dll", "WindowsAccessBridge.dll" };

            foreach (string dll in requiredDlls)
            {
                if (File.Exists(dll))
                {
                    var info = FileVersionInfo.GetVersionInfo(dll);
                    WriteLog($"✅ 找到 {dll} - 版本: {info.FileVersion}");
                }
                else if (File.Exists(Path.Combine("accessbridge2_0_2", dll)))
                {
                    var info = FileVersionInfo.GetVersionInfo(Path.Combine("accessbridge2_0_2", dll));
                    WriteLog($"✅ 找到 accessbridge2_0_2\\{dll} - 版本: {info.FileVersion}");
                }
                else
                {
                    WriteLog($"⚠️ 未找到 {dll}");
                }
            }
        }

        static bool InitializeAccessBridge()
        {
            WriteLog("=== 初始化Access Bridge（JavaFerret方式）===");

            try
            {
                WriteLog("调用 Windows_run() - 这是JavaFerret使用的初始化方法");
                Windows_run();
                WriteLog("✅ Windows_run() 调用成功");

                // 等待一点时间让初始化完成
                System.Threading.Thread.Sleep(1000);
                WriteLog("等待Access Bridge初始化完成...");

                return true;
            }
            catch (Exception ex)
            {
                WriteLog($"❌ Access Bridge初始化失败: {ex.Message}");
                WriteLog($"详细错误: {ex}");
                return false;
            }
        }

        static void DiscoverJavaApplications()
        {
            WriteLog("=== 发现Java应用程序（JavaFerret方式）===");

            try
            {
                WriteLog("正在枚举所有窗口并检查Java窗口...");
                javaWindows.Clear();

                // 枚举所有窗口
                EnumWindows(new EnumWindowsProc(EnumWindowCallback), IntPtr.Zero);

                WriteLog($"发现 {javaWindows.Count} 个Java窗口");

                if (javaWindows.Count == 0)
                {
                    WriteLog("❌ 没有发现运行中的Java应用程序");
                    WriteLog("请确保:");
                    WriteLog("1. 已启动Java应用程序（如用友NC）");
                    WriteLog("2. Java Access Bridge已正确安装");
                    WriteLog("3. 当前进程架构与Java进程架构匹配");
                    return;
                }

                // 分析每个Java窗口
                for (int i = 0; i < javaWindows.Count; i++)
                {
                    IntPtr hwnd = javaWindows[i];
                    WriteLog($"\n--- Java窗口 {i + 1}/{javaWindows.Count} ---");
                    WriteLog($"句柄: {hwnd}");
                    
                    string title = GetWindowTitle(hwnd);
                    WriteLog($"标题: {title}");

                    // 检查是否为NC系统窗口
                    if (title.ToLower().Contains("nc") || title.ToLower().Contains("用友") || title.ToLower().Contains("ufida"))
                    {
                        WriteLog($"🎯 发现NC系统窗口！正在分析...");
                        AnalyzeJavaWindow(hwnd, title);
                    }
                    else
                    {
                        WriteLog("普通Java窗口，跳过详细分析");
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"❌ 发现Java应用程序时出错: {ex.Message}");
                WriteLog($"详细错误: {ex}");
            }
        }

        static bool EnumWindowCallback(IntPtr hWnd, IntPtr lParam)
        {
            try
            {
                if (IsWindow(hWnd) && IsJavaWindow(hWnd))
                {
                    javaWindows.Add(hWnd);
                    string title = GetWindowTitle(hWnd);
                    WriteLog($"发现Java窗口: {hWnd} - {title}");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"⚠️ 检查窗口 {hWnd} 时出错: {ex.Message}");
            }

            return true; // 继续枚举
        }

        static string GetWindowTitle(IntPtr hWnd)
        {
            try
            {
                int length = GetWindowTextLength(hWnd);
                if (length == 0)
                    return "[无标题]";

                StringBuilder sb = new StringBuilder(length + 1);
                GetWindowText(hWnd, sb, sb.Capacity);
                return sb.ToString();
            }
            catch (Exception ex)
            {
                return $"[获取标题失败: {ex.Message}]";
            }
        }

        static void AnalyzeJavaWindow(IntPtr hwnd, string title)
        {
            try
            {
                WriteLog($"=== NC窗口分析: {title} ===");

                // 尝试获取AccessibleContext
                int vmID;
                IntPtr accessibleContext;
                bool success = GetAccessibleContextFromHWND(hwnd, out vmID, out accessibleContext);

                WriteLog($"GetAccessibleContextFromHWND 结果: {success}");
                if (success)
                {
                    WriteLog($"✅ 成功获取AccessibleContext!");
                    WriteLog($"VM ID: {vmID}");
                    WriteLog($"AccessibleContext: {accessibleContext}");

                    // 这里可以进一步调用其他Access Bridge函数来获取更多信息
                    // 但首先我们验证基本连接是否工作
                }
                else
                {
                    WriteLog($"❌ 无法获取AccessibleContext");
                }
            }
            catch (Exception ex)
            {
                WriteLog($"❌ 分析Java窗口时出错: {ex.Message}");
                WriteLog($"详细错误: {ex}");
            }
        }
    }
} 