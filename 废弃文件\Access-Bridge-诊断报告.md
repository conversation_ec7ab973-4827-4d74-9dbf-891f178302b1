# Java Access Bridge 2.0.2 安装诊断报告

## 📊 诊断结果总览

### ✅ 已正确安装的组件

- Java 8 JDK/JRE：`C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot`
- WindowsAccessBridge-64.dll (系统目录)：213,096 bytes
- JavaAccessBridge-64.dll (Java bin 目录)：156,776 bytes
- JAWTAccessBridge-64.dll (Java bin 目录)：22,120 bytes
- jaccess.jar (Java ext 目录)：44,516 bytes
- access-bridge-64.jar (Java ext 目录)：197,144 bytes

### ❌ 发现的问题

#### 1. 关键问题：accessibility.properties 配置被注释

**当前状态：**

```properties
#assistive_technologies=com.sun.java.accessibility.AccessBridge
#screen_magnifier_present=true
```

**正确配置应该是：**

```properties
assistive_technologies=com.sun.java.accessibility.AccessBridge
screen_magnifier_present=true
```

#### 2. 文件版本不匹配问题

- **官方 Access Bridge 2.0.2**: WindowsAccessBridge-64.dll = 116,736 bytes
- **当前系统**: WindowsAccessBridge-64.dll = 213,096 bytes

**分析：** 系统中的文件是来自更新版本的 Java（可能是 Java 8u452），而不是官方 Access Bridge 2.0.2 包。

#### 3. 缺失的 32 位支持文件

- WindowsAccessBridge-32.dll 未安装到 `C:\Windows\SysWOW64\`

## 🎯 问题影响分析

### 主要问题：accessibility.properties 配置错误

这是导致用友 NC 系统无法完全访问的**根本原因**：

- Access Bridge 功能未完全激活
- Java 应用程序的可访问性支持被禁用
- 这解释了为什么只能看到窗口框架，看不到内部控件

### 次要问题：版本差异

- 当前系统使用的是更新的 Access Bridge 版本（来自 Java 8u452）
- 官方 Access Bridge 2.0.2 是较旧的版本
- **建议：保持当前版本，因为新版本通常有更好的兼容性**

## 🔧 解决方案

### 立即修复：启用 accessibility.properties

**方法 1：直接复制官方配置文件**

```bash
# 以管理员权限运行
copy "accessbridge2_0_2\accessibility.properties" "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\lib\"
```

**方法 2：手动编辑配置文件**
编辑 `C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre\lib\accessibility.properties`：

- 删除 `assistive_technologies=com.sun.java.accessibility.AccessBridge` 前面的 `#`
- 删除 `screen_magnifier_present=true` 前面的 `#`

### 可选修复：安装 32 位支持（如果需要 32 位 Java 应用支持）

```bash
copy "accessbridge2_0_2\WindowsAccessBridge-32.dll" "C:\Windows\SysWOW64\"
```

## 📋 验证步骤

1. **修复配置文件后**：

   ```bash
   # 重新启用Access Bridge
   "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -disable
   "C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -enable
   ```

2. **重启用友 NC 系统**

3. **使用测试工具验证**：

   ```bash
   # 运行官方测试工具
   accessbridge2_0_2\JavaFerret-64.exe
   ```

4. **测试 AccessBridgeExplorer**

## 🎯 预期结果

修复 `accessibility.properties` 配置后，您应该能够：

- 在 AccessBridgeExplorer 中看到完整的用友 NC 组件树
- 访问之前显示为空的 Panel 内容
- 获取详细的组件属性信息

## 🚀 快速修复脚本

已创建 `fix-accessibility-properties.bat` 脚本进行快速修复。

## 📝 技术说明

### 为什么会出现这个问题？

1. **Eclipse Adoptium (Temurin) Java 发行版**默认禁用了 accessibility 支持
2. **安全考虑**：避免不必要的可访问性开销
3. **需要显式启用**：通过配置文件激活

### 用友 NC 为什么受影响？

- 用友 NC 是较旧的 Java 应用程序，依赖于 Java Access Bridge
- 需要完整的可访问性支持才能被外部工具检测
- 配置错误导致只能访问顶层窗口，无法深入到业务组件

这个诊断解释了您截图中看到的问题：能看到窗口结构但无法展开主要内容 Panel 的原因！
