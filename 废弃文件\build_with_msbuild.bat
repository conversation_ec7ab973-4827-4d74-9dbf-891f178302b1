@echo off
setlocal enabledelayedexpansion

echo ==============================
echo 使用MSBuild编译Java元素遍历工具
echo ==============================

:: 检查MSBuild是否可用
where msbuild >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: 找不到MSBuild命令
    echo 请确保已安装Visual Studio或Build Tools
    echo 或者运行Developer Command Prompt
    pause
    exit /b 1
)

:: 检查依赖DLL是否存在
if not exist "bin\Debug\WindowsAccessBridgeInterop.dll" (
    echo 错误: 找不到WindowsAccessBridgeInterop.dll
    echo 请确保该文件存在于 bin\Debug\ 目录中
    pause
    exit /b 1
)

echo.
echo 正在清理旧的编译文件...
del /q JavaAccessBridgeQuickTest.exe 2>nul
del /q JavaElementDumper.exe 2>nul
del /q JavaElementDumperEnhanced.exe 2>nul

echo.
echo 正在使用MSBuild编译所有工具...

:: 编译快速测试工具
echo.
echo [1/3] 编译快速测试工具...
msbuild JavaElementTools.csproj /t:BuildTest /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo 快速测试工具编译失败!
    pause
    exit /b 1
)

:: 编译基础版本
echo.
echo [2/3] 编译基础版本...
msbuild JavaElementTools.csproj /t:BuildBasic /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo 基础版本编译失败!
    pause
    exit /b 1
)

:: 编译增强版本
echo.
echo [3/3] 编译增强版本...
msbuild JavaElementTools.csproj /t:BuildEnhanced /p:Configuration=Debug /p:Platform=AnyCPU /verbosity:minimal
if %ERRORLEVEL% neq 0 (
    echo 增强版本编译失败!
    pause
    exit /b 1
)

echo.
echo ==============================
echo 编译成功!
echo ==============================

:: 检查生成的文件
echo.
echo 检查生成的文件:
if exist "JavaAccessBridgeQuickTest.exe" (
    echo   ✓ JavaAccessBridgeQuickTest.exe - 快速测试工具
) else (
    echo   ✗ JavaAccessBridgeQuickTest.exe - 未生成
)

if exist "JavaElementDumper.exe" (
    echo   ✓ JavaElementDumper.exe - 基础版遍历工具
) else (
    echo   ✗ JavaElementDumper.exe - 未生成
)

if exist "JavaElementDumperEnhanced.exe" (
    echo   ✓ JavaElementDumperEnhanced.exe - 增强版遍历工具
) else (
    echo   ✗ JavaElementDumperEnhanced.exe - 未生成
)

echo.
echo ==============================
echo 使用说明
echo ==============================
echo.
echo 1. 首先启动你的Java应用程序 (如用友NC)
echo 2. 运行测试工具验证环境:
echo    JavaAccessBridgeQuickTest.exe
echo.
echo 3. 如果测试通过，运行遍历工具:
echo    JavaElementDumper.exe          (基础版本)
echo    JavaElementDumperEnhanced.exe  (增强版本，推荐)
echo.
echo 4. 查看生成的文本文件了解遍历结果
echo.
echo 建议使用增强版本，它使用多种策略来发现更多的Java元素
echo.
pause
