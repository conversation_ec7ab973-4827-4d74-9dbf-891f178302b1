﻿@echo off
echo ===============================================
echo 增强版 Java 元素深度遍历工具
echo ===============================================
echo.
echo 正在初始化并开始深度遍历...
echo 输出文件: java_traverse_results.txt
echo.

powershell -Command "& { 
function Write-Log($message) { 
    Write-Host $message; 
    Add-Content -Path 'java_traverse_results.txt' -Value $message -Encoding UTF8; 
} 

function Traverse-Element($element, $depth, $maxDepth) { 
    if ($depth -gt $maxDepth) { return } 
    $indent = '  ' * $depth; 
    
    try { 
        $title = $element.GetTitle(); 
        if (-not $title) { $title = '无标题' } 
        
        $info = ''; 
        if ($element.GetType().Name -eq 'AccessibleContextNode') { 
            try { 
                $contextInfo = $element.GetInfo(); 
                $rect = $element.GetScreenRectangle(); 
                $info = ' | 角色:' + ($contextInfo.role -replace $null, '无') + ' | 名称:' + ($contextInfo.name -replace $null, '无') + ' | 子元素:' + $contextInfo.childrenCount; 
                if ($rect) { 
                    $info += ' | 位置:(' + $rect.X + ',' + $rect.Y + ') ' + $rect.Width + 'x' + $rect.Height; 
                } 
            } catch { 
                $info = ' | 获取详细信息失败: ' + $_.Exception.Message; 
            } 
        } 
        
        Write-Log ('[$depth] ' + $indent + $title + $info); 
        
        # 获取子元素 - 使用多种方法 
        $allChildren = @(); 
        
        # 方法1: 标准获取 
        try { 
            $standardChildren = $element.GetChildren(); 
            foreach ($child in $standardChildren) { 
                if ($child) { $allChildren += $child } 
            } 
            Write-Log ($indent + '  策略1(标准): 发现 ' + $standardChildren.Count + ' 个子元素'); 
        } catch { 
            Write-Log ($indent + '  策略1(标准): 失败 - ' + $_.Exception.Message); 
        } 
        
        # 方法2: 可见子元素（仅对AccessibleContextNode） 
        if ($element.GetType().Name -eq 'AccessibleContextNode') { 
            try { 
                $bridge = $element.AccessBridge; 
                $handle = $element.AccessibleContextHandle; 
                $vmId = $element.JvmId; 
                
                $visibleCount = $bridge.Functions.GetVisibleChildrenCount($vmId, $handle); 
                if ($visibleCount -gt 0) { 
                    $childrenInfo = $null; 
                    if ($bridge.Functions.GetVisibleChildren($vmId, $handle, 0, [ref]$childrenInfo)) { 
                        for ($i = 0; $i -lt [Math]::Min($childrenInfo.returnedChildrenCount, 20); $i++) { 
                            $childHandle = $childrenInfo.children[$i]; 
                            if (-not $childHandle.IsNull) { 
                                $childNode = New-Object WindowsAccessBridgeInterop.AccessibleContextNode($bridge, $childHandle); 
                                $allChildren += $childNode; 
                            } 
                        } 
                        Write-Log ($indent + '  策略2(可见): 发现 ' + $childrenInfo.returnedChildrenCount + ' 个子元素'); 
                    } 
                } else { 
                    Write-Log ($indent + '  策略2(可见): 无可见子元素'); 
                } 
            } catch { 
                Write-Log ($indent + '  策略2(可见): 失败 - ' + $_.Exception.Message); 
            } 
            
            # 方法3: 直接枚举子元素 
            try { 
                $contextInfo = $element.GetInfo(); 
                $childCount = $contextInfo.childrenCount; 
                if ($childCount -gt 0) { 
                    Write-Log ($indent + '  策略3(直接): 尝试获取 ' + $childCount + ' 个子元素'); 
                    for ($i = 0; $i -lt [Math]::Min($childCount, 10); $i++) { 
                        try { 
                            $childHandle = $bridge.Functions.GetAccessibleChildFromContext($vmId, $handle, $i); 
                            if (-not $childHandle.IsNull) { 
                                $childNode = New-Object WindowsAccessBridgeInterop.AccessibleContextNode($bridge, $childHandle); 
                                $allChildren += $childNode; 
                            } 
                        } catch { 
                            Write-Log ($indent + '    子元素' + $i + '获取失败: ' + $_.Exception.Message); 
                        } 
                    } 
                } 
            } catch { 
                Write-Log ($indent + '  策略3(直接): 失败 - ' + $_.Exception.Message); 
            } 
        } 
        
        # 去重并遍历子元素 
        $uniqueChildren = $allChildren | Sort-Object -Property {$_.GetHashCode()} -Unique; 
        Write-Log ($indent + '  总计发现 ' + $uniqueChildren.Count + ' 个唯一子元素'); 
        
        foreach ($child in $uniqueChildren) { 
            if ($child) { 
                Traverse-Element $child ($depth + 1) $maxDepth; 
            } 
        } 
        
    } catch { 
        Write-Log ($indent + '处理元素失败: ' + $_.Exception.Message); 
    } 
} 

# 主程序开始 
Remove-Item -Path 'java_traverse_results.txt' -ErrorAction SilentlyContinue; 
Write-Log '=== 增强版 Java 元素深度遍历报告 ==='; 
Write-Log ('时间: ' + (Get-Date)); 
Write-Log '使用策略: 标准遍历 + 可见子组件 + 直接枚举'; 
Write-Log ''; 

Add-Type -Path 'bin\Debug\WindowsAccessBridgeInterop.dll'; 
$bridge = New-Object WindowsAccessBridgeInterop.AccessBridge; 
$jvms = $bridge.EnumJvms(); 

Write-Log ('发现 ' + $jvms.Count + ' 个Java虚拟机'); 

if ($jvms.Count -eq 0) { 
    Write-Log '没有发现运行中的Java应用程序'; 
    Write-Log '请启动Java应用程序（如用友NC）然后重试'; 
} else { 
    $elementCount = 0; 
    foreach ($jvm in $jvms) { 
        Write-Log (''); 
        Write-Log ('=== JVM: ' + $jvm.GetTitle() + ' ==='); 
        
        foreach ($window in $jvm.Windows) { 
            Write-Log (''); 
            Write-Log ('--- 窗口: ' + $window.GetTitle() + ' ---'); 
            Traverse-Element $window 0 5; # 限制最大深度为5 
        } 
    } 
} 

$bridge.Dispose(); 
Write-Log ''; 
Write-Log '=== 遍历完成 ==='; 
Write-Host ''; 
Write-Host '遍历完成！结果已保存到 java_traverse_results.txt' -ForegroundColor Green; 
}"

echo.
echo ===============================================
echo 遍历完成！
echo ===============================================
echo.
echo 请查看 java_traverse_results.txt 文件了解详细结果
echo.
echo 如果发现用友NC等应用的子元素无法访问：
echo   1. 这确认了问题的存在
echo   2. 可能需要更深层的API调用
echo   3. 可以参考Java Monkey/Ferret的实现方式
echo.

pause 