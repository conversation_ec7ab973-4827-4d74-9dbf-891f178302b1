===============================================
NC专用命令行遍历工具 - 使用说明
===============================================

工具简介：
---------
这是一个专门为用友NC 5.7系统设计的Java元素遍历工具，能够完整遍历NC系统的结构树，
比Google的AccessBridgeExplorer更全面，可以发现更多的Java可访问元素。

编译完成：
---------
✅ NCTraverser.exe - 主要遍历工具 (11,264 字节)

使用前准备：
-----------
1. 确保用友NC 5.7应用程序正在运行
2. 确保Java Access Bridge已正确安装和启用
3. 确保WindowsAccessBridgeInterop.dll在bin\Debug目录中

使用方法：
---------

1. 遍历所有Java应用程序：
   NCTraverser.exe

2. 只遍历包含"NC"的应用程序：
   NCTraverser.exe NC

3. 只遍历包含"yonyou"的应用程序：
   NCTraverser.exe yonyou

4. 指定其他应用程序名称：
   NCTraverser.exe [应用程序名称]

输出文件：
---------
工具会生成详细的日志文件：
- 文件名格式：NC_Elements_Complete_YYYYMMDD_HHMMSS.log
- 包含完整的结构树信息
- 使用UTF-8编码，可用任何文本编辑器打开

遍历策略：
---------
工具使用多种策略确保完整性：
1. 标准子元素遍历 - 使用GetChildren()方法
2. 增强子组件遍历 - 使用GetChildrenEnhanced()方法
3. 深度递归遍历 - 避免遗漏嵌套元素
4. 重复元素过滤 - 避免无限循环

日志内容说明：
-------------
每个元素包含以下信息：
- 元素编号：[1], [2], [3]...
- 角色类型：button, text, panel等
- 元素名称：显示的文本或标识
- 描述信息：详细描述（如果有）
- 状态信息：enabled, visible等状态
- 位置信息：屏幕坐标和大小

示例输出：
---------
├─ [1] frame: Yonyou NC - 192.168.1.1980
│  位置: (100,50) 大小: 1200x800
┌─ 标准子元素 (3 个)
  ├─ [2] panel: 主面板
  │  状态: enabled,showing
  │  位置: (0,0) 大小: 1200x800
  ┌─ 增强子组件 (15 个)
    ├─ [3] button: 登录
    │  位置: (500,400) 大小: 80x30

故障排除：
---------
如果遇到问题：

1. "没有发现运行中的Java应用程序"
   - 确保NC应用程序正在运行
   - 确保Java Access Bridge已启用
   - 尝试重启NC应用程序

2. "Access Bridge初始化失败"
   - 检查WindowsAccessBridgeInterop.dll是否存在
   - 确保.NET Framework 4.0或更高版本已安装
   - 以管理员权限运行

3. "遍历元素时出错"
   - 这是正常现象，某些元素可能无法访问
   - 工具会继续遍历其他元素
   - 检查日志文件中的详细错误信息

4. "未找到匹配的应用程序"
   - 检查应用程序名称是否正确
   - 尝试不指定参数遍历所有应用
   - 查看日志中的JVM列表

性能说明：
---------
- 遍历时间取决于NC系统的复杂度
- 通常需要几秒到几分钟
- 大型NC系统可能需要更长时间
- 工具会显示实时进度信息

与其他工具对比：
--------------
相比Google AccessBridgeExplorer：
✅ 发现更多元素
✅ 使用多种遍历策略
✅ 生成详细日志文件
✅ 专门优化NC系统
✅ 命令行界面便于自动化

相比JavaFerret/JavaMonkey：
✅ 更详细的输出格式
✅ 专门针对NC系统优化
✅ 支持参数化运行
✅ 生成结构化日志

技术支持：
---------
如需技术支持或报告问题，请提供：
1. NC系统版本信息
2. 生成的日志文件
3. 错误信息截图
4. 系统环境信息

版本信息：
---------
工具版本：1.0
编译时间：2025/07/14 15:54
编译器：Microsoft Visual C# Compiler 4.8.9037.0
目标框架：.NET Framework 4.0
文件大小：11,264 字节

===============================================
祝您使用愉快！
===============================================
