﻿using System;
using System.Linq;
using WindowsAccessBridgeInterop;

/// <summary>
/// Java Access Bridge 快速测试工具
/// 验证Access Bridge是否正常工作，以及Java应用程序是否可访问
/// </summary>
class JavaAccessBridgeQuickTest {
    static void Main(string[] args) {
        Console.WriteLine("=== Java Access Bridge 快速测试 ===");
        Console.WriteLine();
        
        AccessBridge accessBridge = null;
        
        try {
            Console.WriteLine("1. 正在初始化 Access Bridge...");
            accessBridge = new AccessBridge();
            Console.WriteLine("   ✓ Access Bridge 初始化成功");
            
            Console.WriteLine();
            Console.WriteLine("2. 检查 Access Bridge 版本信息...");
            if (accessBridge.IsLoaded) {
                var version = accessBridge.LibraryVersion;
                Console.WriteLine($"   ✓ Library 版本: {version.FileVersion}");
                Console.WriteLine($"   ✓ Library 路径: {version.FileName}");
            } else {
                Console.WriteLine("   ✗ Access Bridge 库未加载");
                return;
            }
            
            Console.WriteLine();
            Console.WriteLine("3. 扫描运行中的Java应用程序...");
            var jvms = accessBridge.EnumJvms();
            Console.WriteLine($"   发现 {jvms.Count} 个Java虚拟机");
            
            if (jvms.Count == 0) {
                Console.WriteLine();
                Console.WriteLine("❌ 没有发现运行中的Java应用程序");
                Console.WriteLine();
                Console.WriteLine("可能的原因:");
                Console.WriteLine("  • 没有Java应用程序在运行");
                Console.WriteLine("  • Java应用程序没有启用可访问性支持");
                Console.WriteLine("  • Access Bridge 未正确安装或配置");
                Console.WriteLine();
                Console.WriteLine("解决建议:");
                Console.WriteLine("  1. 启动一个Java应用程序 (如用友NC、Eclipse等)");
                Console.WriteLine("  2. 确保运行 install-access-bridge-complete.bat");
                Console.WriteLine("  3. 检查 Java 系统属性是否包含 accessibility 支持");
                return;
            }
            
            Console.WriteLine();
            Console.WriteLine("4. 分析每个Java应用程序...");
            
            for (int i = 0; i < jvms.Count; i++) {
                var jvm = jvms[i];
                Console.WriteLine($"\n   JVM {i + 1}: {jvm.GetTitle()}");
                Console.WriteLine($"     • JVM ID: {jvm.JvmId}");
                Console.WriteLine($"     • 窗口数量: {jvm.Windows.Count}");
                
                // 检查版本信息
                try {
                    AccessBridgeVersionInfo versionInfo;
                    if (accessBridge.Functions.GetVersionInfo(jvm.JvmId, out versionInfo)) {
                        Console.WriteLine($"     • JVM版本: {versionInfo.VMversion}");
                        Console.WriteLine($"     • AccessBridge版本: {versionInfo.bridgeJavaClassVersion}");
                    }
                } catch (Exception ex) {
                    Console.WriteLine($"     • 版本信息获取失败: {ex.Message}");
                }
                
                // 检查每个窗口
                for (int j = 0; j < Math.Min(jvm.Windows.Count, 3); j++) { // 限制检查前3个窗口
                    var window = jvm.Windows[j];
                    Console.WriteLine($"       窗口 {j + 1}: {window.GetTitle()}");
                    Console.WriteLine($"         - 句柄: {window.Hwnd}");
                    
                    try {
                        var info = window.GetInfo();
                        Console.WriteLine($"         - 子元素数量: {info.childrenCount}");
                        Console.WriteLine($"         - 角色: {info.role ?? "无"}");
                        Console.WriteLine($"         - 状态: {info.states_en_US ?? "无"}");
                        
                        // 尝试获取第一个子元素
                        if (info.childrenCount > 0) {
                            try {
                                var firstChild = window.GetChildren().FirstOrDefault();
                                if (firstChild != null) {
                                    Console.WriteLine("         ✓ 能够访问子元素");
                                } else {
                                    Console.WriteLine("         ⚠ 子元素计数>0但无法获取");
                                }
                            } catch (Exception ex) {
                                Console.WriteLine($"         ✗ 获取子元素失败: {ex.Message}");
                            }
                        }
                        
                    } catch (Exception ex) {
                        Console.WriteLine($"         ✗ 获取窗口信息失败: {ex.Message}");
                    }
                }
                
                if (jvm.Windows.Count > 3) {
                    Console.WriteLine($"       ... 还有 {jvm.Windows.Count - 3} 个窗口未显示");
                }
            }
            
            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine();
            Console.WriteLine("✓ Java Access Bridge 工作正常");
            Console.WriteLine($"✓ 发现 {jvms.Count} 个可访问的Java应用程序");
            Console.WriteLine($"✓ 总共 {jvms.Sum(j => j.Windows.Count)} 个Java窗口");
            
            Console.WriteLine();
            Console.WriteLine("下一步建议:");
            Console.WriteLine("  1. 运行 JavaElementDumper.exe 进行基础遍历");
            Console.WriteLine("  2. 运行 JavaElementDumperEnhanced.exe 进行增强遍历");
            Console.WriteLine("  3. 比较两种方法的结果差异");
            
        } catch (Exception ex) {
            Console.WriteLine();
            Console.WriteLine($"❌ 测试失败: {ex.Message}");
            Console.WriteLine($"   堆栈跟踪: {ex.StackTrace}");
            Console.WriteLine();
            Console.WriteLine("可能的问题:");
            Console.WriteLine("  • Windows Access Bridge DLL 未找到");
            Console.WriteLine("  • 系统架构不匹配 (32位/64位)");
            Console.WriteLine("  • Access Bridge 未正确安装");
            Console.WriteLine();
            Console.WriteLine("解决方案:");
            Console.WriteLine("  1. 运行 install-access-bridge-complete.bat");
            Console.WriteLine("  2. 重启计算机");
            Console.WriteLine("  3. 确保Java和系统架构匹配");
        } finally {
            accessBridge?.Dispose();
        }
        
        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
} 