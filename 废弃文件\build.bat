@echo off
echo 正在编译Access Bridge Explorer增强版...

REM 设置MSBuild路径
set MSBUILD_PATH=C:\Windows\Microsoft.NET\Framework64\v4.0.30319\MSBuild.exe

REM 检查MSBuild是否存在
if not exist "%MSBUILD_PATH%" (
    echo 错误: 找不到MSBuild.exe，请确认.NET Framework 4.0已安装
    pause
    exit /b 1
)

REM 编译解决方案
echo 开始编译...
"%MSBUILD_PATH%" AccessBridgeExplorer.sln /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:normal

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo 可执行文件位置: bin\Debug\AccessBridgeExplorer.exe
echo.
echo 按任意键继续...
pause > nul 