using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace TestEnhancedDiscovery
{
    public partial class TestForm : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeView;
        private TextBox logTextBox;
        private Button refreshButton;
        private Button testButton;

        public TestForm()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        private void InitializeComponent()
        {
            this.Text = "增强发现测试工具";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;

            var splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 400;

            // 树视图
            treeView = new TreeView();
            treeView.Dock = DockStyle.Fill;
            treeView.AfterSelect += TreeView_AfterSelect;

            // 日志文本框
            logTextBox = new TextBox();
            logTextBox.Dock = DockStyle.Fill;
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.ReadOnly = true;
            logTextBox.Font = new Font("Consolas", 9);

            // 按钮面板
            var buttonPanel = new Panel();
            buttonPanel.Dock = DockStyle.Top;
            buttonPanel.Height = 40;

            refreshButton = new Button();
            refreshButton.Text = "刷新";
            refreshButton.Location = new Point(10, 8);
            refreshButton.Size = new Size(80, 25);
            refreshButton.Click += RefreshButton_Click;

            testButton = new Button();
            testButton.Text = "测试增强";
            testButton.Location = new Point(100, 8);
            testButton.Size = new Size(80, 25);
            testButton.Click += TestButton_Click;

            buttonPanel.Controls.Add(refreshButton);
            buttonPanel.Controls.Add(testButton);

            splitContainer.Panel1.Controls.Add(treeView);
            splitContainer.Panel2.Controls.Add(logTextBox);

            this.Controls.Add(splitContainer);
            this.Controls.Add(buttonPanel);
        }

        private void InitializeAccessBridge()
        {
            try
            {
                accessBridge = new AccessBridge();
                LogMessage("Access Bridge 初始化成功");
                RefreshJavaApplications();
            }
            catch (Exception ex)
            {
                LogMessage("Access Bridge 初始化失败: " + ex.Message);
                MessageBox.Show("无法初始化 Access Bridge: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            RefreshJavaApplications();
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            if (treeView.SelectedNode != null && treeView.SelectedNode.Tag is AccessibleContextNode)
            {
                TestEnhancedMethods((AccessibleContextNode)treeView.SelectedNode.Tag);
            }
            else
            {
                LogMessage("请先选择一个Java组件进行测试");
            }
        }

        private void RefreshJavaApplications()
        {
            try
            {
                treeView.Nodes.Clear();
                LogMessage("开始搜索Java应用程序...");

                // 使用与原来工具相同的方法：直接枚举Windows窗口
                var javaWindows = FindJavaWindows();
                LogMessage("发现 " + javaWindows.Count + " 个Java窗口");

                foreach (var hWnd in javaWindows)
                {
                    CreateJavaWindowNode(hWnd);
                }

                LogMessage("Java应用程序搜索完成");
            }
            catch (Exception ex)
            {
                LogMessage("搜索Java应用程序时出错: " + ex.Message);
            }
        }

        private List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();

            // 枚举所有顶级窗口
            EnumWindows(delegate(IntPtr hWnd, IntPtr lParam)
            {
                if (IsWindowVisible(hWnd) && accessBridge.Functions.IsJavaWindow(hWnd))
                {
                    javaWindows.Add(hWnd);
                }
                return true;
            }, IntPtr.Zero);

            return javaWindows;
        }

        private void CreateJavaWindowNode(IntPtr hWnd)
        {
            try
            {
                // 获取窗口标题
                var windowTitle = GetWindowTitle(hWnd);

                // 获取AccessibleContext
                int vmId;
                JavaObjectHandle ac;
                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out vmId, out ac))
                {
                    var rootNode = new TreeNode("Java窗口: " + windowTitle + " (HWND: " + hWnd + ")");

                    // 创建AccessibleWindow对象
                    var accessibleWindow = accessBridge.CreateAccessibleWindow(hWnd);
                    if (accessibleWindow != null)
                    {
                        rootNode.Tag = accessibleWindow;

                        // 使用增强的子组件获取方法
                        AddChildNodesEnhanced(rootNode, accessibleWindow, 0, 2);

                        treeView.Nodes.Add(rootNode);
                        rootNode.Expand();
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("错误: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                treeView.Nodes.Add(errorNode);
            }
        }

        private string GetWindowTitle(IntPtr hWnd)
        {
            var sb = new StringBuilder(256);
            GetWindowText(hWnd, sb, sb.Capacity);
            return sb.ToString();
        }

        private void AddChildNodesEnhanced(TreeNode parentNode, AccessibleNode accessibleNode, int currentDepth, int maxDepth)
        {
            if (currentDepth >= maxDepth) return;

            try
            {
                IEnumerable<AccessibleNode> children;

                // 如果是AccessibleContextNode，使用增强方法
                if (accessibleNode is AccessibleContextNode)
                {
                    var contextNode = (AccessibleContextNode)accessibleNode;
                    LogMessage("使用增强方法获取子组件: " + (contextNode.GetInfo().name ?? "未命名"));
                    children = contextNode.GetChildrenEnhanced();
                }
                else
                {
                    children = accessibleNode.GetChildren();
                }

                var childList = children.ToList();
                LogMessage("找到 " + childList.Count + " 个子组件");

                foreach (var child in childList.Take(10)) // 限制显示数量
                {
                    try
                    {
                        if (child is AccessibleContextNode)
                        {
                            var contextChild = (AccessibleContextNode)child;
                            var childInfo = contextChild.GetInfo();
                            var childNode = new TreeNode((childInfo.name ?? "未命名") + " [" + (childInfo.role ?? "未知") + "]");
                            childNode.Tag = child;

                            parentNode.Nodes.Add(childNode);

                            // 递归添加子节点
                            if (childInfo.childrenCount > 0)
                            {
                                AddChildNodesEnhanced(childNode, child, currentDepth + 1, maxDepth);
                            }
                        }
                        else
                        {
                            var childNode = new TreeNode("未知类型节点");
                            childNode.Tag = child;
                            parentNode.Nodes.Add(childNode);
                        }
                    }
                    catch (Exception ex)
                    {
                        var errorNode = new TreeNode("子组件错误: " + ex.Message);
                        errorNode.ForeColor = Color.Red;
                        parentNode.Nodes.Add(errorNode);
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode("获取子组件失败: " + ex.Message);
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        private void TreeView_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node.Tag is AccessibleContextNode)
            {
                var contextNode = (AccessibleContextNode)e.Node.Tag;
                var info = contextNode.GetInfo();
                LogMessage("选中组件: " + (info.name ?? "未命名") + " [" + (info.role ?? "未知") + "]");
                LogMessage("  子组件数: " + info.childrenCount);
                LogMessage("  位置: (" + info.x + ", " + info.y + ")");
                LogMessage("  大小: " + info.width + " x " + info.height);
            }
        }

        private void TestEnhancedMethods(AccessibleContextNode contextNode)
        {
            try
            {
                LogMessage("=== 开始增强方法测试 ===");
                var info = contextNode.GetInfo();
                LogMessage("测试组件: " + (info.name ?? "未命名"));

                // 测试标准方法
                try
                {
                    var standardChildren = contextNode.GetChildren().ToList();
                    LogMessage("标准方法找到: " + standardChildren.Count + " 个子组件");
                }
                catch (Exception ex)
                {
                    LogMessage("标准方法失败: " + ex.Message);
                }

                // 测试增强方法
                try
                {
                    var enhancedChildren = contextNode.GetChildrenEnhanced().ToList();
                    LogMessage("增强方法找到: " + enhancedChildren.Count + " 个子组件");
                }
                catch (Exception ex)
                {
                    LogMessage("增强方法失败: " + ex.Message);
                }

                // 测试NC系统检测
                if (contextNode.IsNCSystemComponent())
                {
                    LogMessage("✓ 检测到用友NC系统组件");
                }
                else
                {
                    LogMessage("○ 非用友NC系统组件");
                }

                // 获取诊断信息
                var diagnosticInfo = contextNode.GetDiagnosticInfo();
                LogMessage("诊断信息:");
                LogMessage(diagnosticInfo);

                LogMessage("=== 测试完成 ===");
            }
            catch (Exception ex)
            {
                LogMessage("测试过程中出错: " + ex.Message);
            }
        }

        private void LogMessage(string message)
        {
            if (logTextBox.InvokeRequired)
            {
                logTextBox.Invoke(new Action<string>(LogMessage), message);
                return;
            }

            logTextBox.AppendText("[" + DateTime.Now.ToString("HH:mm:ss") + "] " + message + "\r\n");
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            if (accessBridge != null)
                accessBridge.Dispose();
            base.OnFormClosed(e);
        }

        #region Windows API声明

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        #endregion
    }

    static class Program
    {
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestForm());
        }
    }
}
