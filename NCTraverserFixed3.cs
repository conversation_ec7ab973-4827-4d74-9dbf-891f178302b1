using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using WindowsAccessBridgeInterop;

namespace NCTraverser
{
    /// <summary>
    /// NC遍历器 - 使用修复的WindowsAccessBridgeInterop库测试标准API
    /// 这个版本使用修复后的库，支持标准的initializeAccessBridge()和shutdownAccessBridge()调用
    /// </summary>
    class NCTraverserFixed3
    {
        private static AccessBridge accessBridge;
        private static string logFile;
        private static StreamWriter logWriter;

        static void Main(string[] args)
        {
            // 初始化日志文件
            logFile = Path.Combine(Environment.CurrentDirectory, "NCTraverserFixed3_Log.txt");
            InitializeLogging();

            try
            {
                WriteLog("=== NC遍历器启动 (使用修复的WindowsAccessBridgeInterop库) ===");
                WriteLog($"时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                WriteLog($"版本: Fixed3 - 标准API版本");
                WriteLog($"日志文件: {logFile}");
                WriteLog("");

                // 系统环境检查
                PerformSystemCheck();

                // 初始化Access Bridge
                if (!InitializeAccessBridge())
                {
                    WriteLog("❌ Access Bridge初始化失败");
                    return;
                }

                WriteLog("✅ Access Bridge初始化成功");

                // 发现Java应用程序
                DiscoverJavaApplications();

                WriteLog("\n=== 程序执行完毕 ===");
            }
            catch (Exception ex)
            {
                WriteLog($"❌ 程序异常: {ex.Message}");
                WriteLog($"调用栈: {ex.StackTrace}");
            }
            finally
            {
                try
                {
                    // 清理资源
                    if (accessBridge != null)
                    {
                        WriteLog("正在关闭Access Bridge...");
                        accessBridge.Dispose();
                        WriteLog("✅ Access Bridge已关闭");
                    }
                }
                catch (Exception ex)
                {
                    WriteLog($"⚠️ 关闭Access Bridge时出现错误: {ex.Message}");
                }

                // 关闭日志
                if (logWriter != null)
                {
                    logWriter.Close();
                    logWriter.Dispose();
                }

                Console.WriteLine("\n按任意键退出...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// 初始化日志记录
        /// </summary>
        static void InitializeLogging()
        {
            try
            {
                logWriter = new StreamWriter(logFile, false, Encoding.UTF8);
                logWriter.AutoFlush = true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"无法创建日志文件: {ex.Message}");
                // 继续执行，只输出到控制台
            }
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        static void WriteLog(string message)
        {
            string logMessage = $"[{DateTime.Now:HH:mm:ss.fff}] {message}";
            Console.WriteLine(logMessage);

            try
            {
                logWriter?.WriteLine(logMessage);
            }
            catch (Exception)
            {
                // 忽略日志写入错误
            }
        }

        /// <summary>
        /// 执行系统环境检查
        /// </summary>
        static void PerformSystemCheck()
        {
            WriteLog("=== 系统环境检查 ===");

            // 操作系统信息
            WriteLog($"操作系统: {Environment.OSVersion}");
            WriteLog($"处理器架构: {Environment.ProcessorCount} cores, {(Environment.Is64BitOperatingSystem ? "64-bit" : "32-bit")} OS");
            WriteLog($"进程架构: {(Environment.Is64BitProcess ? "64-bit" : "32-bit")} process");

            // .NET Framework版本
            WriteLog($".NET Framework: {Environment.Version}");

            // Access Bridge DLL检查
            CheckAccessBridgeDLLs();

            WriteLog("");
        }

        /// <summary>
        /// 检查Access Bridge DLL文件
        /// </summary>
        static void CheckAccessBridgeDLLs()
        {
            WriteLog("--- Access Bridge DLL检查 ---");

            string[] requiredDlls = Environment.Is64BitProcess 
                ? new[] { "WindowsAccessBridge-64.dll", "WindowsAccessBridge.dll" }
                : new[] { "WindowsAccessBridge-32.dll", "WindowsAccessBridge.dll" };

            foreach (string dll in requiredDlls)
            {
                if (File.Exists(dll))
                {
                    var info = FileVersionInfo.GetVersionInfo(dll);
                    WriteLog($"✅ 找到 {dll} - 版本: {info.FileVersion}");
                }
                else if (File.Exists(Path.Combine("accessbridge2_0_2", dll)))
                {
                    var info = FileVersionInfo.GetVersionInfo(Path.Combine("accessbridge2_0_2", dll));
                    WriteLog($"✅ 找到 accessbridge2_0_2\\{dll} - 版本: {info.FileVersion}");
                }
                else
                {
                    WriteLog($"⚠️ 未找到 {dll}");
                }
            }
        }

        /// <summary>
        /// 初始化Access Bridge
        /// </summary>
        static bool InitializeAccessBridge()
        {
            WriteLog("=== 初始化Access Bridge ===");

            try
            {
                WriteLog("正在创建AccessBridge实例...");
                accessBridge = new AccessBridge();

                WriteLog("正在初始化Access Bridge...");
                accessBridge.Initialize();

                WriteLog($"Access Bridge版本: {accessBridge.LibraryVersion?.FileVersion ?? "未知"}");
                WriteLog($"是否为Legacy版本: {accessBridge.IsLegacy}");

                return true;
            }
            catch (Exception ex)
            {
                WriteLog($"❌ Access Bridge初始化失败: {ex.Message}");
                WriteLog($"详细错误: {ex}");
                return false;
            }
        }

        /// <summary>
        /// 发现Java应用程序
        /// </summary>
        static void DiscoverJavaApplications()
        {
            WriteLog("=== 发现Java应用程序 ===");

            try
            {
                WriteLog("正在枚举Java虚拟机...");
                var jvms = accessBridge.EnumJvms();

                WriteLog($"发现 {jvms.Count} 个Java虚拟机");

                if (jvms.Count == 0)
                {
                    WriteLog("❌ 没有发现运行中的Java应用程序");
                    WriteLog("请确保:");
                    WriteLog("1. 已启动Java应用程序（如用友NC）");
                    WriteLog("2. Java Access Bridge已正确安装");
                    WriteLog("3. 当前进程架构与Java进程架构匹配");
                    return;
                }

                // 遍历所有JVM
                foreach (var jvm in jvms)
                {
                    WriteLog($"\n--- JVM {jvm.JvmId} ---");
                    WriteLog($"窗口数量: {jvm.Windows.Count}");

                    foreach (var window in jvm.Windows)
                    {
                        WriteLog($"  窗口: {window.GetTitle()} (HWND: {window.Hwnd})");
                        
                        // 检查是否为NC系统窗口
                        string title = window.GetTitle().ToLower();
                        if (title.Contains("nc") || title.Contains("用友") || title.Contains("ufida"))
                        {
                            WriteLog($"  🎯 发现NC系统窗口！正在分析...");
                            AnalyzeNCWindow(window);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog($"❌ 发现Java应用程序时出错: {ex.Message}");
                WriteLog($"详细错误: {ex}");
            }
        }

        /// <summary>
        /// 分析NC窗口
        /// </summary>
        static void AnalyzeNCWindow(WindowsAccessBridgeInterop.AccessibleWindow window)
        {
            try
            {
                WriteLog($"=== NC窗口分析: {window.GetTitle()} ===");

                // 获取窗口的根上下文信息
                var rootNode = window;
                WriteLog($"根节点信息:");
                WriteLog($"  标题: {rootNode.GetTitle()}");
                WriteLog($"  子节点数量: {rootNode.GetChildren().Count()}");

                int childIndex = 0;
                foreach (var child in rootNode.GetChildren().Take(10)) // 限制前10个子节点
                {
                    WriteLog($"  子节点[{childIndex}]: {child.GetTitle()}");
                    
                    // 检查子节点的子节点
                    int grandChildCount = child.GetChildren().Count();
                    WriteLog($"    孙节点数量: {grandChildCount}");
                    
                    if (grandChildCount > 0)
                    {
                        int grandChildIndex = 0;
                        foreach (var grandChild in child.GetChildren().Take(5)) // 限制前5个孙节点
                        {
                            WriteLog($"      孙节点[{grandChildIndex}]: {grandChild.GetTitle()}");
                            grandChildIndex++;
                        }
                    }
                    
                    childIndex++;
                }

                WriteLog("✅ NC窗口分析完成");
            }
            catch (Exception ex)
            {
                WriteLog($"❌ 分析NC窗口时出错: {ex.Message}");
            }
        }
    }
} 