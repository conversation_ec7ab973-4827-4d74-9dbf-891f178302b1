/*
 * Copyright 2005 Sun Microsystems, Inc. All rights reserved.
 * SUN PROPRIETARY/CONFIDENTIAL. Use is subject to license terms.
 */

/*
 * @(#)FerretResource.h	1.18 05/03/21
 */

#define cFerretText                     1001
#define cFerretMenus                    10000

#define cFileMenu                       10100
#define cAccessBridgeDLLLoaded          10101
#define cExitMenuItem                   10102

#define cJavaEventsMenu                 10200
#define cTrackMouseMenuItem             10201
#define cTrackFocusMenuItem             10203
#define cTrackCaretMenuItem             10204
#define cTrackMenuSelectedMenuItem      10205
#define cTrackMenuDeselectedMenuItem    10206
#define cTrackMenuCanceledItem          10207
#define cTrackPopupBecomeVisibleMenuItem 10208
#define cTrackPopupBecomeInvisibleMenuItem 10209
#define cTrackPopupCanceledItem         10210

#define cUpdateSettingsMenu             10300
#define cUpdateWithF1Item               10301
#define cUpdateWithF2Item               10302
#define cUpdateFromMouseMenuItem        10304

#define cAccessibilityEventsMenu            10400
#define cTrackPropertyNameItem              10401
#define cTrackPropertyDescriptionItem       10402
#define cTrackPropertyStateItem             10403
#define cTrackPropertyValueItem             10404
#define cTrackPropertySelectionItem         10405
#define cTrackPropertyTextItem              10406
#define cTrackPropertyCaretItem             10407
#define cTrackPropertyVisibleDataItem       10408
#define cTrackPropertyChildItem             10409
#define cTrackPropertyActiveDescendentItem  10410
#define cTrackPropertyTableModelChangeItem  10411

#define cTrackShutdownMenuItem              10412


// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NO_MFC                     1
#define _APS_NEXT_RESOURCE_VALUE        102
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1029
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif
