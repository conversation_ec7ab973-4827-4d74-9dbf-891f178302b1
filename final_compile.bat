@echo off
echo ==============================
echo Final Compilation Attempt
echo ==============================

echo Setting up .NET Framework environment...

:: Try different .NET Framework versions
set DOTNET_VERSIONS=v4.0.30319 v3.5 v2.0.50727

for %%v in (%DOTNET_VERSIONS%) do (
    set CSC_PATH=C:\Windows\Microsoft.NET\Framework64\%%v\csc.exe
    if exist "!CSC_PATH!" (
        echo Found C# compiler at: !CSC_PATH!
        goto :compile
    )
    set CSC_PATH=C:\Windows\Microsoft.NET\Framework\%%v\csc.exe
    if exist "!CSC_PATH!" (
        echo Found C# compiler at: !CSC_PATH!
        goto :compile
    )
)

echo ERROR: No C# compiler found
echo Please install .NET Framework or Visual Studio
pause
exit /b 1

:compile
echo.
echo Using compiler: %CSC_PATH%

echo Checking dependencies...
if not exist "bin\Debug\WindowsAccessBridgeInterop.dll" (
    echo ERROR: WindowsAccessBridgeInterop.dll not found
    echo Please make sure the file exists in bin\Debug\
    pause
    exit /b 1
)

echo.
echo Compiling tools...

echo [1/3] JavaAccessBridgeQuickTest...
"%CSC_PATH%" /target:exe /out:JavaAccessBridgeQuickTest.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll JavaAccessBridgeQuickTest.cs
if %ERRORLEVEL% neq 0 (
    echo Compilation failed for JavaAccessBridgeQuickTest
    pause
    exit /b 1
)

echo [2/3] JavaElementDumper...
"%CSC_PATH%" /target:exe /out:JavaElementDumper.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll JavaElementDumper.cs
if %ERRORLEVEL% neq 0 (
    echo Compilation failed for JavaElementDumper
    pause
    exit /b 1
)

echo [3/3] JavaElementDumperEnhanced...
"%CSC_PATH%" /target:exe /out:JavaElementDumperEnhanced.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll /reference:System.Drawing.dll JavaElementDumperEnhanced.cs
if %ERRORLEVEL% neq 0 (
    echo Compilation failed for JavaElementDumperEnhanced
    pause
    exit /b 1
)

echo.
echo ==============================
echo SUCCESS! All tools compiled
echo ==============================

echo.
echo Generated files:
dir *.exe /b 2>nul

echo.
echo Next steps:
echo 1. Start your NC 5.7 application
echo 2. Run JavaAccessBridgeQuickTest.exe to verify setup
echo 3. Run JavaElementDumperEnhanced.exe for comprehensive traversal
echo 4. Compare results with JavaFerret/JavaMonkey
echo.
pause
