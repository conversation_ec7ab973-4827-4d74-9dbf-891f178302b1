Write-Host "=== 编译NC简化版遍历工具 ===" -ForegroundColor Green

$cscPath = "C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe"
$outputFile = "NCTraverserSimple.exe"
$sourceFile = "NCTraverserSimple.cs"
$bridgeDll = "src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll"

Write-Host "检查编译器..." -ForegroundColor Yellow
if (-not (Test-Path $cscPath)) {
    Write-Host "错误: 未找到CSC编译器: $cscPath" -ForegroundColor Red
    exit 1
}

Write-Host "检查依赖DLL..." -ForegroundColor Yellow
if (-not (Test-Path $bridgeDll)) {
    Write-Host "错误: 未找到WindowsAccessBridgeInterop.dll: $bridgeDll" -ForegroundColor Red
    exit 1
}

Write-Host "检查源文件..." -ForegroundColor Yellow
if (-not (Test-Path $sourceFile)) {
    Write-Host "错误: 未找到源文件: $sourceFile" -ForegroundColor Red
    exit 1
}

Write-Host "清理旧文件..." -ForegroundColor Yellow
if (Test-Path $outputFile) {
    Remove-Item $outputFile
}

Write-Host "开始编译..." -ForegroundColor Yellow
$arguments = @(
    "/target:exe",
    "/out:$outputFile",
    "/reference:$bridgeDll",
    "/reference:System.dll",
    "/reference:System.Core.dll", 
    "/reference:System.Drawing.dll",
    $sourceFile
)

Write-Host "执行命令: $cscPath $($arguments -join ' ')" -ForegroundColor Cyan

try {
    & $cscPath $arguments
    
    if (Test-Path $outputFile) {
        Write-Host "✅ 编译成功!" -ForegroundColor Green
        $fileInfo = Get-Item $outputFile
        Write-Host "生成文件: $($fileInfo.Name) (大小: $($fileInfo.Length) bytes)" -ForegroundColor Green
        Write-Host "运行命令: .\$outputFile" -ForegroundColor Cyan
    }
    else {
        Write-Host "❌ 编译失败: 未生成输出文件" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ 编译过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "完成" 