@echo off
echo ==============================
echo Testing NC Access with JavaFerret
echo ==============================

echo.
echo Please make sure your NC 5.7 application is running before proceeding.
echo.
pause

echo.
echo Starting JavaFerret to test NC access...
echo.
echo Instructions:
echo 1. JavaFerret window will open
echo 2. Look for your NC application in the tree
echo 3. Expand the nodes to see all accessible elements
echo 4. Compare with what you see in other tools
echo.

if exist "accessbridge2_0_2\JavaFerret.exe" (
    echo Running JavaFerret...
    start "" "accessbridge2_0_2\JavaFerret.exe"
    echo.
    echo JavaFerret has been started.
    echo You can now explore your NC application elements.
) else (
    echo ERROR: JavaFerret.exe not found in accessbridge2_0_2 directory
    echo Please check if the file exists.
)

echo.
echo You can also try:
if exist "accessbridge2_0_2\JavaMonkey.exe" (
    echo - JavaMonkey.exe for event monitoring
)

echo.
echo Press any key to exit...
pause >nul
