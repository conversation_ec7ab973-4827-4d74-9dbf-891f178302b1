# Java程序发现工具使用说明

## 📋 工具概述

这是一个基于**Java Access Bridge API**开发的专用Java程序发现和遍历工具，能够自动发现系统中运行的Java应用程序，并以树形结构展示其组件层次结构。

## 🎯 主要功能

### 1. 自动发现Java程序
- 扫描系统中所有可见的Java应用程序窗口
- 使用`IsJavaWindow` API检测Java窗口
- 显示窗口标题和句柄信息

### 2. 组件层次遍历
- 递归遍历Java应用程序的组件树
- 使用`GetAccessibleChildFromContext` API获取子组件
- 限制遍历深度避免性能问题

### 3. 详细属性显示
- 显示每个组件的完整属性信息
- 包括名称、角色、状态、位置、大小等
- 支持的接口类型(组件、动作、选择、文本)

## 🚀 使用步骤

### 1. 环境准备
```bash
# 确保Java Access Bridge已安装和配置
# 检查accessibility.properties配置
# 启动目标Java应用程序(如用友NC)
```

### 2. 编译工具
```bash
# 运行编译脚本
.\build_java_discovery_tool.bat
```

### 3. 运行工具
```bash
# 启动发现工具
.\JavaDiscoveryTool.exe
```

### 4. 操作界面
1. **刷新发现** - 扫描当前运行的Java程序
2. **展开所有** - 展开所有组件节点
3. **折叠所有** - 折叠所有组件节点
4. **选择节点** - 查看详细属性信息

## 🔧 技术实现

### 核心API调用
```csharp
// 1. 初始化Access Bridge
accessBridge = new AccessBridge();

// 2. 检测Java窗口
bool isJava = accessBridge.Functions.IsJavaWindow(hWnd);

// 3. 获取AccessibleContext
accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out vmId, out ac);

// 4. 获取组件信息
accessBridge.Functions.GetAccessibleContextInfo(vmId, ac, out contextInfo);

// 5. 遍历子组件
var childAc = accessBridge.Functions.GetAccessibleChildFromContext(vmId, ac, index);
```

### 数据结构
- **JavaWindowInfo** - Java窗口信息(句柄、VM ID、AccessibleContext)
- **JavaElementInfo** - Java组件信息(VM ID、AccessibleContext、详细信息)
- **AccessibleContextInfo** - 组件详细属性信息

## 📊 界面布局

```
┌─────────────────────────────────────────────────────────┐
│ Java程序发现和遍历工具                                    │
├─────────────────────────────────────────────────────────┤
│ [🔄 刷新发现] [📂 展开所有] [📁 折叠所有]                  │
├─────────────────────────────────────────────────────────┤
│ 🖥️ Java应用程序树                                       │
│ ├─ 🖥️ 用友NC (HWND: 0x12345)                           │
│ │  ├─ 📄 主面板 [panel]                                 │
│ │  │  ├─ 📄 菜单栏 [menubar]                           │
│ │  │  └─ 📄 工具栏 [toolbar]                           │
│ │  └─ 📄 状态栏 [statusbar]                             │
├─────────────────────────────────────────────────────────┤
│ 详细属性信息                                             │
│ ┌─────────┬─────────────────┬──────────┐                │
│ │ 属性    │ 值              │ 类型     │                │
│ ├─────────┼─────────────────┼──────────┤                │
│ │ 名称    │ 主面板          │ String   │                │
│ │ 角色    │ panel           │ Role     │                │
│ │ 状态    │ enabled,visible │ States   │                │
│ └─────────┴─────────────────┴──────────┘                │
├─────────────────────────────────────────────────────────┤
│ 状态: 发现 2 个Java应用程序                              │
└─────────────────────────────────────────────────────────┘
```

## ⚠️ 注意事项

### 1. 环境要求
- Windows操作系统
- .NET Framework 4.7.2或更高版本
- Java Access Bridge 2.0.2已安装
- 目标Java应用程序支持可访问性

### 2. 性能优化
- 限制遍历深度为3层
- 每层最多显示20个子组件
- 延迟加载避免界面卡顿

### 3. 常见问题
- **未发现Java程序**: 检查accessibility.properties配置
- **组件显示不完整**: 确保Java应用支持可访问性
- **程序崩溃**: 检查Access Bridge DLL版本匹配

## 🔍 调试信息

工具会在状态栏显示当前操作状态：
- "就绪" - 工具已启动，等待操作
- "正在扫描..." - 正在发现Java程序
- "发现 X 个Java应用程序" - 扫描完成
- "未发现Java应用程序" - 没有找到Java窗口

## 📝 开发说明

基于官方Java Access Bridge API文档实现：
- 使用`AccessBridgeCalls.h`中定义的API函数
- 遵循`AccessBridgePackages.h`中的数据结构
- 实现了核心的发现和遍历功能
- 提供友好的图形界面

这个工具专门用于Java程序的发现和分析，是调试Java应用程序可访问性问题的有力工具。
