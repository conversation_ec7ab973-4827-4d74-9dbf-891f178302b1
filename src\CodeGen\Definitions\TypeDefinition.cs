﻿// Copyright 2016 Google Inc. All Rights Reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
//     http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using System.Collections.Generic;

namespace CodeGen.Definitions {
  /// <summary>
  /// Common abstraction of a <see cref="StructDefinition"/> and <see
  /// cref="ClassDefinition"/>.
  /// </summary>
  public abstract class TypeDefinition {
    private List<FieldDefinition> _fields = new List<FieldDefinition>();

    public string Name { get; set; }

    public List<FieldDefinition> Fields {
      get { return _fields; }
      set { _fields = value; }
    }
  }
}