@echo off
chcp 65001 >nul
echo ===============================================
echo NC专用命令行遍历工具 - 完整解决方案
echo ===============================================

echo.
echo 🔧 检查依赖文件...

REM 检查主程序
if not exist "NCTraverser.exe" (
    echo ❌ NCTraverser.exe 不存在，正在编译...
    if exist "compile_final.bat" (
        call compile_final.bat
    ) else (
        echo ❌ 编译脚本不存在，请手动编译
        pause
        exit /b 1
    )
)

REM 检查依赖DLL
if not exist "WindowsAccessBridgeInterop.dll" (
    echo 📋 复制依赖DLL文件...
    if exist "src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll" (
        copy "src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll" .
        echo ✅ DLL文件复制成功
    ) else (
        echo ❌ 找不到WindowsAccessBridgeInterop.dll
        echo 请先运行 build.bat 编译依赖项
        pause
        exit /b 1
    )
)

echo ✅ 依赖文件检查完成

echo.
echo 📋 使用说明：
echo 1. 请确保用友NC 5.7应用程序正在运行
echo 2. 确保Java Access Bridge已正确安装
echo 3. 选择遍历模式
echo.

echo 🚀 选择运行模式：
echo 1. 遍历所有Java应用程序
echo 2. 只遍历包含"NC"的应用程序  
echo 3. 只遍历包含"yonyou"的应用程序
echo 4. 自定义应用程序名称
echo 5. 查看帮助信息
echo 6. 退出
echo.

set /p choice="请选择 (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🎯 开始遍历所有Java应用程序...
    echo 💡 这将遍历系统中所有运行的Java应用程序
    echo.
    NCTraverser.exe
    goto :show_results
) else if "%choice%"=="2" (
    echo.
    echo 🎯 开始遍历包含"NC"的应用程序...
    echo 💡 专门针对用友NC系统
    echo.
    NCTraverser.exe NC
    goto :show_results
) else if "%choice%"=="3" (
    echo.
    echo 🎯 开始遍历包含"yonyou"的应用程序...
    echo 💡 专门针对用友系统
    echo.
    NCTraverser.exe yonyou
    goto :show_results
) else if "%choice%"=="4" (
    echo.
    set /p appname="请输入应用程序名称关键字: "
    echo.
    echo 🎯 开始遍历包含"!appname!"的应用程序...
    echo.
    NCTraverser.exe !appname!
    goto :show_results
) else if "%choice%"=="5" (
    goto :show_help
) else if "%choice%"=="6" (
    echo 👋 再见！
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    goto :main
)

:show_results
echo.
echo ===============================================
echo 📊 遍历结果
echo ===============================================

echo.
echo 🔍 检查生成的日志文件：
set found_log=0
for %%f in (NC_Elements_Complete_*.log) do (
    set found_log=1
    echo.
    echo ✅ 找到日志文件: %%f
    for %%s in ("%%f") do echo    文件大小: %%~zs 字节
    
    echo.
    echo 📋 日志文件前10行预览：
    echo ----------------------------------------
    type "%%f" | more /E +1 | head -10 2>nul || (
        powershell -Command "Get-Content '%%f' | Select-Object -First 10"
    )
    echo ----------------------------------------
    
    echo.
    echo 💡 完整查看日志文件的方法：
    echo    1. 用记事本打开: notepad "%%f"
    echo    2. 用命令行查看: type "%%f" ^| more
    echo    3. 用PowerShell查看: Get-Content "%%f" ^| more
)

if %found_log%==0 (
    echo ❌ 没有找到日志文件
    echo.
    echo 🔧 可能的原因：
    echo 1. 没有找到Java应用程序 - 请确保NC正在运行
    echo 2. Access Bridge未正确安装
    echo 3. 权限不足 - 尝试以管理员身份运行
    echo 4. 程序运行出错 - 检查错误信息
)

echo.
pause
goto :main

:show_help
echo.
echo ===============================================
echo 📖 帮助信息
echo ===============================================
echo.
echo 🎯 工具功能：
echo   专门为用友NC 5.7系统设计的Java元素遍历工具
echo   比Google AccessBridgeExplorer更全面
echo   可以发现更多的Java可访问元素
echo.
echo 📋 使用前准备：
echo   1. 启动用友NC 5.7应用程序
echo   2. 确保Java Access Bridge已安装
echo   3. 确保有足够的磁盘空间存储日志
echo.
echo 🚀 命令行用法：
echo   NCTraverser.exe              - 遍历所有Java应用
echo   NCTraverser.exe NC           - 只遍历NC应用
echo   NCTraverser.exe yonyou       - 只遍历用友应用
echo.
echo 📁 输出文件：
echo   NC_Elements_Complete_YYYYMMDD_HHMMSS.log
echo   包含完整的结构树信息，UTF-8编码
echo.
echo 🔧 故障排除：
echo   如果遇到问题，请检查：
echo   - NC应用程序是否正在运行
echo   - Java Access Bridge是否已安装
echo   - 是否有管理员权限
echo.
pause
goto :main

:main
goto :EOF
