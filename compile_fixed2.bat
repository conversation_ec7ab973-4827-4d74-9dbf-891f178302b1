@echo off
echo Compiling NCTraverserFixed2.cs...

C:\Windows\Microsoft.NET\Framework64\v4.0.30319\csc.exe /target:exe /out:NCTraverserFixed2.exe /reference:src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll /reference:System.dll /reference:System.Core.dll /reference:System.Drawing.dll NCTraverserFixed2.cs

if %ERRORLEVEL% equ 0 (
    echo.
    echo SUCCESS! NCTraverserFixed2.exe compiled successfully!
    echo.
    echo This version uses the same JVM enumeration method as Access Bridge Explorer
    echo.
    if exist NCTraverserFixed2.exe (
        echo File created: NCTraverserFixed2.exe
        dir NCTraverserFixed2.exe
        echo.
        echo Testing with NC application...
        echo.
        NCTraverserFixed2.exe NC
    )
) else (
    echo.
    echo Compilation failed!
)

pause
