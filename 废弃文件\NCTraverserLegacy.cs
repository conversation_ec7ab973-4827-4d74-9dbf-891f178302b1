using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Drawing;
using System.Linq;
using System.Threading;
using WindowsAccessBridgeInterop;

/// <summary>
/// NC专用命令行遍历工具 - Legacy版本
/// 尝试使用Legacy Access Bridge API，模仿JavaFerret的工作方式
/// </summary>
class NCTraverserLegacy {
    private static AccessBridge accessBridge;
    private static StreamWriter logWriter;
    private static int elementCount = 0;
    private static HashSet<string> processedElements = new HashSet<string>();
    private static string targetAppName = "";
    
    static void Main(string[] args) {
        Console.WriteLine("=== NC专用命令行遍历工具 - Legacy版本 ===");
        Console.WriteLine("尝试使用Legacy Access Bridge API");
        Console.WriteLine("模仿JavaFerret的工作方式");
        Console.WriteLine();
        
        // 解析命令行参数
        if (args.Length > 0) {
            targetAppName = args[0].ToLower();
            Console.WriteLine("目标应用程序: " + args[0]);
        } else {
            Console.WriteLine("将遍历所有Java应用程序");
            Console.WriteLine("用法: NCTraverserLegacy.exe [应用程序名称]");
        }
        
        Console.WriteLine("正在初始化 Access Bridge...");
        
        try {
            // 创建输出文件
            string outputFile = "NC_Elements_Legacy_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".log";
            logWriter = new StreamWriter(outputFile, false, Encoding.UTF8);
            
            Console.WriteLine("日志文件: " + outputFile);
            
            WriteLog("=== NC专用完整结构树遍历报告 - Legacy版本 ===");
            WriteLog("遍历时间: " + DateTime.Now.ToString());
            WriteLog("目标应用: " + (string.IsNullOrEmpty(targetAppName) ? "所有Java应用" : targetAppName));
            WriteLog("尝试方法: Legacy Access Bridge API");
            WriteLog("参考工具: JavaFerret-32/64.exe");
            WriteLog(new string('=', 60));
            WriteLog("");
            
            // 尝试不同的初始化方式
            Console.WriteLine("尝试不同的Access Bridge初始化方式...");
            
            // 方法1: 标准初始化
            WriteLog("方法1: 标准Access Bridge初始化");
            try {
                accessBridge = new AccessBridge();
                WriteLog("  标准初始化成功");
                Console.WriteLine("  标准初始化成功");
                
                // 检查是否为Legacy模式
                WriteLog("  Legacy模式: " + AccessBridge.IsLegacy);
                Console.WriteLine("  Legacy模式: " + AccessBridge.IsLegacy);
                
                // 长时间等待初始化
                Console.WriteLine("  等待10秒让Access Bridge充分初始化...");
                WriteLog("  等待10秒让Access Bridge充分初始化...");
                Thread.Sleep(10000);
                
                // 测试JVM枚举
                var jvms = TestJvmEnumeration();
                if (jvms.Count > 0) {
                    WriteLog("  成功！发现 " + jvms.Count + " 个JVM");
                    Console.WriteLine("  成功！发现 " + jvms.Count + " 个JVM");
                    ProcessJvms(jvms);
                    return;
                } else {
                    WriteLog("  标准方法仍然无法发现JVM");
                    Console.WriteLine("  标准方法仍然无法发现JVM");
                }
                
            } catch (Exception ex) {
                WriteLog("  标准初始化失败: " + ex.Message);
                Console.WriteLine("  标准初始化失败: " + ex.Message);
            }
            
            // 方法2: 强制Legacy模式（如果可能）
            WriteLog("\n方法2: 尝试强制Legacy模式");
            Console.WriteLine("方法2: 尝试强制Legacy模式");
            
            // 这里我们需要检查是否有强制Legacy模式的方法
            // 根据WindowsAccessBridgeInterop的实现，可能需要特殊的初始化
            
            WriteLog("检查Access Bridge版本和配置...");
            Console.WriteLine("检查Access Bridge版本和配置...");
            
            if (accessBridge != null) {
                try {
                    // 尝试获取所有可能的JVM信息
                    WriteLog("尝试直接访问Access Bridge函数...");
                    
                    // 这里我们可以尝试直接调用底层函数
                    // 但需要更深入了解WindowsAccessBridgeInterop的实现
                    
                } catch (Exception ex) {
                    WriteLog("直接访问失败: " + ex.Message);
                }
            }
            
            // 方法3: 分析JavaFerret的工作原理
            WriteLog("\n方法3: 分析差异");
            Console.WriteLine("方法3: 分析差异");
            
            WriteLog("JavaFerret能工作的可能原因:");
            WriteLog("1. JavaFerret使用了不同的Access Bridge版本");
            WriteLog("2. JavaFerret使用了不同的初始化顺序");
            WriteLog("3. JavaFerret可能直接调用了Windows API");
            WriteLog("4. JavaFerret可能使用了特定的JNI调用");
            
            Console.WriteLine("JavaFerret能工作但我们的工具不能，可能原因:");
            Console.WriteLine("1. 不同的Access Bridge版本或配置");
            Console.WriteLine("2. 不同的初始化方式");
            Console.WriteLine("3. 可能需要特定的Windows API调用");
            
            // 建议
            WriteLog("\n建议的解决方案:");
            WriteLog("1. 分析JavaFerret的源代码（如果可获得）");
            WriteLog("2. 使用Process Monitor监控JavaFerret的系统调用");
            WriteLog("3. 尝试使用相同的Access Bridge DLL版本");
            WriteLog("4. 检查是否需要特定的注册表设置");
            
            Console.WriteLine("\n建议的解决方案:");
            Console.WriteLine("1. 分析JavaFerret的源代码");
            Console.WriteLine("2. 使用Process Monitor监控JavaFerret的系统调用");
            Console.WriteLine("3. 尝试使用相同的Access Bridge DLL版本");
            Console.WriteLine("4. 检查注册表设置");
            
        } catch (Exception ex) {
            string errorMsg = "发生错误: " + ex.Message;
            Console.WriteLine(errorMsg);
            if (logWriter != null) {
                WriteLog(errorMsg);
                WriteLog("错误详情: " + ex.ToString());
            }
        } finally {
            if (logWriter != null) {
                logWriter.Close();
            }
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
    
    /// <summary>
    /// 测试JVM枚举的多种方法
    /// </summary>
    static List<AccessibleJvm> TestJvmEnumeration() {
        WriteLog("测试多种JVM枚举方法...");
        
        var allJvms = new List<AccessibleJvm>();
        
        // 方法1: 直接调用EnumJvms
        try {
            WriteLog("  测试方法1: 直接调用EnumJvms()");
            var jvms1 = accessBridge.EnumJvms();
            WriteLog("    结果: " + jvms1.Count + " 个JVM");
            if (jvms1.Count > 0) allJvms.AddRange(jvms1);
        } catch (Exception ex) {
            WriteLog("    方法1失败: " + ex.Message);
        }
        
        // 方法2: 使用窗口缓存
        try {
            WriteLog("  测试方法2: 使用窗口缓存");
            var jvms2 = accessBridge.EnumJvms(hwnd => {
                try {
                    return accessBridge.CreateAccessibleWindow(hwnd);
                } catch {
                    return null;
                }
            });
            WriteLog("    结果: " + jvms2.Count + " 个JVM");
            if (jvms2.Count > 0) {
                foreach (var jvm in jvms2) {
                    if (!allJvms.Any(j => j.JvmId == jvm.JvmId)) {
                        allJvms.Add(jvm);
                    }
                }
            }
        } catch (Exception ex) {
            WriteLog("    方法2失败: " + ex.Message);
        }
        
        // 方法3: 多次重试
        for (int i = 1; i <= 5; i++) {
            try {
                WriteLog("  测试方法3: 重试 " + i + "/5");
                Thread.Sleep(2000);
                var jvms3 = accessBridge.EnumJvms();
                WriteLog("    重试 " + i + " 结果: " + jvms3.Count + " 个JVM");
                if (jvms3.Count > 0) {
                    foreach (var jvm in jvms3) {
                        if (!allJvms.Any(j => j.JvmId == jvm.JvmId)) {
                            allJvms.Add(jvm);
                        }
                    }
                    break; // 成功就退出重试
                }
            } catch (Exception ex) {
                WriteLog("    重试 " + i + " 失败: " + ex.Message);
            }
        }
        
        WriteLog("总计发现 " + allJvms.Count + " 个唯一JVM");
        return allJvms;
    }
    
    /// <summary>
    /// 处理发现的JVM
    /// </summary>
    static void ProcessJvms(List<AccessibleJvm> jvms) {
        WriteLog("\n开始处理发现的JVM...");
        
        for (int jvmIndex = 0; jvmIndex < jvms.Count; jvmIndex++) {
            var jvm = jvms[jvmIndex];
            string jvmTitle = jvm.GetTitle();
            
            // 检查是否匹配目标应用程序
            bool isTarget = string.IsNullOrEmpty(targetAppName) || 
                           jvmTitle.ToLower().Contains(targetAppName) ||
                           jvmTitle.ToLower().Contains("nc") ||
                           jvmTitle.ToLower().Contains("yonyou");
            
            if (!string.IsNullOrEmpty(targetAppName) && !isTarget) {
                WriteLog("跳过JVM " + (jvmIndex + 1) + ": " + jvmTitle + " (不匹配目标应用)");
                continue;
            }
            
            WriteLog("\n" + new string('=', 80));
            WriteLog("🎯 JVM " + (jvmIndex + 1) + ": " + jvmTitle);
            WriteLog("JVM ID: " + jvm.JvmId);
            WriteLog("窗口数量: " + jvm.Windows.Count);
            
            // 获取版本信息
            try {
                AccessBridgeVersionInfo versionInfo;
                if (accessBridge.Functions.GetVersionInfo(jvm.JvmId, out versionInfo)) {
                    WriteLog("版本信息:");
                    WriteLog("  Java虚拟机版本: " + versionInfo.VMversion);
                    WriteLog("  Access Bridge Java类版本: " + versionInfo.bridgeJavaClassVersion);
                    WriteLog("  Access Bridge Java DLL版本: " + versionInfo.bridgeJavaDLLVersion);
                    WriteLog("  Access Bridge Windows DLL版本: " + versionInfo.bridgeWinDLLVersion);
                }
            } catch (Exception ex) {
                WriteLog("获取版本信息失败: " + ex.Message);
            }
            
            WriteLog(new string('=', 80));
            
            Console.WriteLine("\n🎯 处理目标JVM " + (jvmIndex + 1) + ": " + jvmTitle);
            Console.WriteLine("   窗口数量: " + jvm.Windows.Count);
            
            // 遍历JVM中的所有窗口
            for (int winIndex = 0; winIndex < jvm.Windows.Count; winIndex++) {
                var window = jvm.Windows[winIndex];
                string windowTitle = window.GetTitle();
                
                WriteLog("\n" + new string('-', 60));
                WriteLog("📋 窗口 " + (winIndex + 1) + ": " + windowTitle);
                WriteLog("窗口句柄: " + window.Hwnd);
                
                Console.WriteLine("  📋 遍历窗口 " + (winIndex + 1) + ": " + windowTitle);
                
                // 深度遍历窗口
                TraverseElementEnhanced(window, 0);
            }
        }
        
        WriteLog("\n" + new string('=', 80));
        WriteLog("✅ 遍历完成统计");
        WriteLog(new string('=', 80));
        WriteLog("📊 总计发现: " + elementCount + " 个唯一可访问元素");
        WriteLog("🔍 处理过的元素: " + processedElements.Count + " 个");
        WriteLog("⏰ 完成时间: " + DateTime.Now.ToString());
        WriteLog(new string('=', 80));
        
        Console.WriteLine("\n✅ 遍历完成！");
        Console.WriteLine("📊 总共发现 " + elementCount + " 个唯一可访问元素");
    }
    
    /// <summary>
    /// 增强版元素遍历
    /// </summary>
    static void TraverseElementEnhanced(AccessibleNode element, int depth) {
        if (element == null || depth > 10) return;
        
        try {
            string elementId = GenerateElementId(element);
            if (processedElements.Contains(elementId)) {
                return;
            }
            processedElements.Add(elementId);
            
            elementCount++;
            string indent = new string(' ', depth * 2);
            
            string name = "";
            string role = "";
            string description = "";
            string states = "";
            
            var contextNode = element as AccessibleContextNode;
            if (contextNode != null) {
                try {
                    var info = contextNode.GetInfo();
                    name = info.name ?? "";
                    role = info.role ?? "";
                    description = info.description ?? "";
                    states = info.states ?? "";
                } catch (Exception ex) {
                    WriteLog(indent + "❌ 获取元素信息失败: " + ex.Message);
                }
            } else {
                try {
                    name = element.GetTitle() ?? "";
                    role = element.GetType().Name;
                } catch (Exception ex) {
                    WriteLog(indent + "❌ 获取标题失败: " + ex.Message);
                }
            }
            
            WriteLog(indent + "├─ [" + elementCount + "] " + role + ": " + name);
            
            if (!string.IsNullOrEmpty(description)) {
                WriteLog(indent + "│  描述: " + description);
            }
            
            if (!string.IsNullOrEmpty(states)) {
                WriteLog(indent + "│  状态: " + states);
            }
            
            try {
                var rect = element.GetScreenRectangle();
                if (rect.HasValue) {
                    WriteLog(indent + "│  位置: (" + rect.Value.X + "," + rect.Value.Y + ") 大小: " + rect.Value.Width + "x" + rect.Value.Height);
                }
            } catch {
                WriteLog(indent + "│  位置: 无法获取");
            }
            
            TraverseChildren(element, depth + 1);
            
        } catch (Exception ex) {
            WriteLog(new string(' ', depth * 2) + "❌ 遍历元素时出错: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 遍历子元素
    /// </summary>
    static void TraverseChildren(AccessibleNode element, int depth) {
        try {
            var children = element.GetChildren().ToList();
            if (children != null && children.Count > 0) {
                WriteLog(new string(' ', depth * 2) + "┌─ 子元素 (" + children.Count + " 个)");
                
                for (int i = 0; i < children.Count; i++) {
                    var child = children[i];
                    if (child != null) {
                        TraverseElementEnhanced(child, depth + 1);
                    }
                }
            }
        } catch (Exception ex) {
            WriteLog(new string(' ', depth * 2) + "❌ 子元素遍历失败: " + ex.Message);
        }
    }
    
    /// <summary>
    /// 生成元素唯一标识符
    /// </summary>
    static string GenerateElementId(AccessibleNode element) {
        try {
            string name = "";
            string role = "";
            
            var contextNode = element as AccessibleContextNode;
            if (contextNode != null) {
                var info = contextNode.GetInfo();
                name = info.name ?? "";
                role = info.role ?? "";
            } else {
                name = element.GetTitle() ?? "";
                role = element.GetType().Name;
            }
            
            var rect = element.GetScreenRectangle();
            if (rect.HasValue) {
                return role + "|" + name + "|" + rect.Value.X + "," + rect.Value.Y + "," + rect.Value.Width + "," + rect.Value.Height;
            } else {
                return role + "|" + name + "|" + Guid.NewGuid().ToString();
            }
        } catch {
            return Guid.NewGuid().ToString();
        }
    }
    
    /// <summary>
    /// 写入日志
    /// </summary>
    static void WriteLog(string message) {
        if (logWriter != null) {
            logWriter.WriteLine(message);
            logWriter.Flush();
        }
    }
}
