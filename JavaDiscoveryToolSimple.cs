using System;
using System.Collections.Generic;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using WindowsAccessBridgeInterop;

namespace JavaDiscoveryTool
{
    /// <summary>
    /// Java程序发现工具 - 简化版本
    /// 兼容C# 5语法，基于Java Access Bridge API
    /// </summary>
    public partial class JavaDiscoveryForm : Form
    {
        private AccessBridge accessBridge;
        private TreeView treeViewJavaApps;
        private ListView listViewDetails;
        private Button btnRefresh;
        private Button btnExpandAll;
        private Button btnCollapseAll;
        private StatusStrip statusStrip;
        private ToolStripStatusLabel statusLabel;
        private SplitContainer splitContainer;
        private Panel topPanel;
        private Label lblTitle;

        public JavaDiscoveryForm()
        {
            InitializeComponent();
            InitializeAccessBridge();
        }

        /// <summary>
        /// 初始化界面组件
        /// </summary>
        private void InitializeComponent()
        {
            this.Text = "Java程序发现工具 - Java Access Bridge API";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.Icon = SystemIcons.Application;

            // 创建主面板
            topPanel = new Panel();
            topPanel.Dock = DockStyle.Top;
            topPanel.Height = 80;
            topPanel.BackColor = Color.FromArgb(240, 240, 240);

            // 标题标签
            lblTitle = new Label();
            lblTitle.Text = "Java程序发现和遍历工具";
            lblTitle.Font = new Font("Microsoft YaHei", 14, FontStyle.Bold);
            lblTitle.ForeColor = Color.FromArgb(51, 51, 51);
            lblTitle.Location = new Point(20, 15);
            lblTitle.AutoSize = true;

            // 刷新按钮
            btnRefresh = new Button();
            btnRefresh.Text = "刷新发现";
            btnRefresh.Size = new Size(100, 30);
            btnRefresh.Location = new Point(20, 45);
            btnRefresh.BackColor = Color.FromArgb(0, 120, 215);
            btnRefresh.ForeColor = Color.White;
            btnRefresh.FlatStyle = FlatStyle.Flat;
            btnRefresh.Click += BtnRefresh_Click;

            // 展开所有按钮
            btnExpandAll = new Button();
            btnExpandAll.Text = "展开所有";
            btnExpandAll.Size = new Size(100, 30);
            btnExpandAll.Location = new Point(130, 45);
            btnExpandAll.BackColor = Color.FromArgb(16, 124, 16);
            btnExpandAll.ForeColor = Color.White;
            btnExpandAll.FlatStyle = FlatStyle.Flat;
            btnExpandAll.Click += BtnExpandAll_Click;

            // 折叠所有按钮
            btnCollapseAll = new Button();
            btnCollapseAll.Text = "折叠所有";
            btnCollapseAll.Size = new Size(100, 30);
            btnCollapseAll.Location = new Point(240, 45);
            btnCollapseAll.BackColor = Color.FromArgb(196, 89, 17);
            btnCollapseAll.ForeColor = Color.White;
            btnCollapseAll.FlatStyle = FlatStyle.Flat;
            btnCollapseAll.Click += BtnCollapseAll_Click;

            topPanel.Controls.AddRange(new Control[] { lblTitle, btnRefresh, btnExpandAll, btnCollapseAll });

            // 创建分割容器
            splitContainer = new SplitContainer();
            splitContainer.Dock = DockStyle.Fill;
            splitContainer.Orientation = Orientation.Horizontal;
            splitContainer.SplitterDistance = 400;

            // 创建Java应用程序树视图
            treeViewJavaApps = new TreeView();
            treeViewJavaApps.Dock = DockStyle.Fill;
            treeViewJavaApps.Font = new Font("Consolas", 9);
            treeViewJavaApps.ShowLines = true;
            treeViewJavaApps.ShowPlusMinus = true;
            treeViewJavaApps.ShowRootLines = true;
            treeViewJavaApps.FullRowSelect = true;
            treeViewJavaApps.HideSelection = false;
            treeViewJavaApps.AfterSelect += TreeViewJavaApps_AfterSelect;

            // 创建详细信息列表视图
            listViewDetails = new ListView();
            listViewDetails.Dock = DockStyle.Fill;
            listViewDetails.View = View.Details;
            listViewDetails.FullRowSelect = true;
            listViewDetails.GridLines = true;
            listViewDetails.Font = new Font("Microsoft YaHei", 9);

            // 添加列
            listViewDetails.Columns.Add("属性", 150);
            listViewDetails.Columns.Add("值", 400);
            listViewDetails.Columns.Add("类型", 100);

            splitContainer.Panel1.Controls.Add(treeViewJavaApps);
            splitContainer.Panel2.Controls.Add(listViewDetails);

            // 创建状态栏
            statusStrip = new StatusStrip();
            statusLabel = new ToolStripStatusLabel("就绪 - 点击'刷新发现'开始扫描Java应用程序");
            statusStrip.Items.Add(statusLabel);

            // 添加控件到窗体
            this.Controls.Add(splitContainer);
            this.Controls.Add(topPanel);
            this.Controls.Add(statusStrip);
        }

        /// <summary>
        /// 初始化Access Bridge
        /// </summary>
        private void InitializeAccessBridge()
        {
            try
            {
                accessBridge = new AccessBridge();
                statusLabel.Text = "Access Bridge初始化成功";
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("Access Bridge初始化失败: {0}", ex.Message), "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "Access Bridge初始化失败";
            }
        }

        /// <summary>
        /// 刷新发现Java应用程序
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            DiscoverJavaApplications();
        }

        /// <summary>
        /// 展开所有节点
        /// </summary>
        private void BtnExpandAll_Click(object sender, EventArgs e)
        {
            treeViewJavaApps.ExpandAll();
            statusLabel.Text = "已展开所有节点";
        }

        /// <summary>
        /// 折叠所有节点
        /// </summary>
        private void BtnCollapseAll_Click(object sender, EventArgs e)
        {
            treeViewJavaApps.CollapseAll();
            statusLabel.Text = "已折叠所有节点";
        }

        /// <summary>
        /// 发现Java应用程序
        /// </summary>
        private void DiscoverJavaApplications()
        {
            try
            {
                statusLabel.Text = "正在扫描Java应用程序...";
                treeViewJavaApps.Nodes.Clear();
                listViewDetails.Items.Clear();

                Application.DoEvents();

                // 获取所有窗口并检查是否为Java窗口
                var javaWindows = FindJavaWindows();
                
                if (javaWindows.Count == 0)
                {
                    var noJavaNode = new TreeNode("未发现Java应用程序");
                    noJavaNode.ForeColor = Color.Red;
                    noJavaNode.Tag = null;
                    treeViewJavaApps.Nodes.Add(noJavaNode);
                    statusLabel.Text = "未发现Java应用程序";
                    return;
                }

                // 为每个Java窗口创建树节点
                foreach (var javaWindow in javaWindows)
                {
                    CreateJavaWindowNode(javaWindow);
                }

                statusLabel.Text = string.Format("发现 {0} 个Java应用程序", javaWindows.Count);
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("发现Java应用程序时出错: {0}", ex.Message), "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel.Text = "发现过程出错";
            }
        }

        /// <summary>
        /// 查找Java窗口
        /// </summary>
        private List<IntPtr> FindJavaWindows()
        {
            var javaWindows = new List<IntPtr>();
            
            // 枚举所有顶级窗口
            EnumWindows(delegate(IntPtr hWnd, IntPtr lParam)
            {
                if (IsWindowVisible(hWnd) && accessBridge.Functions.IsJavaWindow(hWnd))
                {
                    javaWindows.Add(hWnd);
                }
                return true;
            }, IntPtr.Zero);

            return javaWindows;
        }

        /// <summary>
        /// 为Java窗口创建树节点
        /// </summary>
        private void CreateJavaWindowNode(IntPtr hWnd)
        {
            try
            {
                // 获取窗口标题
                var windowTitle = GetWindowTitle(hWnd);
                
                // 获取AccessibleContext
                int vmId;
                JavaObjectHandle ac;
                if (accessBridge.Functions.GetAccessibleContextFromHWND(hWnd, out vmId, out ac))
                {
                    var rootNode = new TreeNode(string.Format("Java窗口: {0} (HWND: {1})", windowTitle, hWnd));
                    var windowInfo = new JavaWindowInfo();
                    windowInfo.HWnd = hWnd;
                    windowInfo.VmId = vmId;
                    windowInfo.AccessibleContext = ac;
                    rootNode.Tag = windowInfo;
                    rootNode.ImageIndex = 0;

                    // 递归添加子节点
                    AddChildNodes(rootNode, vmId, ac, 0, 3); // 限制深度为3层

                    treeViewJavaApps.Nodes.Add(rootNode);
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode(string.Format("错误: {0}", ex.Message));
                errorNode.ForeColor = Color.Red;
                treeViewJavaApps.Nodes.Add(errorNode);
            }
        }

        /// <summary>
        /// 递归添加子节点
        /// </summary>
        private void AddChildNodes(TreeNode parentNode, int vmId, JavaObjectHandle ac, int currentDepth, int maxDepth)
        {
            if (currentDepth >= maxDepth) return;

            try
            {
                // 获取AccessibleContext信息
                AccessibleContextInfo contextInfo;
                if (accessBridge.Functions.GetAccessibleContextInfo(vmId, ac, out contextInfo))
                {
                    // 遍历子元素
                    for (int i = 0; i < contextInfo.childrenCount && i < 20; i++) // 限制每层最多20个子元素
                    {
                        var childAc = accessBridge.Functions.GetAccessibleChildFromContext(vmId, ac, i);
                        if (!childAc.IsNull)
                        {
                            AccessibleContextInfo childInfo;
                            if (accessBridge.Functions.GetAccessibleContextInfo(vmId, childAc, out childInfo))
                            {
                                var childNode = new TreeNode(string.Format("{0} [{1}]", childInfo.name, childInfo.role));
                                var elementInfo = new JavaElementInfo();
                                elementInfo.VmId = vmId;
                                elementInfo.AccessibleContext = childAc;
                                elementInfo.ContextInfo = childInfo;
                                childNode.Tag = elementInfo;

                                parentNode.Nodes.Add(childNode);

                                // 递归添加子节点
                                if (childInfo.childrenCount > 0)
                                {
                                    AddChildNodes(childNode, vmId, childAc, currentDepth + 1, maxDepth);
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                var errorNode = new TreeNode(string.Format("加载子节点错误: {0}", ex.Message));
                errorNode.ForeColor = Color.Red;
                parentNode.Nodes.Add(errorNode);
            }
        }

        /// <summary>
        /// 树视图选择变化事件
        /// </summary>
        private void TreeViewJavaApps_AfterSelect(object sender, TreeViewEventArgs e)
        {
            ShowElementDetails(e.Node);
        }

        /// <summary>
        /// 显示元素详细信息
        /// </summary>
        private void ShowElementDetails(TreeNode node)
        {
            listViewDetails.Items.Clear();

            if (node == null || node.Tag == null) return;

            try
            {
                if (node.Tag is JavaWindowInfo)
                {
                    ShowWindowDetails((JavaWindowInfo)node.Tag);
                }
                else if (node.Tag is JavaElementInfo)
                {
                    ShowElementDetails((JavaElementInfo)node.Tag);
                }
            }
            catch (Exception ex)
            {
                AddDetailItem("错误", ex.Message, "Error");
            }
        }

        /// <summary>
        /// 显示窗口详细信息
        /// </summary>
        private void ShowWindowDetails(JavaWindowInfo windowInfo)
        {
            AddDetailItem("窗口句柄", string.Format("0x{0:X8}", windowInfo.HWnd), "HWND");
            AddDetailItem("虚拟机ID", windowInfo.VmId.ToString(), "VM ID");

            // 获取窗口标题
            var title = GetWindowTitle(windowInfo.HWnd);
            AddDetailItem("窗口标题", title, "String");

            // 获取AccessibleContext信息
            AccessibleContextInfo contextInfo;
            if (accessBridge.Functions.GetAccessibleContextInfo(windowInfo.VmId, windowInfo.AccessibleContext, out contextInfo))
            {
                ShowContextInfo(contextInfo);
            }

            // 获取版本信息
            AccessBridgeVersionInfo versionInfo;
            if (accessBridge.Functions.GetVersionInfo(windowInfo.VmId, out versionInfo))
            {
                AddDetailItem("Java VM版本", versionInfo.VMversion, "Version");
                AddDetailItem("Access Bridge版本", versionInfo.bridgeJavaClassVersion, "Version");
            }
        }

        /// <summary>
        /// 显示元素详细信息
        /// </summary>
        private void ShowElementDetails(JavaElementInfo elementInfo)
        {
            AddDetailItem("虚拟机ID", elementInfo.VmId.ToString(), "VM ID");
            AddDetailItem("AccessibleContext", string.Format("0x{0:X8}", elementInfo.AccessibleContext), "Pointer");

            ShowContextInfo(elementInfo.ContextInfo);
        }

        /// <summary>
        /// 显示AccessibleContext信息
        /// </summary>
        private void ShowContextInfo(AccessibleContextInfo contextInfo)
        {
            AddDetailItem("名称", contextInfo.name, "String");
            AddDetailItem("描述", contextInfo.description, "String");
            AddDetailItem("角色", contextInfo.role, "Role");
            AddDetailItem("状态", contextInfo.states, "States");
            AddDetailItem("父级索引", contextInfo.indexInParent.ToString(), "Integer");
            AddDetailItem("子元素数量", contextInfo.childrenCount.ToString(), "Integer");
            AddDetailItem("X坐标", contextInfo.x.ToString(), "Integer");
            AddDetailItem("Y坐标", contextInfo.y.ToString(), "Integer");
            AddDetailItem("宽度", contextInfo.width.ToString(), "Integer");
            AddDetailItem("高度", contextInfo.height.ToString(), "Integer");
            AddDetailItem("支持组件接口", contextInfo.accessibleComponent != 0 ? "是" : "否", "Boolean");
            AddDetailItem("支持动作接口", contextInfo.accessibleAction != 0 ? "是" : "否", "Boolean");
            AddDetailItem("支持选择接口", contextInfo.accessibleSelection != 0 ? "是" : "否", "Boolean");
            AddDetailItem("支持文本接口", contextInfo.accessibleText != 0 ? "是" : "否", "Boolean");
        }

        /// <summary>
        /// 添加详细信息项
        /// </summary>
        private void AddDetailItem(string property, string value, string type)
        {
            var item = new ListViewItem(property);
            item.SubItems.Add(value ?? "");
            item.SubItems.Add(type);
            listViewDetails.Items.Add(item);
        }

        /// <summary>
        /// 获取窗口标题
        /// </summary>
        private string GetWindowTitle(IntPtr hWnd)
        {
            const int maxLength = 256;
            var title = new StringBuilder(maxLength);
            GetWindowText(hWnd, title, maxLength);
            return title.ToString();
        }

        /// <summary>
        /// 窗体关闭时清理资源
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                if (accessBridge != null)
                {
                    accessBridge.Dispose();
                }
            }
            catch (Exception ex)
            {
                // 忽略清理时的错误
                System.Diagnostics.Debug.WriteLine(string.Format("清理Access Bridge时出错: {0}", ex.Message));
            }
            base.OnFormClosed(e);
        }

        #region Windows API声明

        [DllImport("user32.dll")]
        private static extern bool EnumWindows(EnumWindowsProc enumProc, IntPtr lParam);

        [DllImport("user32.dll")]
        private static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Unicode)]
        private static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        private delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        #endregion

        #region 数据结构

        /// <summary>
        /// Java窗口信息
        /// </summary>
        public class JavaWindowInfo
        {
            public IntPtr HWnd { get; set; }
            public int VmId { get; set; }
            public JavaObjectHandle AccessibleContext { get; set; }
        }

        /// <summary>
        /// Java元素信息
        /// </summary>
        public class JavaElementInfo
        {
            public int VmId { get; set; }
            public JavaObjectHandle AccessibleContext { get; set; }
            public AccessibleContextInfo ContextInfo { get; set; }
        }

        #endregion
    }

    /// <summary>
    /// 程序入口点
    /// </summary>
    public class Program
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            try
            {
                Application.Run(new JavaDiscoveryForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show(string.Format("应用程序启动失败: {0}", ex.Message), "错误",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
