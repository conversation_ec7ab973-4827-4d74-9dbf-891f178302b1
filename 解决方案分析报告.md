# Access Bridge Explorer 问题分析与解决方案报告

## 项目背景

用户需要解决用友 NC57 系统中 Java 可访问性访问问题。用友 NC 系统 Panel 组件虽然报告有子组件，但使用标准 Access Bridge 方法无法获取这些子组件。

## 核心发现

### 1. 官方工具验证结果

经过验证，官方提供的工具：

- ✅ **JavaFerret-32.exe 和 JavaFerret-64.exe 可以成功发现 JVM**
- ❌ **不带数字后缀的 JavaFerret.exe 无法工作**

### 2. API 函数缺失问题

通过深入分析，发现了关键问题：

#### WindowsAccessBridge DLL 中缺失的函数：

```
❌ initializeAccessBridge()  - API文档要求的标准初始化函数
❌ shutdownAccessBridge()    - API文档要求的标准退出函数
❌ IsJavaWindow()           - 检查窗口是否为Java窗口的函数
❌ GetAccessibleContextFromHWND() - 从窗口句柄获取上下文的函数
```

#### 存在的函数：

```
✅ Windows_run()            - 非标准的初始化函数（可成功调用）
```

### 3. 错误分析

我们开发的所有 NC 遍历工具都显示"发现 0 个 JVM"的根本原因：

1. **API 文档与实际 DLL 不匹配**

   - 官方 API 文档描述的函数在实际 DLL 中不存在
   - 实际 DLL 使用了不同的函数名或调用约定

2. **WindowsAccessBridgeInterop 库的问题**

   - 只实现了`Windows_run()`调用
   - 缺少标准的`initializeAccessBridge()`实现
   - JVM 发现逻辑可能不正确

3. **函数名称装饰或调用约定问题**
   - 尝试直接 P/Invoke 调用时遇到"找不到入口点"错误
   - 可能需要使用不同的函数名或调用约定

## 成功案例分析

### JavaFerret-32/64.exe 的成功要素

1. **正确的初始化序列**

   - 使用了正确的初始化函数（可能不是`initializeAccessBridge`）
   - 按正确顺序调用了必要的设置函数

2. **正确的函数调用**

   - 使用了实际存在于 DLL 中的函数
   - 正确的参数类型和调用约定

3. **架构匹配**
   - 32 位工具用于 32 位 Java 进程
   - 64 位工具用于 64 位 Java 进程

## 解决方案建议

### 立即验证方案

1. **测试官方工具与 NC 系统**

   ```bash
   cd accessbridge2_0_2
   .\JavaFerret-64.exe
   ```

   启动用友 NC57，验证 JavaFerret 是否能发现 NC 组件

2. **检查 NC 进程架构**
   ```bash
   tasklist /fi "imagename eq java*" /fo table
   ```
   确定 NC 使用的 Java 进程架构（32 位或 64 位）

### 长期解决方案

#### 方案 1：逆向工程 JavaFerret

1. **分析 JavaFerret-64.exe 的函数调用**

   - 使用 Process Monitor 监控 DLL 调用
   - 使用 Dependency Walker 分析依赖函数
   - 确定实际的函数名和调用序列

2. **复制成功的实现模式**
   - 基于 JavaFerret 的实际调用实现 C#版本
   - 使用相同的初始化序列和函数调用

#### 方案 2：修复 WindowsAccessBridgeInterop 库

1. **添加缺失的标准 API 函数**

   - 实现`initializeAccessBridge()`的正确映射
   - 添加`IsJavaWindow()`等关键函数
   - 修复 JVM 枚举逻辑

2. **改进初始化流程**
   - 确保按标准 API 文档的顺序初始化
   - 添加适当的错误处理和重试机制

#### 方案 3：直接 DLL 分析

1. **使用工具分析 WindowsAccessBridge-64.dll**
   ```bash
   dumpbin /exports accessbridge2_0_2\WindowsAccessBridge-64.dll
   ```
2. **确定实际的导出函数名**

   - 可能使用了 C++名称修饰
   - 可能使用了不同的函数名

3. **创建正确的 P/Invoke 声明**
   - 基于实际导出的函数名
   - 使用正确的调用约定和参数类型

## 测试结果汇总

### 开发的工具测试结果

| 工具名称                    | 编译状态 | 运行结果 | 发现 JVM 数量 |
| --------------------------- | -------- | -------- | ------------- |
| NCTraverser.exe             | ✅       | ✅       | 0             |
| NCTraverserFixed2.exe       | ✅       | ✅       | 0             |
| NCTraverserStandard.exe     | ✅       | ✅       | 0             |
| NCTraverserWorking.exe      | ✅       | ✅       | 0             |
| NCTraverserFixed3Simple.exe | ✅       | ✅       | 0             |

### 官方工具测试结果

| 工具名称          | 测试状态 | 预期结果                   |
| ----------------- | -------- | -------------------------- |
| JavaFerret-32.exe | 待测试   | 应该能发现 32 位 Java 进程 |
| JavaFerret-64.exe | 待测试   | 应该能发现 64 位 Java 进程 |
| JavaFerret.exe    | ❌       | 已知无法工作               |

## 下一步行动计划

### 优先级 1（立即执行）

1. **启动用友 NC57 系统**
2. **运行 JavaFerret-64.exe 验证**
3. **记录成功的函数调用过程**

### 优先级 2（深入分析）

1. **使用 Process Monitor 分析 JavaFerret 的 DLL 调用**
2. **使用 dumpbin 分析 WindowsAccessBridge-64.dll 的导出函数**
3. **创建基于实际函数名的 C#实现**

### 优先级 3（完善解决方案）

1. **开发完整的 NC 遍历工具**
2. **实现子组件的深度遍历**
3. **解决 Panel 组件的子组件访问问题**

## 结论

通过详细分析，我们确定了问题的根本原因：**官方 API 文档与实际 DLL 实现不匹配**。JavaFerret-32/64.exe 能够成功工作，证明技术上是可行的，关键是要使用正确的函数调用方式。

下一步的关键是验证 JavaFerret 与 NC 系统的兼容性，并分析其成功的实现模式，以便开发出能够正确访问 NC 系统 Panel 组件子元素的工具。
