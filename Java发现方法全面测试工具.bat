@echo off
echo ========================================
echo Java发现方法全面测试工具
echo ========================================
echo.
echo 这个工具可以让您逐个测试不同的Java组件发现方法：
echo.
echo 🔍 可测试的方法：
echo   1. GetChildren (标准方法)
echo   2. GetChildrenEnhanced (增强方法)
echo   3. GetVisibleChildren (可见子组件)
echo   4. GetAccessibleSelection (选择项)
echo   5. 坐标搜索 (GetAccessibleContextAt)
echo   6. GetAccessibleTable (表格信息)
echo   7. GetAccessibleText (文本信息)
echo   8. 深度递归搜索 (多层级搜索)
echo   9. 组合测试 (对比所有方法)
echo.
echo 📋 使用方法：
echo   1. 确保用友NC或其他Java程序正在运行
echo   2. 点击"刷新Java窗口"按钮
echo   3. 在左侧树中选择一个组件
echo   4. 点击右侧的测试按钮逐个测试
echo   5. 查看下方日志了解各方法的效果
echo.
echo 💡 特别说明：
echo   - 这个工具专门用于发现用友NC等复杂Java应用中的隐藏组件
echo   - 不同方法在不同类型的组件上效果不同
echo   - 建议先用"组合测试"快速了解哪种方法最有效
echo   - 然后针对性地使用效果最好的方法
echo.
echo 按任意键启动工具...
pause
echo.
echo 启动中...
SimpleJavaMethodTester.exe
