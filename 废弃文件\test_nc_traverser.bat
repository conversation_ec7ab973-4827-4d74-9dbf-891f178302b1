@echo off
echo ===============================================
echo NC专用遍历工具 - 快速测试
echo ===============================================

echo.
echo 请确保：
echo 1. 用友NC 5.7应用程序正在运行
echo 2. Java Access Bridge已正确安装
echo.

echo 检查工具文件...
if not exist "NCTraverser.exe" (
    echo ❌ 错误: NCTraverser.exe 不存在
    echo 请先运行 compile_final.bat 编译工具
    pause
    exit /b 1
)

if not exist "src\WindowsAccessBridgeInterop\bin\Debug\WindowsAccessBridgeInterop.dll" (
    echo ❌ 错误: WindowsAccessBridgeInterop.dll 不存在
    echo 请先运行 build.bat 编译依赖项
    pause
    exit /b 1
)

echo ✅ 工具文件检查通过

echo.
echo 选择测试模式：
echo 1. 遍历所有Java应用程序
echo 2. 只遍历包含"NC"的应用程序
echo 3. 只遍历包含"yonyou"的应用程序
echo 4. 退出
echo.

set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 🚀 开始遍历所有Java应用程序...
    echo 这可能需要几分钟时间，请耐心等待...
    echo.
    NCTraverser.exe
) else if "%choice%"=="2" (
    echo.
    echo 🚀 开始遍历包含"NC"的应用程序...
    echo.
    NCTraverser.exe NC
) else if "%choice%"=="3" (
    echo.
    echo 🚀 开始遍历包含"yonyou"的应用程序...
    echo.
    NCTraverser.exe yonyou
) else if "%choice%"=="4" (
    echo 退出测试
    exit /b 0
) else (
    echo 无效选择，退出
    pause
    exit /b 1
)

echo.
echo ===============================================
echo 测试完成！
echo ===============================================

echo.
echo 检查生成的日志文件：
for %%f in (NC_Elements_Complete_*.log) do (
    echo ✅ 找到日志文件: %%f
    echo    文件大小: 
    dir "%%f" | find "%%f"
    echo.
    echo 💡 建议用文本编辑器打开查看详细内容
    echo    或者用以下命令查看前50行：
    echo    type "%%f" ^| more
)

echo.
echo 如果没有找到日志文件，可能是：
echo 1. 没有找到Java应用程序
echo 2. Access Bridge未正确安装
echo 3. 权限不足
echo.

pause
