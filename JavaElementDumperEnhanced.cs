﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using WindowsAccessBridgeInterop;

/// <summary>
/// NC专用命令行遍历工具 - 完整遍历用友NC系统结构树
/// 使用多种策略确保获取所有可访问元素，输出详细日志便于分析
/// </summary>
class JavaElementDumperEnhanced {
    private static AccessBridge accessBridge;
    private static StreamWriter logWriter;
    private static int elementCount = 0;
    private static HashSet<string> processedElements = new HashSet<string>();
    private static string targetAppName = ""; // 目标应用程序名称

    static void Main(string[] args) {
        Console.WriteLine("=== NC专用命令行遍历工具 ===");
        Console.WriteLine("专门用于完整遍历用友NC 5.7系统结构树");
        Console.WriteLine("输出详细日志文件便于分析");
        Console.WriteLine();

        // 解析命令行参数
        if (args.Length > 0) {
            targetAppName = args[0].ToLower();
            Console.WriteLine($"目标应用程序: {args[0]}");
        } else {
            Console.WriteLine("将遍历所有Java应用程序（建议指定NC应用程序名称）");
            Console.WriteLine("用法: JavaElementDumperEnhanced.exe [应用程序名称]");
        }

        Console.WriteLine("正在初始化 Access Bridge...");

        try {
            // 初始化Access Bridge
            accessBridge = new AccessBridge();

            // 创建输出文件
            string outputFile = "NC_Elements_Complete_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".log";
            logWriter = new StreamWriter(outputFile, false, Encoding.UTF8);

            Console.WriteLine($"日志文件: {outputFile}");
            Console.WriteLine("开始深度遍历Java应用程序...\n");

            WriteLog("=== NC专用完整结构树遍历报告 ===");
            WriteLog($"遍历时间: {DateTime.Now}");
            WriteLog($"目标应用: {(string.IsNullOrEmpty(targetAppName) ? "所有Java应用" : targetAppName)}");
            WriteLog("遍历策略: 标准遍历 + 可见子组件 + 坐标搜索 + 选择遍历 + 深度递归");
            WriteLog("=" + new string('=', 60));
            WriteLog("");
            
            // 获取所有Java应用程序
            var jvms = accessBridge.EnumJvms();
            WriteLog($"发现 {jvms.Count} 个Java虚拟机");
            Console.WriteLine($"发现 {jvms.Count} 个Java虚拟机");

            if (jvms.Count == 0) {
                WriteLog("❌ 没有发现运行中的Java应用程序");
                WriteLog("请确保:");
                WriteLog("1. NC 5.7应用程序正在运行");
                WriteLog("2. Java Access Bridge已正确安装");
                WriteLog("3. 应用程序启用了可访问性支持");
                Console.WriteLine("❌ 没有发现运行中的Java应用程序");
                Console.WriteLine("请确保NC应用程序正在运行且Access Bridge已安装");
                return;
            }

            // 遍历每个JVM，重点关注目标应用
            bool foundTarget = false;
            for (int jvmIndex = 0; jvmIndex < jvms.Count; jvmIndex++) {
                var jvm = jvms[jvmIndex];
                string jvmTitle = jvm.GetTitle();

                // 如果指定了目标应用程序，检查是否匹配
                bool isTarget = string.IsNullOrEmpty(targetAppName) ||
                               jvmTitle.ToLower().Contains(targetAppName) ||
                               jvmTitle.ToLower().Contains("nc") ||
                               jvmTitle.ToLower().Contains("yonyou");

                if (!string.IsNullOrEmpty(targetAppName) && !isTarget) {
                    WriteLog($"跳过JVM {jvmIndex + 1}: {jvmTitle} (不匹配目标应用)");
                    continue;
                }

                foundTarget = true;
                WriteLog($"\n" + new string('=', 80));
                WriteLog($"🎯 JVM {jvmIndex + 1}: {jvmTitle}");
                WriteLog($"JVM ID: {jvm.JvmId}");
                WriteLog($"窗口数量: {jvm.Windows.Count}");
                WriteLog(new string('=', 80));

                Console.WriteLine($"\n🎯 处理目标JVM {jvmIndex + 1}: {jvmTitle}");
                Console.WriteLine($"   窗口数量: {jvm.Windows.Count}");

                // 遍历JVM中的所有窗口
                for (int winIndex = 0; winIndex < jvm.Windows.Count; winIndex++) {
                    var window = jvm.Windows[winIndex];
                    string windowTitle = window.GetTitle();

                    WriteLog($"\n" + new string('-', 60));
                    WriteLog($"📋 窗口 {winIndex + 1}: {windowTitle}");
                    WriteLog($"窗口句柄: {window.Hwnd}");
                    WriteLog(new string('-', 60));

                    Console.WriteLine($"  📋 遍历窗口 {winIndex + 1}: {windowTitle}");

                    // 深度遍历窗口
                    TraverseElementEnhanced(window, 0);
                }
            }

            if (!foundTarget && !string.IsNullOrEmpty(targetAppName)) {
                WriteLog($"\n⚠️ 警告: 未找到匹配 '{targetAppName}' 的应用程序");
                WriteLog("建议检查应用程序名称或不指定参数遍历所有应用");
                Console.WriteLine($"⚠️ 未找到匹配 '{targetAppName}' 的应用程序");
            }
            
            WriteLog($"\n" + new string('=', 80));
            WriteLog($"✅ 遍历完成统计");
            WriteLog(new string('=', 80));
            WriteLog($"📊 总计发现: {elementCount} 个唯一可访问元素");
            WriteLog($"🔍 处理过的元素: {processedElements.Count} 个");
            WriteLog($"⏰ 完成时间: {DateTime.Now}");
            WriteLog($"📁 日志文件: {outputFile}");
            WriteLog(new string('=', 80));

            Console.WriteLine($"\n✅ 遍历完成！");
            Console.WriteLine($"📊 总共发现 {elementCount} 个唯一可访问元素");
            Console.WriteLine($"📁 详细日志已保存到: {outputFile}");
            Console.WriteLine($"💡 建议用文本编辑器打开日志文件查看完整结构树");
            
        } catch (Exception ex) {
            var errorMsg = $"错误: {ex.Message}";
            WriteLog(errorMsg);
            Console.WriteLine(errorMsg);
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        } finally {
            logWriter?.Close();
            accessBridge?.Dispose();
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
    
    /// <summary>
    /// 增强版元素遍历 - 使用多种策略获取子元素
    /// </summary>
    static void TraverseElementEnhanced(AccessibleNode element, int depth) {
        try {
            string indent = new string(' ', depth * 2);
            
            // 生成元素唯一标识符
            string elementId = GenerateElementId(element);
            if (processedElements.Contains(elementId)) {
                return; // 避免重复处理
            }
            processedElements.Add(elementId);
            
            elementCount++;
            
            // 获取元素详细信息
            string info = GetElementInfo(element, indent);
            WriteLog(info);
            
            // 显示进度
            if (elementCount % 50 == 0) {
                Console.WriteLine($"  已处理 {elementCount} 个唯一元素...");
            }
            
            // 多重策略获取子元素
            var allChildren = GetChildrenByMultipleStrategies(element, indent);
            
            // 遍历所有找到的子元素
            foreach (var child in allChildren) {
                if (child != null) {
                    TraverseElementEnhanced(child, depth + 1);
                }
            }
            
        } catch (Exception ex) {
            WriteLog($"处理元素时出错 (深度 {depth}): {ex.Message}");
        }
    }
    
    /// <summary>
    /// 使用多种策略获取子元素
    /// </summary>
    static List<AccessibleNode> GetChildrenByMultipleStrategies(AccessibleNode element, string indent) {
        var allChildren = new List<AccessibleNode>();
        var childIds = new HashSet<string>();
        
        WriteLog($"{indent}  开始多重策略子元素搜索:");
        
        // 策略1: 标准子元素获取
        try {
            var standardChildren = element.GetChildren().ToList();
            WriteLog($"{indent}    策略1 (标准): 发现 {standardChildren.Count} 个子元素");
            
            foreach (var child in standardChildren) {
                string childId = GenerateElementId(child);
                if (childIds.Add(childId)) {
                    allChildren.Add(child);
                }
            }
        } catch (Exception ex) {
            WriteLog($"{indent}    策略1 (标准): 失败 - {ex.Message}");
        }
        
        // 策略2: 可见子元素（仅对AccessibleContextNode）
        if (element is AccessibleContextNode contextNode) {
            try {
                var visibleChildren = GetVisibleChildren(contextNode);
                WriteLog($"{indent}    策略2 (可见): 发现 {visibleChildren.Count} 个子元素");
                
                foreach (var child in visibleChildren) {
                    string childId = GenerateElementId(child);
                    if (childIds.Add(childId)) {
                        allChildren.Add(child);
                    }
                }
            } catch (Exception ex) {
                WriteLog($"{indent}    策略2 (可见): 失败 - {ex.Message}");
            }
            
            // 策略3: 通过选择获取
            try {
                var selectionChildren = GetChildrenThroughSelection(contextNode);
                WriteLog($"{indent}    策略3 (选择): 发现 {selectionChildren.Count} 个子元素");
                
                foreach (var child in selectionChildren) {
                    string childId = GenerateElementId(child);
                    if (childIds.Add(childId)) {
                        allChildren.Add(child);
                    }
                }
            } catch (Exception ex) {
                WriteLog($"{indent}    策略3 (选择): 失败 - {ex.Message}");
            }
            
            // 策略4: 坐标网格搜索
            try {
                var coordinateChildren = GetChildrenByCoordinateSearch(contextNode);
                WriteLog($"{indent}    策略4 (坐标): 发现 {coordinateChildren.Count} 个子元素");
                
                foreach (var child in coordinateChildren) {
                    string childId = GenerateElementId(child);
                    if (childIds.Add(childId)) {
                        allChildren.Add(child);
                    }
                }
            } catch (Exception ex) {
                WriteLog($"{indent}    策略4 (坐标): 失败 - {ex.Message}");
            }
        }
        
        WriteLog($"{indent}  多重策略总计: 发现 {allChildren.Count} 个唯一子元素");
        return allChildren;
    }
    
    /// <summary>
    /// 获取可见子元素
    /// </summary>
    static List<AccessibleNode> GetVisibleChildren(AccessibleContextNode contextNode) {
        var children = new List<AccessibleNode>();
        
        try {
            var visibleCount = accessBridge.Functions.GetVisibleChildrenCount(contextNode.JvmId, contextNode.AccessibleContextHandle);
            if (visibleCount > 0) {
                VisibleChildrenInfo childrenInfo;
                if (accessBridge.Functions.GetVisibleChildren(contextNode.JvmId, contextNode.AccessibleContextHandle, 0, out childrenInfo)) {
                    for (int i = 0; i < Math.Min(childrenInfo.returnedChildrenCount, 100); i++) {
                        var childHandle = childrenInfo.children[i];
                        if (!childHandle.IsNull) {
                            var childNode = new AccessibleContextNode(accessBridge, childHandle);
                            children.Add(childNode);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            // 忽略错误
        }
        
        return children;
    }
    
    /// <summary>
    /// 通过选择机制获取子元素
    /// </summary>
    static List<AccessibleNode> GetChildrenThroughSelection(AccessibleContextNode contextNode) {
        var children = new List<AccessibleNode>();
        
        try {
            var info = contextNode.GetInfo();
            if (info.accessibleSelection != 0) {
                var selCount = accessBridge.Functions.GetAccessibleSelectionCountFromContext(contextNode.JvmId, contextNode.AccessibleContextHandle);
                for (int i = 0; i < Math.Min(selCount, 50); i++) {
                    var selectedHandle = accessBridge.Functions.GetAccessibleSelectionFromContext(contextNode.JvmId, contextNode.AccessibleContextHandle, i);
                    if (!selectedHandle.IsNull) {
                        var selectedNode = new AccessibleContextNode(accessBridge, selectedHandle);
                        children.Add(selectedNode);
                    }
                }
            }
        } catch (Exception ex) {
            // 忽略错误
        }
        
        return children;
    }
    
    /// <summary>
    /// 通过坐标搜索获取子元素
    /// </summary>
    static List<AccessibleNode> GetChildrenByCoordinateSearch(AccessibleContextNode contextNode) {
        var children = new List<AccessibleNode>();
        var foundHandles = new HashSet<string>();
        
        try {
            var rect = contextNode.GetScreenRectangle();
            if (rect.HasValue) {
                var searchRect = rect.Value;
                
                // 简化的网格搜索，避免性能问题
                int stepX = Math.Max(20, searchRect.Width / 8);
                int stepY = Math.Max(20, searchRect.Height / 8);
                
                for (int x = searchRect.X; x < searchRect.X + searchRect.Width; x += stepX) {
                    for (int y = searchRect.Y; y < searchRect.Y + searchRect.Height; y += stepY) {
                        try {
                            JavaObjectHandle childHandle;
                            if (accessBridge.Functions.GetAccessibleContextAt(contextNode.JvmId, contextNode.AccessibleContextHandle, x, y, out childHandle)) {
                                if (!childHandle.IsNull) {
                                    string handleId = $"{childHandle.JvmId}_{childHandle.Handle.Value}";
                                    if (foundHandles.Add(handleId)) {
                                        var childNode = new AccessibleContextNode(accessBridge, childHandle);
                                        
                                        // 确保不是自己
                                        if (!IsSameElement(childNode, contextNode)) {
                                            children.Add(childNode);
                                        }
                                    }
                                }
                            }
                        } catch {
                            // 忽略单个点的错误
                        }
                    }
                }
            }
        } catch (Exception ex) {
            // 忽略错误
        }
        
        return children;
    }
    
    /// <summary>
    /// 生成元素唯一标识符
    /// </summary>
    static string GenerateElementId(AccessibleNode element) {
        if (element is AccessibleContextNode contextNode) {
            return $"{contextNode.JvmId}_{contextNode.AccessibleContextHandle.Handle.Value}";
        } else if (element is AccessibleWindow window) {
            return $"Window_{window.Hwnd}_{window.JvmId}";
        } else {
            return $"Other_{element.GetHashCode()}_{element.JvmId}";
        }
    }
    
    /// <summary>
    /// 检查两个元素是否相同
    /// </summary>
    static bool IsSameElement(AccessibleNode element1, AccessibleNode element2) {
        if (element1 is AccessibleContextNode context1 && element2 is AccessibleContextNode context2) {
            return accessBridge.Functions.IsSameObject(context1.JvmId, context1.AccessibleContextHandle, context2.AccessibleContextHandle);
        }
        return false;
    }
    
    /// <summary>
    /// 获取元素详细信息
    /// </summary>
    static string GetElementInfo(AccessibleNode element, string indent) {
        var info = new StringBuilder();
        info.Append($"{indent}[{elementCount}] ");
        
        try {
            string title = element.GetTitle() ?? "无标题";
            info.Append(title);
            
            if (element is AccessibleContextNode contextNode) {
                try {
                    var contextInfo = contextNode.GetInfo();
                    var rect = contextNode.GetScreenRectangle();
                    
                    info.Append($" | 角色:{contextInfo.role ?? "无"}");
                    info.Append($" | 名称:{contextInfo.name ?? "无"}");
                    info.Append($" | 状态:{contextInfo.states_en_US ?? "无"}");
                    info.Append($" | 子元素:{contextInfo.childrenCount}");
                    info.Append($" | 索引:{contextInfo.indexInParent}");
                    
                    if (rect.HasValue) {
                        info.Append($" | 位置:({rect.Value.X},{rect.Value.Y}) {rect.Value.Width}x{rect.Value.Height}");
                    }
                    
                    // 检查特殊接口支持
                    var interfaces = new List<string>();
                    if (contextInfo.accessibleAction != 0) interfaces.Add("Action");
                    if (contextInfo.accessibleSelection != 0) interfaces.Add("Selection");
                    if (contextInfo.accessibleText != 0) interfaces.Add("Text");
                    if (contextInfo.accessibleComponent != 0) interfaces.Add("Component");
                    
                    if (interfaces.Count > 0) {
                        info.Append($" | 接口:{string.Join(",", interfaces)}");
                    }
                    
                } catch (Exception ex) {
                    info.Append($" | 获取详细信息失败: {ex.Message}");
                }
            } else if (element is AccessibleWindow window) {
                info.Append($" | 类型:窗口 | 句柄:{window.Hwnd}");
            }
            
        } catch (Exception ex) {
            info.Append($" | 获取信息失败: {ex.Message}");
        }
        
        return info.ToString();
    }
    
    /// <summary>
    /// 写入日志
    /// </summary>
    static void WriteLog(string message) {
        logWriter?.WriteLine(message);
        logWriter?.Flush();
    }
} 