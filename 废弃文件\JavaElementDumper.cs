﻿using System;
using System.IO;
using System.Text;
using WindowsAccessBridgeInterop;

/// <summary>
/// 简单的Java元素遍历工具 - 命令行版本
/// 用于验证Access Bridge的基本功能和遍历能力
/// </summary>
class JavaElementDumper {
    private static AccessBridge accessBridge;
    private static StreamWriter logWriter;
    private static int elementCount = 0;
    
    static void Main(string[] args) {
        Console.WriteLine("=== Java 可访问元素遍历工具 ===");
        Console.WriteLine("正在初始化 Access Bridge...");
        
        try {
            // 初始化Access Bridge
            accessBridge = new AccessBridge();
            
            // 创建输出文件
            string outputFile = "java_elements_" + DateTime.Now.ToString("yyyyMMdd_HHmmss") + ".txt";
            logWriter = new StreamWriter(outputFile, false, Encoding.UTF8);
            
            Console.WriteLine($"输出文件: {outputFile}");
            Console.WriteLine("开始遍历Java应用程序...\n");
            
            WriteLog("=== Java 可访问元素完整遍历报告 ===");
            WriteLog($"时间: {DateTime.Now}");
            WriteLog("");
            
            // 获取所有Java应用程序
            var jvms = accessBridge.EnumJvms();
            WriteLog($"发现 {jvms.Count} 个Java虚拟机");
            Console.WriteLine($"发现 {jvms.Count} 个Java虚拟机");
            
            if (jvms.Count == 0) {
                WriteLog("没有发现运行中的Java应用程序");
                Console.WriteLine("没有发现运行中的Java应用程序");
                Console.WriteLine("请确保:");
                Console.WriteLine("1. 有Java应用程序正在运行");
                Console.WriteLine("2. Java Access Bridge已正确安装");
                Console.WriteLine("3. 应用程序启用了可访问性支持");
                return;
            }
            
            // 遍历每个JVM
            for (int jvmIndex = 0; jvmIndex < jvms.Count; jvmIndex++) {
                var jvm = jvms[jvmIndex];
                WriteLog($"\n=== JVM {jvmIndex + 1}: {jvm.GetTitle()} ===");
                Console.WriteLine($"\n处理 JVM {jvmIndex + 1}: {jvm.GetTitle()}");
                
                // 遍历JVM中的所有窗口
                for (int winIndex = 0; winIndex < jvm.Windows.Count; winIndex++) {
                    var window = jvm.Windows[winIndex];
                    WriteLog($"\n--- 窗口 {winIndex + 1}: {window.GetTitle()} ---");
                    Console.WriteLine($"  窗口 {winIndex + 1}: {window.GetTitle()}");
                    
                    // 深度遍历窗口中的所有元素
                    TraverseElement(window, 0);
                }
            }
            
            WriteLog($"\n=== 遍历完成 ===");
            WriteLog($"总共发现 {elementCount} 个可访问元素");
            Console.WriteLine($"\n遍历完成！总共发现 {elementCount} 个可访问元素");
            Console.WriteLine($"详细信息已保存到: {outputFile}");
            
        } catch (Exception ex) {
            var errorMsg = $"错误: {ex.Message}";
            WriteLog(errorMsg);
            Console.WriteLine(errorMsg);
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
        } finally {
            logWriter?.Close();
            accessBridge?.Dispose();
        }
        
        Console.WriteLine("\n按任意键退出...");
        Console.ReadKey();
    }
    
    /// <summary>
    /// 递归遍历元素及其所有子元素
    /// </summary>
    static void TraverseElement(AccessibleNode element, int depth) {
        try {
            elementCount++;
            string indent = new string(' ', depth * 2);
            
            // 获取元素基本信息
            string title = element.GetTitle() ?? "无标题";
            string info = $"{indent}[{elementCount}] {title}";
            
            // 如果是AccessibleContextNode，获取更详细信息
            if (element is AccessibleContextNode contextNode) {
                try {
                    var contextInfo = contextNode.GetInfo();
                    var rect = contextNode.GetScreenRectangle();
                    
                    var details = new StringBuilder();
                    details.Append($" | 角色:{contextInfo.role ?? "无"}");
                    details.Append($" | 状态:{contextInfo.states_en_US ?? "无"}");
                    details.Append($" | 子元素:{contextInfo.childrenCount}");
                    
                    if (rect.HasValue) {
                        details.Append($" | 位置:({rect.Value.X},{rect.Value.Y}) {rect.Value.Width}x{rect.Value.Height}");
                    }
                    
                    info += details.ToString();
                } catch (Exception ex) {
                    info += $" | 获取详细信息失败: {ex.Message}";
                }
            }
            
            WriteLog(info);
            
            // 显示进度
            if (elementCount % 50 == 0) {
                Console.WriteLine($"  已处理 {elementCount} 个元素...");
            }
            
            // 遍历子元素
            try {
                var children = element.GetChildren();
                foreach (var child in children) {
                    if (child != null) {
                        TraverseElement(child, depth + 1);
                    }
                }
            } catch (Exception ex) {
                WriteLog($"{indent}  获取子元素失败: {ex.Message}");
            }
            
        } catch (Exception ex) {
            WriteLog($"处理元素时出错 (深度 {depth}): {ex.Message}");
        }
    }
    
    /// <summary>
    /// 写入日志
    /// </summary>
    static void WriteLog(string message) {
        logWriter?.WriteLine(message);
        logWriter?.Flush();
    }
} 