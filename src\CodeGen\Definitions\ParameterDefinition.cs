﻿// Copyright 2016 Google Inc. All Rights Reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
//     http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using System.Runtime.InteropServices;
using System.Text;

namespace CodeGen.Definitions {
  public class ParameterDefinition {
    public string Name { get; set; }
    public MarshalAsAttribute MarshalAs { get; set; }
    public TypeReference Type { get; set; }
    public bool IsOut { get; set; }
    public bool IsRef { get; set; }
    public bool IsOutAttribute { get; set; }

    public override string ToString() {
      var sb = new StringBuilder();
      if (IsOutAttribute) {
        sb.Append("[Out]");
      }

      if (IsOut) {
        sb.Append("out ");
      } else if (IsRef) {
        sb.Append("ref ");
      }
      sb.Append(Type);
      sb.Append(" ");
      sb.Append(Name);
      return sb.ToString();
    }
  }
}