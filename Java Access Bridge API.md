The Java Access Bridge API enables you to develop assistive technology applications for the Microsoft Windows operating system that work with Java applications. It contains native methods that enable you to view and manipulate information about GUI elements in a Java application, which is forwarded to your assistive technology application through Java Access Bridge.

The following topics are covered:

    API Files

    API Changes from 2.0.1

    API Calls

    API Data Structures

    API Callbacks

API Files

The Java Access Bridge API can be found in three files: AccessBridgeCalls.h, AccessBridgePackages.h, and AccessBridgeCallbacks.h. These files correspond to the Java Access Bridge API calls, the API data structures, and the API callbacks, respectively.
API Changes from 2.0.1

Java Access Bridge 2.0.2 includes a new set of APIs in order to support 64-bit systems. However, the changes to the API are specific to parameters passed in API calls; there are no name changes in the API.

Assistive Technologies must recompile their applications with the new header files provided in the source.

Objects of type jobject are now passed as jlong. The typedef JOBJECT64 is either a jobject or jlong depending on whether ACCESSBRIDGE_ARCH_LEGACY is defined. See the AccessBridgePackages.h header file.

For usage examples, see the JavaFerret.cpp example:

void HandlePropertyChildChange(
  long vmID, PropertyChangeEvent event,
  AccessibleContext ac, JOBJECT64 oldChild, JOBJECT64 newChild);
 
void HandlePropertyActiveDescendentChange(
  long vmID, PropertyChangeEvent event, AccessibleContext ac,
  JOBJECT64 oldActiveDescendent, JOBJECT64 newActiveDescendent);

API Calls

The Java Access Bridge API calls are contained in AccessBridgeCalls.h and to use them, you must also compile the file AccessBridgeCalls.c, which acts as the interface between your application and WindowsAccessBridge.dll.
Initialization/Shutdown Functions

These two functions start and shut down Java Access Bridge.

    BOOL initializeAccessBridge();

    Starts Java Access Bridge. You cannot use any part of the Java Access Bridge API until you call this function.

    BOOL shutdownAccessBridge();

    Shuts down Java Access Bridge. It is important to call this function when your application is finished using Java Access Bridge (before your application exists) so that Java Access Bridge can properly perform memory cleanup.

    Note:
    Calling the function shutdownAccessBridge is not a substitute for releasing any data structures that are maintained by the JVM; do this by calling the function ReleaseJavaObject.

Gateway Functions

You typically call these functions before calling any other Java Access Bridge API function:

    BOOL IsJavaWindow(HWND window);

    Checks to see if the given window implements the Java Accessibility API.

    BOOL GetAccessibleContextFromHWND(HWND target, long *vmID, AccessibleContext *ac);

    Gets the AccessibleContext and vmID values for the given window. Many Java Access Bridge functions require the AccessibleContext and vmID values.

Event Handling Functions

These take a function pointer to the function that will handle the event type. When you no longer are interested in receiving those types of events, call the function again, passing in the NULL value. Find prototypes for the function pointers you need pass into these functions in the file AccessBridgeCallbacks.h. See the section "API Callbacks" for more information about these prototypes.

    void SetFocusGained(AccessBridge_FocusGainedFP fp);

    void SetFocusLost(AccessBridge_FocusLostFP fp);

General Functions

In order to determine the functionality available, you can get version information from the GetVersionInfo call.

    void ReleaseJavaObject(long vmID, Java_Object object);

    Release the memory used by the Java object object, where object is an object returned to you by Java Access Bridge. Java Access Bridge automatically maintains a reference to all Java objects that it returns to you in the JVM so they are not garbage collected. To prevent memory leaks, you must call ReleaseJavaObject on all Java objects returned to you by Java Access Bridge once you are finished with them. See JavaFerret.c for an illustration of how to do this.

    BOOL GetVersionInfo(long vmID, AccessBridgeVersionInfo *info);

    Gets the version information of the instance of Java Access Bridge instance your application is using. You can use this information to determine the available functionality of your version of Java Access Bridge.

    Note:
    In order to determine the version of the JVM, you need to pass in a valid vmID; otherwise all that is returned is the version of the WindowsAccessBridge.DLL file to which your application is connected.

Accessible Context Functions

These functions provide the core of the Java Accessibility API that is exposed by Java Access Bridge.

The functions GetAccessibleContextAt and GetAccessibleContextWithFocus retrieve an AccessibleContext object, which is a magic cookie (really a Java Object reference) to an Accessible object and a JVM cookie. You use these two cookies to reference objects through Java Access Bridge. Most Java Access Bridge API functions require that you pass in these two parameters.

Note:
AccessibleContext objects are 64-bit references under 64-bit inter-process communication (which uses the DLL and JAR files with -32 and -64 in their file names), while AccessibleContext objects are 32-bit references under 32-bit inter-process communication (which uses the DLL and JAR files without -32 and -64 in their file names). Consequently, you need to recompile your Assistive Technology applications if you are using 64-bit inter-process communication.

The function GetAccessibleContextInfo returns detailed information about an AccessibleContext object belonging to the JVM. In order to improve performance, the various distinct methods in the Java Accessibility API are collected together into a few routines in the Java Access Bridge API and returned in struct values. These struct values are defined in the file AccessBridgePackages.h and are described in the section "API Callbacks".

The functions GetAccessibleChildFromContext and GetAccessibleParentFromContext enable you to walk the GUI component hierarchy, retrieving the nth child, or the parent, of a particular GUI object.

    BOOL GetAccessibleContextAt(long vmID, AccessibleContext acParent, jint x, jint y, AccessibleContext *ac)

    Retrieves an AccessibleContext object of the window or object that is under the mouse pointer.

    BOOL GetAccessibleContextWithFocus(HWND window, long *vmID, AccessibleContext *ac);

    Retrieves an AccessibleContext object of the window or object that has the focus.

    BOOL GetAccessibleContextInfo(long vmID, AccessibleContext ac, AccessibleContextInfo *info);

    Retrieves an AccessibleContextInfo object of the AccessibleContext object ac.

    AccessibleContext GetAccessibleChildFromContext(long vmID, AccessibleContext ac, jint index);

    Returns an AccessibleContext object that represents the nth child of the object ac, where n is specified by the value index.

    AccessibleContext GetAccessibleParentFromContext(long vmID, AccessibleContext ac);

    Returns an AccessibleContext object that represents the parent of object ac.

Accessible Text Functions

These functions get AccessibleText information provided by the Java Accessibility API, broken down into seven chunks for efficiency. An AccessibleContext has AccessibleText information contained within it if the flag accessibleText in the AccessibleContextInfo data structure is set to TRUE. The struct values used in these functions are defined in the file AccessBridgePackages.h and are described in the section "API Callbacks".

    BOOL GetAccessibleTextInfo(long vmID, AccessibleText at, AccessibleTextInfo *textInfo, jint x, jint y);

    BOOL GetAccessibleTextItems(long vmID, AccessibleText at, AccessibleTextItemsInfo *textItems, jint index);

    BOOL GetAccessibleTextSelectionInfo(long vmID, AccessibleText at, AccessibleTextSelectionInfo *textSelection);

    char *GetAccessibleTextAttributes(long vmID, AccessibleText at, jint index, AccessibleTextAttributesInfo *attributes);

    BOOL GetAccessibleTextRect(long vmID, AccessibleText at, AccessibleTextRectInfo *rectInfo, jint index);

    BOOL GetAccessibleTextRange(long vmID, AccessibleText at, jint start, jint end, wchar_t *text, short len);

    BOOL GetAccessibleTextLineBounds(long vmID, AccessibleText at, jint index, jint *startIndex, jint *endIndex);

Accessible Value Functions

These functions get AccessibleValue information provided by the Java Accessibility API. An AccessibleContext object has AccessibleValue information contained within it if the flag accessibleValue in the AccessibleContextInfo data structure is set to TRUE. The values returned are strings (char *value) because there is no way to tell in advance if the value is an integer, a floating point value, or some other object that subclasses the Java language construct java.lang.Number.

    BOOL GetCurrentAccessibleValueFromContext(long vmID, AccessibleValue av, wchar_t *value, short len);

    BOOL GetMaximumAccessibleValueFromContext(long vmID, AccessibleValue av, wchar_ *value, short len);

    BOOL GetMinimumAccessibleValueFromContext(long vmID, AccessibleValue av, wchar_ *value, short len);

Accessible Selection Functions

These functions get and manipulate AccessibleSelection information provided by the Java Accessibility API. An AccessibleContext has AccessibleSelection information contained within it if the flag accessibleSelection in the AccessibleContextInfo data structure is set to TRUE. The AccessibleSelection support is the first place where the user interface can be manipulated, as opposed to being queries, through adding and removing items from a selection. Some of the functions use an index that is in child coordinates, while other use selection coordinates. For example, add to remove from a selection by passing child indicies (for example, add the fourth child to the selection). On the other hand, enumerating the selected children is done in selection coordinates (for example, get the AccessibleContext of the first object selected).

    void AddAccessibleSelectionFromContext(long vmID, AccessibleSelection as, int i);

    void ClearAccessibleSelectionFromContext(long vmID, AccessibleSelection as);

    jobject GetAccessibleSelectionFromContext(long vmID, AccessibleSelection as, int i);

    int GetAccessibleSelectionCountFromContext(long vmID, AccessibleSelection as);

    BOOL IsAccessibleChildSelectedFromContext(long vmID, AccessibleSelection as, int i);

    void RemoveAccessibleSelectionFromContext(long vmID, AccessibleSelection as, int i);

    void SelectAllAccessibleSelectionFromContext(long vmID, AccessibleSelection as);

API Data Structures

The Java Access Bridge API data structures are contained in the file AccessBridgePackages.h. There are data structures in this file that you do not need (and can ignore); they are used as part of the inter-process communication mechanism of the two Java Access Bridge DLLs. The data structures of importance are as follows:

#define MAX_STRING_SIZE     1024
#define SHORT_STRING_SIZE    256
 
struct AccessBridgeVersionInfo {
 wchar_t VMversion[SHORT_STRING_SIZE];              // version of the Java VM
 wchar_t bridgeJavaClassVersion[SHORT_STRING_SIZE]; // version of the
                                                    //   AccessBridge.class
 wchar_t bridgeJavaDLLVersion[SHORT_STRING_SIZE];   // version of 
                                                    //   JavaAccessBridge.dll
 wchar_t bridgeWinDLLVersion[SHORT_STRING_SIZE];    // version of 
                                                    //   WindowsAccessBridge.dll
};
 
struct AccessibleContextInfo {
 wchar_ name[MAX_STRING_SIZE];        // the AccessibleName of the object
 wchar_ description[MAX_STRING_SIZE]; // the AccessibleDescription of the object
 wchar_ role[SHORT_STRING_SIZE];      // localized AccesibleRole string
 wchar_ states[SHORT_STRING_SIZE];    // localized AccesibleStateSet string
                                      //   (comma separated)
 jint indexInParent                   // index of object in parent
 jint childrenCount                   // # of children, if any
 jint x;                              // screen x-axis co-ordinate in pixels
 jint y;                              // screen y-axis co-ordinate in pixels
 jint width;                          // pixel width of object
 jint height;                         // pixel height of object
 BOOL accessibleComponent;            // flags for various additional
 BOOL accessibleAction;               // Java Accessibility interfaces
 BOOL accessibleSelection;            // FALSE if this object doesn't
 BOOL accessibleText;                 // implement the additional interface
 BOOL accessibleInterfaces;           // new bitfield containing additional
                                      //   interface flags
};
 
struct AccessibleTextInfo {
 jint charCount;       // # of characters in this text object
 jint caretIndex;      // index of caret
 jint indexAtPoint;    // index at the passsed in point
};

struct AccessibleTextItemsInfo {
 wchar_t letter;
 wchar_t word[SHORT_STRING_SIZE];
 wchar_t sentence[MAX_STRING_SIZE];
};
 
struct AccessibleTextSelectionInfo {
 jint selectionStartIndex;
 jint selectionEndIndex;
 wchar_t selectedText[MAX_STRING_SIZE];
};
 
struct AccessibleTextRectInfo {
 jint x;          // bounding recttangle of char at index, x-axis co-ordinate
 jint y;          // y-axis co-ordinate
 jint width;      // bounding rectangle width
 jint height;     // bounding rectangle height
};
 
struct AccessibleTextAttributesInfo {
 BOOL bold;
 BOOL italic;
 BOOL underline;
 BOOL strikethrough;
 BOOL superscript;
 BOOL subscript;
 wchar_t backgroundColor[SHORT_STRING_SIZE];
 wchar_t foregroundColor[SHORT_STRING_SIZE];
 wchar_t fontFamily[SHORT_STRING_SIZE];
 jint fontSize;
 jint alignment;
 jint bidiLevel;
 jfloat firstLineIndent;
 jfloat leftIndent;
 jfloat rightIndent;
 jfloat lineSpacing;
 jfloat spaceAbove;
 jfloat spaceBelow;
 wchar_t fullAttributesString[MAX_STRING_SIZE];
};

API Callbacks

The Java Access Bridge API callbacks are contained in the file AccessBridgeCallbacks.h. Your event handling functions must match these prototypes.

Note:
All of the Java Access Bridge event handlers are defined and used in the Java Ferret example.

You must call the function ReleaseJavaObject on every jobject returned through these event handlers once you are finished with them to prevent memory leaks in the JVM.

Here, JOBJECT64 is defined as jlong on 64-bit systems and jobject on legacy versions of Java Access Bridge. For definitions, see the section ACCESSBRIDGE_ARCH_LEGACY in the AccessBridgePackages.h header file.

If using legacy APIs, define ACCESSBRIDGE_ARCH_LEGACY. See the AccessBridgePackages.h header file.

    typedef void (*AccessBridge_FocusGainedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_FocusLostFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_CaretUpdateFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MouseClickedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MouseEnteredFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MouseExitedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MousePressedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MouseReleasedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MenuCanceledFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MenuDeselectedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_MenuSelectedFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PopupMenuCanceledFP) (long vmID JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PopupMenuWillBecomeInvisibleFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PopupMenuWillBecomeVisibleFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PropertyNameChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source, wchar_t *oldName, wchar_t *newName);

    typedef void (*AccessBridge_PropertyDescriptionChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source, wchar_t *oldDescription, wchar_t *newDescription);

    typedef void (*AccessBridge_PropertyStateChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source, wchar_t *oldState, wchar_t *newState);

    typedef void (*AccessBridge_PropertyValueChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source, wchar_t *oldValue, wchar_t *newValue);

    typedef void (*AccessBridge_PropertySelectionChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PropertyTextChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PropertyCaretChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source, int oldPosition, int newPosition);

    typedef void (*AccessBridge_PropertyVisibleDataChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source);

    typedef void (*AccessBridge_PropertyChildChangeFP) (long vmID, JOBJECT64 event, JOBJECT64 source, JOBJECT64 oldChild, JOBJECT64 newChild);

    typedef void (*AccessBridge_PropertyActiveDescendentChangeFP) (long vmID, jobject event, JOBJECT64 source, JOBJECT64 oldActiveDescendent, JOBJECT64 newActiveDescendent);

