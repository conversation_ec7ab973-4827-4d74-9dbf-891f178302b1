@echo off
chcp 65001 >nul
echo ===============================================
echo 启用NC应用程序的Java Access Bridge支持
echo ===============================================

echo.
echo 🔧 当前状态检查：
echo 1. NC应用程序正在运行 ✅
echo 2. 需要启用Access Bridge支持
echo.

echo 📋 解决方案步骤：

echo.
echo 步骤1: 检查Java Access Bridge安装
echo ----------------------------------------
if exist "C:\Windows\System32\WindowsAccessBridge-64.dll" (
    echo ✅ 找到 WindowsAccessBridge-64.dll
) else (
    echo ❌ 未找到 WindowsAccessBridge-64.dll
)

if exist "C:\Windows\System32\JAWTAccessBridge-64.dll" (
    echo ✅ 找到 JAWTAccessBridge-64.dll
) else (
    echo ❌ 未找到 JAWTAccessBridge-64.dll
)

echo.
echo 步骤2: 复制Access Bridge文件到系统目录
echo ----------------------------------------
echo 正在复制Access Bridge文件...

copy "accessbridge2_0_2\WindowsAccessBridge-64.dll" "C:\Windows\System32\" >nul 2>&1
copy "accessbridge2_0_2\JAWTAccessBridge-64.dll" "C:\Windows\System32\" >nul 2>&1
copy "accessbridge2_0_2\JavaAccessBridge-64.dll" "C:\Windows\System32\" >nul 2>&1

echo ✅ Access Bridge文件复制完成

echo.
echo 步骤3: 设置Java系统属性
echo ----------------------------------------
echo 创建启动NC的脚本，启用Access Bridge...

echo @echo off > start_nc_with_bridge.bat
echo echo 启动NC应用程序并启用Access Bridge支持... >> start_nc_with_bridge.bat
echo set JAVA_TOOL_OPTIONS=-Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge >> start_nc_with_bridge.bat
echo echo Java Access Bridge已启用 >> start_nc_with_bridge.bat
echo echo 请重新启动您的NC应用程序 >> start_nc_with_bridge.bat
echo pause >> start_nc_with_bridge.bat

echo ✅ 启动脚本创建完成

echo.
echo 步骤4: 测试现有的JavaFerret工具
echo ----------------------------------------
echo 让我们先用JavaFerret测试一下...

if exist "accessbridge2_0_2\JavaFerret.exe" (
    echo ✅ 找到JavaFerret，正在启动...
    echo.
    echo 💡 请在JavaFerret中查看是否能看到NC应用程序
    echo    如果能看到，说明Access Bridge工作正常
    echo.
    start "" "accessbridge2_0_2\JavaFerret.exe"
    
    echo.
    echo ⏳ 请检查JavaFerret窗口...
    echo 1. 如果能看到NC应用程序 → Access Bridge工作正常
    echo 2. 如果看不到NC应用程序 → 需要重启NC并启用Access Bridge
    echo.
    pause
    
    echo.
    echo 现在测试我们的NC遍历工具...
    echo.
    NCTraverser.exe NC
    
) else (
    echo ❌ 找不到JavaFerret.exe
    echo 直接测试我们的工具...
    echo.
    NCTraverser.exe NC
)

echo.
echo ===============================================
echo 🔧 如果仍然无法检测到NC应用程序
echo ===============================================
echo.
echo 方案1: 重启NC应用程序并启用Access Bridge
echo ----------------------------------------
echo 1. 关闭当前的NC应用程序
echo 2. 运行: start_nc_with_bridge.bat
echo 3. 重新启动NC应用程序
echo 4. 再次运行遍历工具
echo.

echo 方案2: 手动设置Java参数
echo ----------------------------------------
echo 在NC应用程序启动时添加以下参数：
echo -Djavax.accessibility.assistive_technologies=com.sun.java.accessibility.AccessBridge
echo.

echo 方案3: 检查NC应用程序类型
echo ----------------------------------------
echo 1. NC可能使用了内嵌的JRE
echo 2. 可能需要在NC的Java配置中启用Access Bridge
echo 3. 检查NC的启动配置文件
echo.

echo 方案4: 使用管理员权限
echo ----------------------------------------
echo 1. 以管理员身份运行此脚本
echo 2. 以管理员身份运行NC应用程序
echo 3. 以管理员身份运行遍历工具
echo.

pause
