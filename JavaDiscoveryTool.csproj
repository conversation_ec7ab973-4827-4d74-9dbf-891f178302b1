<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <UseWindowsForms>true</UseWindowsForms>
    <AssemblyName>JavaDiscoveryTool</AssemblyName>
    <RootNamespace>JavaDiscoveryTool</RootNamespace>
    <AssemblyTitle>Java程序发现工具</AssemblyTitle>
    <AssemblyDescription>基于Java Access Bridge API的Java程序发现和遍历工具</AssemblyDescription>
    <AssemblyCompany>Java Discovery Tool</AssemblyCompany>
    <AssemblyProduct>Java Discovery Tool</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2025</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>

  <ItemGroup>
    <Reference Include="WindowsAccessBridgeInterop">
      <HintPath>src\WindowsAccessBridgeInterop\bin\Release\WindowsAccessBridgeInterop.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Drawing" />
  </ItemGroup>

</Project>
