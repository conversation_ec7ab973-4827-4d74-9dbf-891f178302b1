@echo off
echo ===============================================
echo Fix NC Access Bridge Connection
echo ===============================================

echo.
echo Step 1: Test with existing JavaFerret
echo ----------------------------------------
if exist "accessbridge2_0_2\JavaFerret.exe" (
    echo Starting JavaFerret to test Access Bridge...
    echo Please check if you can see NC application in JavaFerret
    echo.
    start "" "accessbridge2_0_2\JavaFerret.exe"
    
    echo.
    echo Press any key after checking JavaFerret...
    pause
) else (
    echo JavaFerret not found, skipping...
)

echo.
echo Step 2: Copy Access Bridge files to system
echo ----------------------------------------
if exist "accessbridge2_0_2\WindowsAccessBridge-64.dll" (
    echo Copying WindowsAccessBridge-64.dll...
    copy "accessbridge2_0_2\WindowsAccessBridge-64.dll" "C:\Windows\System32\" >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo OK: WindowsAccessBridge-64.dll copied
    ) else (
        echo WARN: Failed to copy WindowsAccessBridge-64.dll
    )
) else (
    echo WARN: WindowsAccessBridge-64.dll not found
)

if exist "accessbridge2_0_2\JAWTAccessBridge-64.dll" (
    echo Copying JAWTAccessBridge-64.dll...
    copy "accessbridge2_0_2\JAWTAccessBridge-64.dll" "C:\Windows\System32\" >nul 2>&1
    if %ERRORLEVEL% equ 0 (
        echo OK: JAWTAccessBridge-64.dll copied
    ) else (
        echo WARN: Failed to copy JAWTAccessBridge-64.dll
    )
) else (
    echo WARN: JAWTAccessBridge-64.dll not found
)

echo.
echo Step 3: Test our NC Traverser
echo ----------------------------------------
echo Testing NC Traverser with NC filter...
echo.
NCTraverser.exe NC

echo.
echo Step 4: Test without filter
echo ----------------------------------------
echo Testing NC Traverser without filter...
echo.
NCTraverser.exe

echo.
echo ===============================================
echo Results Analysis
echo ===============================================

echo.
echo Checking log files...
for %%f in (NC_Elements_Complete_*.log) do (
    echo Found log: %%f
    echo File size: 
    dir "%%f" | find "%%f"
    echo.
    echo First 5 lines:
    type "%%f" | more /E +1 | head -5 2>nul || (
        powershell -Command "Get-Content '%%f' | Select-Object -First 5"
    )
    echo.
)

echo.
echo ===============================================
echo Troubleshooting Steps
echo ===============================================
echo.
echo If still showing "0 Java VMs found":
echo.
echo 1. RESTART NC APPLICATION
echo    - Close NC completely
echo    - Restart NC application
echo    - Run this script again
echo.
echo 2. CHECK NC JAVA PROCESS
echo    - Open Task Manager
echo    - Look for java.exe or javaw.exe processes
echo    - Note the command line parameters
echo.
echo 3. ENABLE ACCESS BRIDGE IN NC
echo    - NC might need specific configuration
echo    - Check NC documentation for accessibility settings
echo.
echo 4. TRY DIFFERENT APPROACH
echo    - Use JavaMonkey: accessbridge2_0_2\JavaMonkey.exe
echo    - Use original AccessBridgeExplorer
echo.

pause
