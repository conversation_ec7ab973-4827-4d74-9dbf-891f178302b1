@echo off
echo =================================================
echo 修复 Java Access Bridge 配置问题
echo =================================================
echo.

set JAVA8_JRE=C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\jre
set PROPS_FILE=%JAVA8_JRE%\lib\accessibility.properties

echo 🔍 检查当前配置...
if exist "%PROPS_FILE%" (
    echo ✓ 找到配置文件: %PROPS_FILE%
    echo.
    echo 当前内容:
    type "%PROPS_FILE%"
    echo.
) else (
    echo ✗ 配置文件不存在: %PROPS_FILE%
    goto :error
)

echo 📋 备份原始配置文件...
copy "%PROPS_FILE%" "%PROPS_FILE%.backup" >nul
if errorlevel 1 (
    echo ✗ 备份失败
    goto :error
)
echo ✓ 已备份到: %PROPS_FILE%.backup
echo.

echo 🔧 修复配置文件...
if exist "accessbridge2_0_2\accessibility.properties" (
    echo ✓ 使用官方配置文件
    copy "accessbridge2_0_2\accessibility.properties" "%PROPS_FILE%" >nul
    if errorlevel 1 (
        echo ✗ 复制官方配置文件失败
        goto :error
    )
) else (
    echo ⚠️ 官方配置文件不存在，手动修复...
    echo # > "%PROPS_FILE%"
    echo # Load the Java Access Bridge class into the JVM >> "%PROPS_FILE%"
    echo # >> "%PROPS_FILE%"
    echo assistive_technologies=com.sun.java.accessibility.AccessBridge >> "%PROPS_FILE%"
    echo screen_magnifier_present=true >> "%PROPS_FILE%"
)

echo ✓ 配置文件已修复
echo.

echo 📄 修复后的配置内容:
type "%PROPS_FILE%"
echo.

echo 🔄 重新启用 Access Bridge...
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -disable >nul 2>&1
"C:\Program Files\Eclipse Adoptium\jdk-8.0.452.9-hotspot\bin\jabswitch.exe" -enable >nul 2>&1
echo ✓ Access Bridge 已重新启用
echo.

echo 🧪 启动测试工具...
if exist "accessbridge2_0_2\JavaFerret-64.exe" (
    echo 启动 JavaFerret-64 测试工具...
    start "" "accessbridge2_0_2\JavaFerret-64.exe"
) else (
    echo ⚠️ 测试工具不可用
)
echo.

echo =================================================
echo ✅ 修复完成！
echo.
echo 📋 下一步操作：
echo 1. 重启用友NC系统
echo 2. 运行 AccessBridgeExplorer.exe
echo 3. 按F5刷新，检查是否能看到完整的组件树
echo 4. 查看之前空的Panel是否现在有内容
echo.
echo 💡 如果问题仍然存在：
echo 1. 确保用友NC使用正确的Java启动参数
echo 2. 检查用友NC是否使用了不同的Java版本
echo 3. 尝试运行JavaFerret-64测试其他Java应用
echo =================================================
goto :end

:error
echo.
echo ❌ 修复过程中出现错误！
echo 请检查：
echo 1. 是否以管理员权限运行此脚本
echo 2. Java路径是否正确
echo 3. 文件是否被占用
echo.

:end
pause 