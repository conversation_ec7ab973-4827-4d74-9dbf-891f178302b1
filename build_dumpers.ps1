Write-Host "==================================" -ForegroundColor Green
Write-Host "编译Java元素遍历工具" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host

# 检查依赖文件
$dllPath = "bin\Debug\WindowsAccessBridgeInterop.dll"
if (-not (Test-Path $dllPath)) {
    Write-Host "错误: 找不到 $dllPath" -ForegroundColor Red
    Write-Host "请先运行 build.bat 编译主项目" -ForegroundColor Yellow
    Read-Host "按Enter键退出"
    exit 1
}

Write-Host "✓ 依赖库检查通过" -ForegroundColor Green
Write-Host

# 使用PowerShell Add-Type来编译
try {
    Write-Host "正在编译快速测试工具..." -ForegroundColor Cyan
    
    $quickTestCode = Get-Content "JavaAccessBridgeQuickTest.cs" -Raw
    Add-Type -TypeDefinition $quickTestCode -ReferencedAssemblies @(
        "System.dll",
        "System.Core.dll", 
        "bin\Debug\WindowsAccessBridgeInterop.dll"
    ) -OutputAssembly "JavaAccessBridgeQuickTest.exe" -OutputType ConsoleApplication
    Write-Host "✓ JavaAccessBridgeQuickTest.exe 编译成功" -ForegroundColor Green
    
    Write-Host "正在编译基础版遍历工具..." -ForegroundColor Cyan
    
    $basicCode = Get-Content "JavaElementDumper.cs" -Raw
    Add-Type -TypeDefinition $basicCode -ReferencedAssemblies @(
        "System.dll",
        "System.Core.dll",
        "bin\Debug\WindowsAccessBridgeInterop.dll"
    ) -OutputAssembly "JavaElementDumper.exe" -OutputType ConsoleApplication
    Write-Host "✓ JavaElementDumper.exe 编译成功" -ForegroundColor Green
    
    Write-Host "正在编译增强版遍历工具..." -ForegroundColor Cyan
    
    $enhancedCode = Get-Content "JavaElementDumperEnhanced.cs" -Raw  
    Add-Type -TypeDefinition $enhancedCode -ReferencedAssemblies @(
        "System.dll",
        "System.Core.dll",
        "System.Drawing.dll",
        "bin\Debug\WindowsAccessBridgeInterop.dll"
    ) -OutputAssembly "JavaElementDumperEnhanced.exe" -OutputType ConsoleApplication
    Write-Host "✓ JavaElementDumperEnhanced.exe 编译成功" -ForegroundColor Green
    
}
catch {
    Write-Host "编译失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "尝试使用替代方法..." -ForegroundColor Yellow
    
    # 备用方法：直接复制源码并内联编译
    Write-Host "使用内联编译方式..." -ForegroundColor Cyan
    
    # 快速测试工具 - 简化版
    $simpleTestSource = @"
using System;
using WindowsAccessBridgeInterop;

public class SimpleTest {
    public static void Main() {
        Console.WriteLine("=== 简单Java Access Bridge测试 ===");
        try {
            var bridge = new AccessBridge();
            var jvms = bridge.EnumJvms();
            Console.WriteLine("发现 {0} 个Java虚拟机", jvms.Count);
            
            foreach (var jvm in jvms) {
                Console.WriteLine("JVM: {0}", jvm.GetTitle());
                Console.WriteLine("  窗口数: {0}", jvm.Windows.Count);
                
                foreach (var window in jvm.Windows) {
                    Console.WriteLine("    窗口: {0}", window.GetTitle());
                    try {
                        var info = window.GetInfo();
                        Console.WriteLine("      子元素: {0}", info.childrenCount);
                    } catch (Exception ex) {
                        Console.WriteLine("      获取信息失败: {0}", ex.Message);
                    }
                }
            }
            
            bridge.Dispose();
            Console.WriteLine("测试完成！");
        } catch (Exception ex) {
            Console.WriteLine("测试失败: {0}", ex.Message);
        }
        Console.ReadKey();
    }
}
"@

    Add-Type -TypeDefinition $simpleTestSource -ReferencedAssemblies @(
        "System.dll",
        "bin\Debug\WindowsAccessBridgeInterop.dll"
    ) -OutputAssembly "SimpleJavaTest.exe" -OutputType ConsoleApplication
    
    Write-Host "✓ SimpleJavaTest.exe 编译成功（简化版）" -ForegroundColor Green
}

Write-Host
Write-Host "==================================" -ForegroundColor Green
Write-Host "编译完成！" -ForegroundColor Green  
Write-Host "==================================" -ForegroundColor Green
Write-Host

Write-Host "生成的工具:" -ForegroundColor White
if (Test-Path "JavaAccessBridgeQuickTest.exe") {
    Write-Host "  ✓ JavaAccessBridgeQuickTest.exe" -ForegroundColor Green
}
if (Test-Path "JavaElementDumper.exe") {
    Write-Host "  ✓ JavaElementDumper.exe" -ForegroundColor Green
}
if (Test-Path "JavaElementDumperEnhanced.exe") {
    Write-Host "  ✓ JavaElementDumperEnhanced.exe" -ForegroundColor Green
}
if (Test-Path "SimpleJavaTest.exe") {
    Write-Host "  ✓ SimpleJavaTest.exe（简化版测试工具）" -ForegroundColor Green
}

Write-Host
Write-Host "使用建议:" -ForegroundColor Yellow
Write-Host "  1. 启动你的Java应用程序（如用友NC）" -ForegroundColor White
Write-Host "  2. 运行测试工具验证Access Bridge工作状态" -ForegroundColor White
Write-Host "  3. 运行遍历工具查看所有可访问元素" -ForegroundColor White
Write-Host

Read-Host "按Enter键退出" 