@echo off
echo ========================================
echo Access Bridge Explorer NC系统调试功能测试
echo ========================================
echo.

echo 1. 检查编译结果
if exist "bin\Debug\AccessBridgeExplorer.exe" (
    echo ✓ 编译成功 - AccessBridgeExplorer.exe 已生成
) else (
    echo ✗ 编译失败 - 找不到 AccessBridgeExplorer.exe
    pause
    exit /b 1
)

echo.
echo 2. 检查Java Access Bridge状态
jabswitch -list 2>nul
if %ERRORLEVEL% neq 0 (
    echo ⚠️ Java Access Bridge可能未正确安装
    echo 请运行: jabswitch -enable
) else (
    echo ✓ Java Access Bridge已安装
)

echo.
echo 3. 检查文件版本信息
if exist "bin\Debug\AccessBridgeExplorer.exe" (
    echo 文件大小: 
    dir "bin\Debug\AccessBridgeExplorer.exe" | findstr AccessBridgeExplorer.exe
    echo 修改时间: 
    dir "bin\Debug\AccessBridgeExplorer.exe" | findstr /C:"2025"
)

echo.
echo 4. 启动程序进行测试
echo 程序已在后台启动，请按照以下步骤进行测试：
echo.
echo 测试步骤：
echo 1) 确保用友NC系统正在运行
echo 2) 在Access Bridge Explorer中点击"刷新"按钮
echo 3) 查看是否能检测到Java应用程序窗口
echo 4) 点击工具栏中的"NC调试"按钮
echo 5) 查看底部Messages标签页中的调试信息
echo.
echo 预期结果：
echo - 应该能看到NC系统窗口检测信息
echo - 应该能看到Panel结构分析
echo - 应该能看到多种子组件获取方法的测试结果
echo - 应该能看到坐标网格搜索结果
echo - 应该能看到Access Bridge配置信息
echo.

echo 5. 功能验证清单
echo □ NC系统调试按钮是否显示在工具栏中？
echo □ 点击按钮后是否有调试信息输出？
echo □ 是否能检测到NC系统窗口？
echo □ 是否能分析Panel结构？
echo □ 是否能尝试多种子组件获取方法？
echo □ 是否能进行坐标网格搜索？
echo □ 是否能显示Access Bridge配置？
echo □ 是否有解决方案建议？
echo.

echo 6. 已知问题和解决方案
echo 问题1: 如果无法检测到Java应用程序
echo 解决: 运行 jabswitch -enable 启用Java Access Bridge
echo.
echo 问题2: 如果NC系统Panel显示有子组件但无法访问
echo 解决: 使用NC调试功能进行深度分析，查看具体原因
echo.
echo 问题3: 如果程序崩溃或无响应
echo 解决: 检查用友NC系统版本兼容性，查看错误日志
echo.

echo 测试完成后，请查看生成的调试报告并验证功能是否正常工作。
echo.
pause 