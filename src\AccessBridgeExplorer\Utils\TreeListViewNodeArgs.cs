// Copyright 2016 Google Inc. All Rights Reserved.
// 
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
// 
//     http://www.apache.org/licenses/LICENSE-2.0
// 
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

using System;

namespace AccessBridgeExplorer.Utils {
  public class NodeArgs<TNode> : EventArgs where TNode : class {
    private readonly TNode _node;

    public NodeArgs(TNode node) {
      _node = node;
    }

    public TNode Node {
      get { return _node; }
    }
  }

  public class NodeVisibilityChangedArg<TNode> : NodeArgs<TNode> where TNode : class {
    private readonly bool _visible;

    public NodeVisibilityChangedArg(TNode node, bool visible)
      : base(node) {
      _visible = visible;
    }

    public bool Visible {
      get { return _visible; }
    }
  }
}