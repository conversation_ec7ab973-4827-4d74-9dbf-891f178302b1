﻿@echo off
echo ==============================
echo 编译Java元素遍历工具
echo ==============================

echo.
echo 正在编译基础版本...
csc /target:exe /out:JavaElementDumper.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll JavaElementDumper.cs
if %ERRORLEVEL% neq 0 (
    echo 基础版本编译失败!
    pause
    exit /b 1
)

echo.
echo 正在编译增强版本...
csc /target:exe /out:JavaElementDumperEnhanced.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll JavaElementDumperEnhanced.cs
if %ERRORLEVEL% neq 0 (
    echo 增强版本编译失败!
    pause
    exit /b 1
)

echo.
echo 正在编译快速测试工具...
csc /target:exe /out:JavaAccessBridgeQuickTest.exe /reference:bin\Debug\WindowsAccessBridgeInterop.dll JavaAccessBridgeQuickTest.cs
if %ERRORLEVEL% neq 0 (
    echo 快速测试工具编译失败!
    pause
    exit /b 1
)

echo.
echo ==============================
echo 编译成功!
echo ==============================
echo.
echo 生成的工具:
echo   JavaAccessBridgeQuickTest.exe - 快速测试Access Bridge是否正常
echo   JavaElementDumper.exe - 基础版Java元素遍历工具
echo   JavaElementDumperEnhanced.exe - 增强版Java元素遍历工具
echo.
echo 使用方法:
echo   1. 启动你的Java应用程序 (如用友NC)
echo   2. 运行相应的工具
echo   3. 查看控制台输出和生成的文本文件
echo.
echo 建议使用顺序:
echo   1. 先运行 JavaAccessBridgeQuickTest.exe 验证环境
echo   2. 再运行 JavaElementDumper.exe 看基础遍历效果
echo   3. 最后运行 JavaElementDumperEnhanced.exe 对比增强效果
echo.
pause 