import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

public class TestJavaApp {
    public static void main(String[] args) {
        // 强制启用Java Access Bridge支持
        System.setProperty("javax.accessibility.assistive_technologies",
                "com.sun.java.accessibility.util.EventQueueMonitor," +
                        "com.sun.java.accessibility.util.SwingEventMonitor");

        // 确保Access Bridge被正确加载
        System.setProperty("java.awt.headless", "false");

        // 输出调试信息
        System.out.println("Starting Java Test Application with Access Bridge support");
        System.out.println("Assistive technologies: " +
                System.getProperty("javax.accessibility.assistive_technologies"));

        SwingUtilities.invokeLater(new Runnable() {
            public void run() {
                createAndShowGUI();
            }
        });
    }

    private static void createAndShowGUI() {
        try {
            // 设置系统外观
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Create main window with specific title for easy identification
        JFrame frame = new JFrame("用友NC57测试程序 - Access Bridge Test");
        frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        frame.setSize(800, 600);
        frame.setLocationRelativeTo(null);

        // 设置窗口可访问性属性
        frame.getAccessibleContext().setAccessibleName("用友NC57测试主窗口");
        frame.getAccessibleContext().setAccessibleDescription("用于测试Java Access Bridge的NC系统模拟窗口");

        // Create main panel with accessibility support
        JPanel mainPanel = new JPanel();
        mainPanel.setLayout(new BorderLayout());
        mainPanel.setName("NC主面板");
        mainPanel.getAccessibleContext().setAccessibleName("NC系统主面板");
        mainPanel.getAccessibleContext().setAccessibleDescription("模拟NC系统的主要界面面板");

        // Top panel with form fields (模拟NC的表单区域)
        JPanel topPanel = new JPanel(new GridBagLayout());
        topPanel.setName("NC表单区域");
        topPanel.getAccessibleContext().setAccessibleName("NC数据录入区域");
        topPanel.setBorder(BorderFactory.createTitledBorder("NC数据录入"));
        GridBagConstraints gbc = new GridBagConstraints();

        // Name field (模拟NC的输入字段)
        JLabel nameLabel = new JLabel("员工姓名:");
        nameLabel.getAccessibleContext().setAccessibleName("员工姓名标签");

        JTextField nameField = new JTextField(20);
        nameField.setName("员工姓名输入框");
        nameField.getAccessibleContext().setAccessibleName("员工姓名输入字段");
        nameField.getAccessibleContext().setAccessibleDescription("请输入员工的真实姓名");

        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.anchor = GridBagConstraints.WEST;
        gbc.insets = new Insets(5, 5, 5, 5);
        topPanel.add(nameLabel, gbc);
        gbc.gridx = 1;
        gbc.gridy = 0;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        topPanel.add(nameField, gbc);

        // Department field
        JLabel deptLabel = new JLabel("所属部门:");
        deptLabel.getAccessibleContext().setAccessibleName("部门选择标签");

        JComboBox<String> deptCombo = new JComboBox<>(new String[] { "财务部", "销售部", "人事部", "技术部" });
        deptCombo.setName("部门选择下拉框");
        deptCombo.getAccessibleContext().setAccessibleName("员工部门选择");
        deptCombo.getAccessibleContext().setAccessibleDescription("选择员工所属的部门");

        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.anchor = GridBagConstraints.WEST;
        topPanel.add(deptLabel, gbc);
        gbc.gridx = 1;
        gbc.gridy = 1;
        gbc.fill = GridBagConstraints.HORIZONTAL;
        topPanel.add(deptCombo, gbc);

        // Left panel (模拟NC的导航区域)
        JPanel leftPanel = new JPanel(new BorderLayout());
        leftPanel.setName("NC导航区域");
        leftPanel.getAccessibleContext().setAccessibleName("NC系统导航面板");
        leftPanel.setBorder(BorderFactory.createTitledBorder("系统导航"));

        JPanel navPanel = new JPanel();
        navPanel.setLayout(new BoxLayout(navPanel, BoxLayout.Y_AXIS));
        navPanel.setName("导航按钮面板");

        String[] navItems = { "基础档案", "财务管理", "供应链", "人力资源", "系统管理" };
        for (int i = 0; i < navItems.length; i++) {
            JButton navBtn = new JButton(navItems[i]);
            navBtn.setName("导航按钮_" + navItems[i]);
            navBtn.getAccessibleContext().setAccessibleName("NC模块: " + navItems[i]);
            navBtn.getAccessibleContext().setAccessibleDescription("进入" + navItems[i] + "功能模块");
            navBtn.setPreferredSize(new Dimension(120, 30));
            navPanel.add(navBtn);
            navPanel.add(Box.createVerticalStrut(5));
        }

        leftPanel.add(navPanel, BorderLayout.NORTH);

        // Center panel with complex nested structure (模拟NC的工作区)
        JPanel centerPanel = new JPanel(new BorderLayout());
        centerPanel.setName("NC工作区域");
        centerPanel.getAccessibleContext().setAccessibleName("NC系统工作区");
        centerPanel.setBorder(BorderFactory.createTitledBorder("工作区"));

        // 创建表格模拟NC的数据列表
        String[] columnNames = { "编码", "名称", "类型", "状态", "创建时间" };
        Object[][] data = {
                { "EMP001", "张三", "正式员工", "在职", "2024-01-15" },
                { "EMP002", "李四", "合同工", "在职", "2024-02-20" },
                { "EMP003", "王五", "正式员工", "离职", "2024-03-10" },
                { "EMP004", "赵六", "实习生", "在职", "2024-04-05" }
        };

        JTable dataTable = new JTable(data, columnNames);
        dataTable.setName("NC数据表格");
        dataTable.getAccessibleContext().setAccessibleName("员工信息数据表");
        dataTable.getAccessibleContext().setAccessibleDescription("显示员工基本信息的数据表格");

        JScrollPane scrollPane = new JScrollPane(dataTable);
        scrollPane.setName("数据表格滚动面板");
        scrollPane.getAccessibleContext().setAccessibleName("数据表格滚动视图");

        centerPanel.add(scrollPane, BorderLayout.CENTER);

        // Right panel with operation buttons (模拟NC的操作按钮区)
        JPanel rightPanel = new JPanel();
        rightPanel.setLayout(new BoxLayout(rightPanel, BoxLayout.Y_AXIS));
        rightPanel.setName("NC操作区域");
        rightPanel.getAccessibleContext().setAccessibleName("NC操作按钮区");
        rightPanel.setBorder(BorderFactory.createTitledBorder("操作"));

        String[] operations = { "新增", "修改", "删除", "查询", "审核", "弃审" };
        for (int i = 0; i < operations.length; i++) {
            JButton opBtn = new JButton(operations[i]);
            opBtn.setName("操作按钮_" + operations[i]);
            opBtn.getAccessibleContext().setAccessibleName("NC操作: " + operations[i]);
            opBtn.getAccessibleContext().setAccessibleDescription("执行" + operations[i] + "操作");
            opBtn.setPreferredSize(new Dimension(80, 25));
            rightPanel.add(opBtn);
            rightPanel.add(Box.createVerticalStrut(5));
        }

        // Bottom panel with status and action buttons (模拟NC的状态栏)
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.setName("NC状态栏");
        bottomPanel.getAccessibleContext().setAccessibleName("NC系统状态栏");

        JLabel statusLabel = new JLabel("就绪 - 已连接到数据库 [*************:1433]");
        statusLabel.setName("系统状态标签");
        statusLabel.getAccessibleContext().setAccessibleName("系统运行状态");
        statusLabel.setBorder(BorderFactory.createLoweredBevelBorder());

        JPanel buttonPanel = new JPanel(new FlowLayout());

        JButton saveButton = new JButton("保存");
        saveButton.setName("保存按钮");
        saveButton.getAccessibleContext().setAccessibleName("保存当前数据");
        saveButton.getAccessibleContext().setAccessibleDescription("保存当前录入或修改的数据");

        JButton cancelButton = new JButton("取消");
        cancelButton.setName("取消按钮");
        cancelButton.getAccessibleContext().setAccessibleName("取消当前操作");

        JButton exitButton = new JButton("退出系统");
        exitButton.setName("退出按钮");
        exitButton.getAccessibleContext().setAccessibleName("退出NC系统");
        exitButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                int result = JOptionPane.showConfirmDialog(frame,
                        "确定要退出NC系统吗？", "退出确认",
                        JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
                if (result == JOptionPane.YES_OPTION) {
                    System.exit(0);
                }
            }
        });

        JButton testAccessButton = new JButton("测试访问性");
        testAccessButton.setName("测试按钮");
        testAccessButton.getAccessibleContext().setAccessibleName("测试Java Access Bridge");
        testAccessButton.addActionListener(new ActionListener() {
            public void actionPerformed(ActionEvent e) {
                showAccessibilityTestDialog(frame, mainPanel);
            }
        });

        buttonPanel.add(saveButton);
        buttonPanel.add(cancelButton);
        buttonPanel.add(testAccessButton);
        buttonPanel.add(exitButton);

        bottomPanel.add(statusLabel, BorderLayout.CENTER);
        bottomPanel.add(buttonPanel, BorderLayout.EAST);

        // Assemble the main panel
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(leftPanel, BorderLayout.WEST);
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        mainPanel.add(rightPanel, BorderLayout.EAST);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);

        frame.add(mainPanel);
        frame.setVisible(true);

        // 输出窗口信息供调试
        System.out.println("NC测试程序已启动");
        System.out.println("窗口标题: " + frame.getTitle());
        System.out.println("窗口句柄 (HWND): " + getWindowHandle(frame));
        System.out.println("主面板组件数量: " + countComponents(mainPanel));

        // Print accessibility information
        printAccessibilityInfo(mainPanel);
    }

    private static void showAccessibilityTestDialog(JFrame parent, Container mainPanel) {
        StringBuilder info = new StringBuilder();
        info.append("=== NC系统访问性测试结果 ===\n\n");
        info.append("Java版本: ").append(System.getProperty("java.version")).append("\n");
        info.append("操作系统: ").append(System.getProperty("os.name")).append("\n");
        info.append("访问性技术: ").append(System.getProperty("javax.accessibility.assistive_technologies")).append("\n\n");
        info.append("主面板组件总数: ").append(countComponents(mainPanel)).append("\n");
        info.append("窗口标题: ").append(parent.getTitle()).append("\n");
        info.append("程序运行时间: ").append(System.currentTimeMillis()).append(" ms\n\n");
        info.append("此窗口应该可以被Java Access Bridge工具检测到，\n");
        info.append("包括JavaFerret、JavaMonkey和其他访问性工具。");

        JTextArea textArea = new JTextArea(info.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(500, 400));

        JOptionPane.showMessageDialog(parent, scrollPane,
                "NC系统访问性测试", JOptionPane.INFORMATION_MESSAGE);
    }

    private static String getWindowHandle(Window window) {
        try {
            // 尝试获取窗口句柄 (仅用于调试显示)
            return "0x" + Integer.toHexString(window.hashCode()).toUpperCase();
        } catch (Exception e) {
            return "无法获取";
        }
    }

    private static int countComponents(Container container) {
        int count = container.getComponentCount();
        for (Component comp : container.getComponents()) {
            if (comp instanceof Container) {
                count += countComponents((Container) comp);
            }
        }
        return count;
    }

    private static void printAccessibilityInfo(Container container) {
        System.out.println("\n=== NC系统组件层次结构 ===");
        printComponentInfo(container, 0);
        System.out.println("=== 层次结构输出完成 ===\n");
    }

    private static void printComponentInfo(Component comp, int level) {
        StringBuilder indentBuilder = new StringBuilder();
        for (int i = 0; i < level; i++) {
            indentBuilder.append("  ");
        }
        String indent = indentBuilder.toString();
        String name = comp.getName() != null ? comp.getName() : "unnamed";
        String className = comp.getClass().getSimpleName();
        String accessibleName = comp.getAccessibleContext().getAccessibleName();

        System.out.println(indent + className + " [" + name + "]" +
                (accessibleName != null ? " - " + accessibleName : ""));

        if (comp instanceof Container) {
            Container container = (Container) comp;
            for (Component child : container.getComponents()) {
                printComponentInfo(child, level + 1);
            }
        }
    }
}