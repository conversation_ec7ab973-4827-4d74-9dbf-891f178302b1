/**
 * NCTraverserStandard.cs - 严格按照Java Access Bridge API标准实现
 * 基于官方API文档：AccessBridgeCalls.h, AccessBridgePackages.h, AccessBridgeCallbacks.h
 * 参考JavaFerret.cpp的成功实现方式
 */

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace NCTraverser
{
    class Program
    {
        private static string logFileName;
        private static bool accessBridgeInitialized = false;

        // P/Invoke声明 - 尝试不同的调用约定
        [DllImport("WindowsAccessBridge-64.dll")]
        static extern bool initializeAccessBridge();

        [DllImport("WindowsAccessBridge-64.dll")]
        static extern bool shutdownAccessBridge();

        [DllImport("WindowsAccessBridge-64.dll")]
        static extern bool IsJavaWindow(IntPtr window);

        [DllImport("WindowsAccessBridge-64.dll")]
        static extern bool GetAccessibleContextFromHWND(IntPtr window, out long vmID, out IntPtr ac);

        [DllImport("WindowsAccessBridge-64.dll")]
        static extern bool GetAccessibleContextInfo(long vmID, IntPtr ac, out AccessibleContextInfo info);

        [DllImport("WindowsAccessBridge-64.dll")]
        static extern IntPtr GetAccessibleChildFromContext(long vmID, IntPtr ac, int index);

        [DllImport("WindowsAccessBridge-64.dll")]
        static extern void ReleaseJavaObject(long vmID, IntPtr javaObject);

        // Win32 API
        [DllImport("user32.dll")]
        static extern bool EnumWindows(EnumWindowsProc lpEnumFunc, IntPtr lParam);

        [DllImport("user32.dll")]
        static extern bool IsWindowVisible(IntPtr hWnd);

        [DllImport("user32.dll", CharSet = CharSet.Auto)]
        static extern int GetWindowText(IntPtr hWnd, StringBuilder lpString, int nMaxCount);

        delegate bool EnumWindowsProc(IntPtr hWnd, IntPtr lParam);

        // 简化的AccessibleContextInfo结构体 - 按照API文档
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        public struct AccessibleContextInfo
        {
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1024)]
            public string name;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 1024)]
            public string description;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string role;
            [MarshalAs(UnmanagedType.ByValTStr, SizeConst = 256)]
            public string states;
            public int indexInParent;
            public int childrenCount;
            public int x;
            public int y;
            public int width;
            public int height;
            public bool accessibleComponent;
            public bool accessibleAction;
            public bool accessibleSelection;
            public bool accessibleText;
            public bool accessibleInterfaces;
        }

        static void Main(string[] args)
        {
            logFileName = string.Format("NC_Elements_Standard_{0}.log", DateTime.Now.ToString("yyyyMMdd_HHmmss"));
            
            Console.WriteLine("=== NC标准API遍历工具 ===");
            Console.WriteLine("严格按照Java Access Bridge API标准实现");
            Console.WriteLine("参考JavaFerret.cpp的成功方式");
            Console.WriteLine("输出日志文件: " + logFileName);
            
            WriteLog("=== NC标准API遍历工具启动 ===");
            WriteLog("启动时间: " + DateTime.Now.ToString("yyyy/M/d H:mm:ss"));
            WriteLog("基于官方API: AccessBridgeCalls.h, AccessBridgePackages.h, AccessBridgeCallbacks.h");

            try
            {
                // 步骤1: 按API文档要求初始化Access Bridge
                WriteLog("\n=== 1. 初始化Access Bridge (标准方式) ===");
                if (!InitializeAccessBridgeStandard())
                {
                    WriteLog("❌ Access Bridge初始化失败");
                    return;
                }

                // 步骤2: 枚举所有窗口并检查Java窗口 
                WriteLog("\n=== 2. 枚举Java窗口 (JavaFerret方式) ===");
                var javaWindows = EnumerateJavaWindows();

                // 步骤3: 遍历每个Java窗口的可访问组件
                WriteLog("\n=== 3. 遍历Java组件 ===");
                int totalElements = 0;
                foreach (var windowInfo in javaWindows)
                {
                    WriteLog(string.Format("\n--- 处理窗口: {0} (HWND: {1}) ---", windowInfo.Title, windowInfo.Hwnd.ToString("X")));
                    int elementCount = TraverseJavaWindow(windowInfo);
                    totalElements += elementCount;
                    WriteLog(string.Format("窗口遍历完成，发现 {0} 个元素", elementCount));
                }

                WriteLog(string.Format("\n=== 遍历总结 ==="));
                WriteLog(string.Format("发现Java窗口: {0} 个", javaWindows.Count));
                WriteLog(string.Format("发现可访问元素: {0} 个", totalElements));

                if (totalElements > 0)
                {
                    WriteLog("✅ 成功！这证明标准API方式可以正常工作");
                }
                else
                {
                    WriteLog("❌ 仍未发现元素，需要进一步诊断");
                }
            }
            catch (Exception ex)
            {
                WriteLog("发生异常: " + ex.ToString());
                Console.WriteLine("发生异常: " + ex.Message);
            }
            finally
            {
                // 步骤4: 按API文档要求关闭Access Bridge
                WriteLog("\n=== 4. 关闭Access Bridge ===");
                ShutdownAccessBridgeStandard();
            }

            Console.WriteLine("运行完成，详细信息请查看日志文件: " + logFileName);
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        static bool InitializeAccessBridgeStandard()
        {
            try
            {
                WriteLog("调用 initializeAccessBridge() - 这是API文档要求的第一步");
                bool result = initializeAccessBridge();
                WriteLog(string.Format("initializeAccessBridge() 返回: {0}", result));

                if (result)
                {
                    accessBridgeInitialized = true;
                    WriteLog("✅ Access Bridge初始化成功 (标准API方式)");
                    
                    // 等待一下让Access Bridge完全初始化
                    System.Threading.Thread.Sleep(1000);
                    WriteLog("等待Access Bridge完全初始化...");
                }
                else
                {
                    WriteLog("❌ initializeAccessBridge() 返回false");
                }

                return result;
            }
            catch (Exception ex)
            {
                WriteLog("初始化Access Bridge时发生异常: " + ex.ToString());
                return false;
            }
        }

        static void ShutdownAccessBridgeStandard()
        {
            if (accessBridgeInitialized)
            {
                try
                {
                    WriteLog("调用 shutdownAccessBridge() - API文档要求的清理步骤");
                    bool result = shutdownAccessBridge();
                    WriteLog(string.Format("shutdownAccessBridge() 返回: {0}", result));
                    accessBridgeInitialized = false;
                }
                catch (Exception ex)
                {
                    WriteLog("关闭Access Bridge时发生异常: " + ex.Message);
                }
            }
        }

        static List<JavaWindowInfo> EnumerateJavaWindows()
        {
            var javaWindows = new List<JavaWindowInfo>();

            WriteLog("开始枚举所有窗口...");
            
            EnumWindows((hWnd, lParam) =>
            {
                if (IsWindowVisible(hWnd))
                {
                    // 获取窗口标题
                    var sb = new StringBuilder(256);
                    GetWindowText(hWnd, sb, sb.Capacity);
                    string windowTitle = sb.ToString();

                    try
                    {
                        // 使用标准API检查是否为Java窗口
                        bool isJava = IsJavaWindow(hWnd);
                        WriteLog(string.Format("窗口检查: {0} (HWND: {1}) -> Java: {2}", 
                            windowTitle, hWnd.ToString("X"), isJava));

                        if (isJava)
                        {
                            WriteLog(string.Format("✅ 发现Java窗口: {0}", windowTitle));
                            javaWindows.Add(new JavaWindowInfo
                            {
                                Hwnd = hWnd,
                                Title = windowTitle
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        WriteLog(string.Format("检查窗口时异常: {0} -> {1}", windowTitle, ex.Message));
                    }
                }
                return true;
            }, IntPtr.Zero);

            WriteLog(string.Format("窗口枚举完成，发现 {0} 个Java窗口", javaWindows.Count));
            return javaWindows;
        }

        static int TraverseJavaWindow(JavaWindowInfo windowInfo)
        {
            int elementCount = 0;

            try
            {
                // 使用标准API获取窗口的可访问上下文
                long vmID;
                IntPtr rootContext;
                bool success = GetAccessibleContextFromHWND(windowInfo.Hwnd, out vmID, out rootContext);
                
                WriteLog(string.Format("GetAccessibleContextFromHWND 返回: {0}", success));
                WriteLog(string.Format("VMID: {0}, RootContext: {1}", vmID, rootContext.ToString("X")));

                if (success && rootContext != IntPtr.Zero)
                {
                    WriteLog("✅ 成功获取根可访问上下文");
                    
                    // 遍历根上下文及其子元素
                    elementCount = TraverseAccessibleContext(vmID, rootContext, 0, "ROOT");
                    
                    // 按API文档要求释放Java对象
                    WriteLog("释放根上下文对象...");
                    ReleaseJavaObject(vmID, rootContext);
                }
                else
                {
                    WriteLog("❌ 无法获取可访问上下文");
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("遍历窗口时异常: {0}", ex.Message));
            }

            return elementCount;
        }

        static int TraverseAccessibleContext(long vmID, IntPtr context, int depth, string path)
        {
            int elementCount = 0;

            if (context == IntPtr.Zero)
            {
                WriteLog(string.Format("{0}上下文为空", GetIndent(depth)));
                return 0;
            }

            try
            {
                // 获取上下文信息
                AccessibleContextInfo info;
                bool success = GetAccessibleContextInfo(vmID, context, out info);
                
                if (success)
                {
                    elementCount = 1; // 当前元素
                    string elementInfo = string.Format("{0}[{1}] {2} ({3})", 
                        GetIndent(depth), path, info.name ?? "无名称", info.role ?? "未知角色");
                    
                    WriteLog(elementInfo);
                    
                    // 记录详细信息
                    if (!string.IsNullOrEmpty(info.description))
                        WriteLog(string.Format("{0}  描述: {1}", GetIndent(depth), info.description));
                    
                    if (!string.IsNullOrEmpty(info.states))
                        WriteLog(string.Format("{0}  状态: {1}", GetIndent(depth), info.states));
                    
                    WriteLog(string.Format("{0}  位置: ({1}, {2}), 大小: {3}x{4}", 
                        GetIndent(depth), info.x, info.y, info.width, info.height));
                    WriteLog(string.Format("{0}  子元素数量: {1}", GetIndent(depth), info.childrenCount));

                    // 遍历子元素（限制深度防止无限递归）
                    if (depth < 8 && info.childrenCount > 0)
                    {
                        int maxChildren = Math.Min(info.childrenCount, 20); // 限制子元素数量
                        WriteLog(string.Format("{0}  开始遍历 {1} 个子元素...", GetIndent(depth), maxChildren));

                        for (int i = 0; i < maxChildren; i++)
                        {
                            try
                            {
                                // 使用标准API获取子元素
                                IntPtr childContext = GetAccessibleChildFromContext(vmID, context, i);
                                if (childContext != IntPtr.Zero)
                                {
                                    string childPath = string.Format("{0}[{1}]", path, i);
                                    int childElements = TraverseAccessibleContext(vmID, childContext, depth + 1, childPath);
                                    elementCount += childElements;
                                    
                                    // 按API文档要求释放子对象
                                    ReleaseJavaObject(vmID, childContext);
                                }
                                else
                                {
                                    WriteLog(string.Format("{0}  子元素{1}: null", GetIndent(depth), i));
                                }
                            }
                            catch (Exception ex)
                            {
                                WriteLog(string.Format("{0}  获取子元素{1}时异常: {2}", GetIndent(depth), i, ex.Message));
                            }
                        }
                    }
                }
                else
                {
                    WriteLog(string.Format("{0}获取上下文信息失败", GetIndent(depth)));
                }
            }
            catch (Exception ex)
            {
                WriteLog(string.Format("{0}遍历上下文时异常: {1}", GetIndent(depth), ex.Message));
            }

            return elementCount;
        }

        static string GetIndent(int depth)
        {
            return new string(' ', depth * 2);
        }

        static void WriteLog(string message)
        {
            string logEntry = string.Format("[{0}] {1}", DateTime.Now.ToString("HH:mm:ss.fff"), message);
            Console.WriteLine(message);
            
            try
            {
                File.AppendAllText(logFileName, logEntry + Environment.NewLine, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                Console.WriteLine("写入日志失败: " + ex.Message);
            }
        }

        public class JavaWindowInfo
        {
            public IntPtr Hwnd { get; set; }
            public string Title { get; set; }
        }
    }
} 